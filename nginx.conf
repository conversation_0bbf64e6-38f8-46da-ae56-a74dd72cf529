# ZeroDuty前端应用Nginx配置
# 支持多后端服务代理的生产环境配置

# 上游服务器配置
upstream oncall_backend {
    # OnCall服务 - 值班管理 (端口2002)
    server localhost:2002 max_fails=3 fail_timeout=30s;
    # 如果有多个实例，可以添加更多服务器
    # server localhost:2002 weight=1;
    # server ********:2002 weight=1 backup;
}

upstream system_backend {
    # System服务 - 系统管理 (端口2001)
    server localhost:2001 max_fails=3 fail_timeout=30s;
    # server ********:2001 weight=1 backup;
}

upstream message_backend {
    # Message服务 - 消息通知 (端口2003)
    server localhost:2003 max_fails=3 fail_timeout=30s;
    # server ********:2003 weight=1 backup;
}

# 日志格式定义
log_format api_access '$remote_addr - $remote_user [$time_local] '
                     '"$request" $status $body_bytes_sent '
                     '"$http_referer" "$http_user_agent" '
                     'upstream: $upstream_addr '
                     'response_time: $upstream_response_time '
                     'request_time: $request_time';

# 主服务器配置
server {
    listen 80;
    server_name zeroduty.example.com;  # 替换为实际域名
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name zeroduty.example.com;  # 替换为实际域名
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/zeroduty.crt;
    ssl_certificate_key /etc/ssl/private/zeroduty.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头设置
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 访问日志
    access_log /var/log/nginx/zeroduty_access.log api_access;
    error_log /var/log/nginx/zeroduty_error.log warn;
    
    # 根目录配置
    root /usr/share/nginx/html/zeroduty;
    index index.html;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/xml+rss 
               application/json application/xml;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }
    
    # OnCall服务API代理
    location /oncall-api/ {
        # 移除路径前缀
        rewrite ^/oncall-api/(.*)$ /$1 break;
        
        proxy_pass http://oncall_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Service-Name "oncall";
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 30s;
        
        # 健康检查
        proxy_set_header Connection "";
        proxy_http_version 1.1;
    }
    
    # System服务API代理
    location /system-api/ {
        rewrite ^/system-api/(.*)$ /$1 break;
        
        proxy_pass http://system_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Service-Name "system";
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 30s;
        
        proxy_set_header Connection "";
        proxy_http_version 1.1;
    }
    
    # Message服务API代理
    location /message-api/ {
        rewrite ^/message-api/(.*)$ /$1 break;
        
        proxy_pass http://message_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Service-Name "message";
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 30s;
        
        proxy_set_header Connection "";
        proxy_http_version 1.1;
    }
    
    # 权限系统代理 (保持现有配置)
    location /tech/ {
        proxy_pass https://tech.seasungame.com/;
        proxy_set_header Host tech.seasungame.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_ssl_verify off;
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # API健康检查
    location /api/health {
        access_log off;
        
        # 检查所有后端服务
        proxy_pass http://oncall_backend/health;
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        
        # 如果OnCall服务不可用，返回503
        error_page 502 503 504 = @health_check_failed;
    }
    
    # 健康检查失败处理
    location @health_check_failed {
        return 503 "Service Unavailable\n";
        add_header Content-Type text/plain;
    }
    
    # SPA路由支持 - 必须放在最后
    location / {
        try_files $uri $uri/ /index.html;
        
        # 防止缓存index.html
        location = /index.html {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# 监控和状态页面 (可选)
server {
    listen 8080;
    server_name localhost;
    
    # 仅允许内网访问
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;
    deny all;
    
    # Nginx状态页面
    location /nginx_status {
        stub_status on;
        access_log off;
    }
    
    # 上游服务器状态
    location /upstream_status {
        # 需要nginx-module-upstream-check模块
        # check_status;
        return 200 "Upstream status check not configured\n";
        add_header Content-Type text/plain;
    }
}
