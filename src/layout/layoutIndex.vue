<template>
    <div>
        <div v-if="'vertical' === layoutMode">
            <!--左右布局-->
            <div :class="classObj" class="app-wrapper">
                <Sidebar class="sidebar-container" />
                <div class="main-container">
                    <div>
                        <nav-bar />
                    </div>
                    <app-main />
                </div>
            </div>
        </div>
        <div v-else-if="'horizontal' === layoutMode">
            <div  class="app-wrapper">
                <div>
                </div>
                <app-main />
            </div>
        </div>
        <div v-else>
            <app-main />
        </div>
    </div>
</template>
<script>
  import { mapState } from 'pinia'
  import { useAppStore } from '@/stores'
  import settings from "./../settings";
  import { NavBar, AppMain, Sidebar } from "./components";
  export default {
    name: "Layout",
    data() {
      return {
        layoutMode: "",
      };
    },
    components: {
      NavBar,
      AppMain,
       Sidebar
    },
    computed: {
      ...mapState(useAppStore, ['sidebar']),
      classObj() {
        return {
          hideSidebar: !this.sidebar.opened,
          openSidebar: this.sidebar.opened,
          withoutAnimation: this.sidebar.withoutAnimation
        };
      },
    },
    methods: {
    },
    created() {
      /**
       * 子应用之间的布局模式
       * 1. 如果是微应用,默认用有侧边栏模式,如果主应用参数为无侧边栏模式,则子应用也为无侧边栏模式
      **/
     //默认
     let layoutMode = null;
      // 非微应用环境,直接使用layout
      if ( !window.__MICRO_APP_ENVIRONMENT__ ) {
        layoutMode = settings.layoutMode;
      } else {
        // 主应用控制侧边栏支持
        const microAppData = window.microApp?.getData();
        //判断非空
        if (microAppData && 'layoutMode' in microAppData) {
          //标准模式显示子应用侧边栏,opsany布局样式
          if (microAppData.layoutMode === "standard"){
            layoutMode = settings.layoutMode;
        }else if (microAppData.layoutMode === "tc"){
          layoutMode = null;
        }
      }
    }
    this.layoutMode = layoutMode;
  }
}
</script>

<style lang="scss" scoped>
    .app-wrapper {
        @include clearfix;
        position: relative;
        height: 100%;
        width: 100%;
    }

    .drawer-bg {
        background: #000;
        opacity: 0.3;
        width: 100%;
        top: 0;
        height: 100%;
        position: absolute;
        z-index: 999;
    }

    .fixed-header {
        position: fixed;
        top: 0;
        right: 0;
        z-index: 9;
        width: calc(100% - #{$sideBarWidth});
        transition: width 0.28s;
    }

    .hideSidebar .fixed-header {
        width: calc(100% - 54px);
    }

</style>
