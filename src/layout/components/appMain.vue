<template>
    <section class="app-main">
        <router-view v-slot="{ Component }" :key="key">
            <transition name="fade-transform" mode="out-in">
                <component :is="Component" />
            </transition>
        </router-view>
    </section>
</template>
<script>
  export default {
    name: "AppMain",
    computed: {
      key() {
        return this.$route.path;
      },
    },
  };
</script>

<style scoped>
    .app-main {
        min-height: calc(100vh - 55px);
        width: 100%;
        position: relative;
        overflow: hidden;
    }
    .fixed-header + .app-main {
        padding-top: 50px;
    }
</style>

<style lang="scss">
    .el-popup-parent--hidden {
        .fixed-header {
            padding-right: 15px;
        }
    }
</style>
