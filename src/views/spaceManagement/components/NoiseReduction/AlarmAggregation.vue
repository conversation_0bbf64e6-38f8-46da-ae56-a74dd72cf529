<template>
  <div class="alarm-aggregation-container">
    <div class="section-header">
      <div class="section-title">
        <span class="title-icon">
          <el-icon><InfoFilled /></el-icon>
        </span>
        告警聚合
        <el-tooltip
          content="告警聚合功能可以将相似的告警归类，减少重复告警"
          placement="top"
          effect="light"
        >
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
    </div>

    <div class="section-content">
      <div class="aggregation-options">
        <el-radio-group v-model="aggregationType">
          <el-radio :label="2">规则聚合</el-radio>
          <el-radio :label="3">不聚合</el-radio>
        </el-radio-group>
      </div>

      <div v-if="aggregationType === 2" class="aggregation-description">
        <div class="description-item">
          <div class="description-icon">
            <el-icon class="circle-icon"><CircleCheck /></el-icon>
          </div>
          <div class="description-content">
            <div class="description-title">模式说明</div>
            <div class="description-text">
              固定义告警聚合维度，仅当新触发告警与活跃故障在所有配置维度的信息完全相同时，将告警合入故障。
            </div>
          </div>
        </div>
      </div>

      <div v-if="aggregationType === 3" class="aggregation-description">
        <div class="description-item">
          <div class="description-icon">
            <el-icon class="circle-icon"><CircleCheck /></el-icon>
          </div>
          <div class="description-content">
            <div class="description-title">模式说明</div>
            <div class="description-text">
              不进行聚合，每一个告警都会生成一个新故障。
            </div>
          </div>
        </div>
      </div>

      <div class="aggregation-settings" v-if="aggregationType === 2">
        <div class="setting-item">
          <div class="setting-label">聚合窗口</div>
          <el-switch v-model="aggregationWindow.enabled" />
          <div class="window-settings" v-if="aggregationWindow.enabled">
            <span class="setting-text">聚合</span>
            <el-input-number
              v-model="aggregationWindow.minutes"
              :min="1"
              :max="1440"
              controls-position="right"
              class="time-input"
            />
            <span class="setting-text">分钟内发生的告警，超出时间范围后告警将被聚合到新的故障。</span>
          </div>
          <div class="setting-description" v-if="!aggregationWindow.enabled">
            未开启，将持续合入新告警。直到故障被关闭
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">风暴提醒</div>
          <el-switch v-model="stormReminder.enabled" />
          <div class="storm-settings" v-if="stormReminder.enabled">
            <div class="storm-row">
              <span class="setting-text">故障触发后立即进行分流通知，并持续聚合告警：</span>
            </div>
            <div class="storm-row">
              <span class="setting-text">聚合告警达到</span>
              <el-input-number
                v-model="stormReminder.threshold"
                :min="1"
                :max="1000"
                controls-position="right"
                class="count-input"
              />
              <span class="setting-text">条时，单独触发一次告警风暴提醒。</span>
            </div>
          </div>
          <div class="setting-description" v-if="!stormReminder.enabled">
            未开启，不进行风暴提醒
          </div>
        </div>

        <div v-if="aggregationType === 2" class="aggregation-rules">
          <div class="aggregation-dimensions">
            <div class="dimension-title">聚合维度</div>
            <div class="dimension-description">
              支持选择多个属性和标签，仅当所有属性和标签完全相同时，该组条件匹配
            </div>
            <el-input
              v-model="dimensionInput"
              placeholder="labels.check"
              class="dimension-input"
            ></el-input>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">严格聚合</div>
          <el-switch v-model="strictAggregation.enabled" />
          <div class="setting-description" v-if="!strictAggregation.enabled">
            未开启，告警与故障中信息为空的聚合条件将视为相同
          </div>
        </div>

        <div class="tooltip-box" v-if="aggregationType === 2">
          <div class="tooltip-content">
            新告警按条件匹配聚合维度，未匹配则创建新故障。如未设置匹认维度，则不聚合
          </div>
        </div>
      </div>

      <div class="action-buttons">
        <el-button type="primary" @click="saveSettings">保 存</el-button>
        <el-button @click="previewSettings">预 览</el-button>
        <el-button @click="cancelSettings">取 消</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { InfoFilled, QuestionFilled, CircleCheck } from '@element-plus/icons-vue'
import { fetchAlarmAggregationConfig, updateAlarmAggregation } from '@/api/legacy/noiseReduction'

const props = defineProps({
  spaceId: {
    type: [Number, String],
    required: true
  }
})

// 聚合类型：2-规则聚合，3-不聚合
const aggregationType = ref(2)

// 聚合窗口设置
const aggregationWindow = ref({
  enabled: false,
  minutes: 60
})

// 风暴提醒设置
const stormReminder = ref({
  enabled: false,
  threshold: 100
})

// 严格聚合设置
const strictAggregation = ref({
  enabled: false
})

// 维度输入
const dimensionInput = ref('')

// 加载配置
const loadConfig = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const response = await fetchAlarmAggregationConfig(props.spaceId)
    if (response.code === 0 && response.data) {
      const config = response.data
      aggregationType.value = config.aggregation_type

      // 聚合窗口设置
      if (config.aggregation_window) {
        aggregationWindow.value.enabled = config.aggregation_window.enabled
        aggregationWindow.value.minutes = config.aggregation_window.minutes || 60
      }

      // 风暴提醒设置
      if (config.storm_reminder) {
        stormReminder.value.enabled = config.storm_reminder.enabled
        stormReminder.value.threshold = config.storm_reminder.threshold || 100
      }

      strictAggregation.value.enabled = config.strict_aggregation.enabled
      dimensionInput.value = config.dimensions ? config.dimensions.join(',') : ''
    }
  } catch (error) {
    console.error('加载告警聚合配置失败:', error)
    ElMessage.error('加载配置失败，请稍后重试')
  } finally {
    loading.close()
  }
}

// 保存设置
const saveSettings = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '保存中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const dimensions = dimensionInput.value
      ? dimensionInput.value.split(',').map(item => item.trim())
      : []

    const data = {
      aggregation_type: aggregationType.value,
      aggregation_window: {
        enabled: aggregationWindow.value.enabled,
        minutes: aggregationWindow.value.minutes
      },
      storm_reminder: {
        enabled: stormReminder.value.enabled,
        threshold: stormReminder.value.threshold
      },
      strict_aggregation: {
        enabled: strictAggregation.value.enabled
      },
      dimensions
    }

    const response = await updateAlarmAggregation(props.spaceId, data)
    if (response.code === 0 && response.data.success) {
      ElMessage.success('设置已保存')
    } else {
      ElMessage.error('保存失败，请稍后重试')
    }
  } catch (error) {
    console.error('保存告警聚合配置失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    loading.close()
  }
}

// 预览设置
const previewSettings = () => {
  ElMessage.info('预览功能暂未实现')
}

// 取消设置
const cancelSettings = () => {
  loadConfig()
  ElMessage.info('已取消修改')
}

// 组件挂载时加载配置
onMounted(() => {
  loadConfig()
})
</script>

<script>
export default {
  name: 'AlarmAggregation'
}
</script>

<style scoped>
.alarm-aggregation-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.title-icon {
  margin-right: 8px;
  color: #409eff;
}

.help-icon {
  margin-left: 8px;
  color: #909399;
  cursor: pointer;
}

.section-content {
  padding: 20px;
}

.aggregation-options {
  margin-bottom: 16px;
}

.aggregation-description {
  background-color: #f0f2ff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
}

.description-item {
  display: flex;
}

.description-icon {
  margin-right: 12px;
}

.circle-icon {
  color: #6366f1;
  font-size: 20px;
}

.description-content {
  flex: 1;
}

.description-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: #303133;
}

.description-text {
  color: #606266;
  line-height: 1.0;
}

.aggregation-settings {
  margin-top: 20px;
}

.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.setting-label {
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.setting-description {
  margin-left: 16px;
  color: #909399;
  font-size: 13px;
}

.rule-type-selector {
  display: flex;
  margin-bottom: 16px;
}

.rule-type {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 16px;
  color: #606266;
}

.rule-type.active {
  background-color: #ecf5ff;
  color: #409eff;
}

.rule-type .el-icon {
  margin-right: 4px;
}

.aggregation-dimensions {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.dimension-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.dimension-description {
  color: #606266;
  font-size: 13px;
  margin-bottom: 12px;
}

.dimension-input {
  width: 100%;
}

.window-settings, .storm-settings {
  margin-top: 12px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.storm-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.time-input, .count-input {
  width: 80px;
}

.tooltip-box {
  background-color: #303133;
  color: #fff;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  position: relative;
}

.tooltip-box:after {
  content: '';
  position: absolute;
  top: -8px;
  left: 20px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #303133;
}

.tooltip-content {
  font-size: 13px;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  margin-top: 24px;
}

.action-buttons .el-button {
  margin-right: 12px;
}
</style>
