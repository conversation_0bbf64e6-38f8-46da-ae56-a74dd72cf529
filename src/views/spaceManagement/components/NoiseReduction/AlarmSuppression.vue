<template>
  <div class="alarm-suppression-container">
    <div class="section-header">
      <div class="section-title">
        <span class="title-icon">
          <el-icon><InfoFilled /></el-icon>
        </span>
        故障收敛
        <el-tooltip
          content="故障收敛功能可以减少短时间内重复发生的故障通知"
          placement="top"
          effect="light"
        >
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
    </div>

    <div class="section-content">
      <div class="suppression-options">
        <el-radio-group v-model="suppressionEnabled">
          <el-radio :label="true">开启</el-radio>
          <el-radio :label="false">关闭</el-radio>
        </el-radio-group>
      </div>

      <div v-if="suppressionEnabled" class="suppression-settings">
        <div class="setting-row">
          <el-input-number v-model="timeWindow" :min="1" :max="1440" class="time-input" />
          <span class="setting-text">分钟内，同一故障</span>
          <el-icon class="setting-icon"><CircleCheck /></el-icon>
          <span class="setting-text">频繁发生且与恢复达</span>
          <el-input-number v-model="occurrenceCount" :min="1" :max="100" class="count-input" />
          <span class="setting-text">次及以上则进抖动状态，未来</span>
          <el-input-number v-model="suppressionTime" :min="1" :max="1440" class="time-input" />
          <span class="setting-text">分钟内不再发送新的故障通知。</span>
        </div>
      </div>

      <div class="action-buttons">
        <el-button type="primary" @click="saveSettings">保 存</el-button>
        <el-button @click="cancelSettings">取 消</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { InfoFilled, QuestionFilled, CircleCheck } from '@element-plus/icons-vue'
import { fetchAlarmSuppressionConfig, updateAlarmSuppression } from '@/api/legacy/noiseReduction'

const props = defineProps({
  spaceId: {
    type: [Number, String],
    required: true
  }
})

// 是否启用故障收敛
const suppressionEnabled = ref(true)

// 时间窗口（分钟）
const timeWindow = ref(60)

// 发生次数
const occurrenceCount = ref(4)

// 抑制时间（分钟）
const suppressionTime = ref(120)

// 加载配置
const loadConfig = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const response = await fetchAlarmSuppressionConfig(props.spaceId)
    if (response.code === 0 && response.data) {
      const config = response.data
      suppressionEnabled.value = config.enabled
      timeWindow.value = config.time_window
      occurrenceCount.value = config.occurrence_count
      suppressionTime.value = config.suppression_time
    }
  } catch (error) {
    console.error('加载故障收敛配置失败:', error)
    ElMessage.error('加载配置失败，请稍后重试')
  } finally {
    loading.close()
  }
}

// 保存设置
const saveSettings = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '保存中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const data = {
      enabled: suppressionEnabled.value,
      time_window: timeWindow.value,
      occurrence_count: occurrenceCount.value,
      suppression_time: suppressionTime.value
    }

    const response = await updateAlarmSuppression(props.spaceId, data)
    if (response.code === 0 && response.data.success) {
      ElMessage.success('设置已保存')
    } else {
      ElMessage.error('保存失败，请稍后重试')
    }
  } catch (error) {
    console.error('保存故障收敛配置失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    loading.close()
  }
}

// 取消设置
const cancelSettings = () => {
  loadConfig()
  ElMessage.info('已取消修改')
}

// 组件挂载时加载配置
onMounted(() => {
  loadConfig()
})
</script>

<script>
export default {
  name: 'AlarmSuppression'
}
</script>

<style scoped>
.alarm-suppression-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.title-icon {
  margin-right: 8px;
  color: #409eff;
}

.help-icon {
  margin-left: 8px;
  color: #909399;
  cursor: pointer;
}

.section-content {
  padding: 20px;
}

.suppression-options {
  margin-bottom: 20px;
}

.suppression-settings {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
}

.setting-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.setting-text {
  font-size: 14px;
  color: #606266;
}

.setting-icon {
  color: #409eff;
  font-size: 16px;
}

.time-input, .count-input {
  width: 80px;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  margin-top: 24px;
}

.action-buttons .el-button {
  margin-right: 12px;
}
</style>
