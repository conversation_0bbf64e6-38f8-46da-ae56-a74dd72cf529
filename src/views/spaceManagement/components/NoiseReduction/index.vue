<template>
  <div class="noise-reduction-container">
    <alarm-aggregation :space-id="spaceId" />
    <alarm-suppression :space-id="spaceId" />
    <silent-strategy :space-id="spaceId" />
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import AlarmAggregation from './AlarmAggregation.vue'
import AlarmSuppression from './AlarmSuppression.vue'
import SilentStrategy from './SilentStrategy.vue'

const props = defineProps({
  spaceId: {
    type: [Number, String],
    required: true
  }
})
</script>

<script>
export default {
  name: 'NoiseReductionComponent'
}
</script>

<style scoped>
.noise-reduction-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
</style>
