<template>
  <div class="dispatch-strategy-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="strategy-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">
          <el-tag size="small" type="primary" class="section-tag">1</el-tag>
          基本信息
        </div>

        <el-form-item label="策略名称" prop="rule_name">
          <el-input
            v-model="formData.rule_name"
            placeholder="请输入策略名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入策略描述"
            :rows="2"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-input-number
            v-model="formData.priority"
            :min="1"
            :max="100"
            controls-position="right"
          />
          <div class="form-tip">数字越小优先级越高，优先匹配</div>
        </el-form-item>
      </div>

      <!-- 匹配设置 -->
      <div class="form-section">
        <div class="section-title">
          <el-tag size="small" type="primary" class="section-tag">2</el-tag>
          匹配设置
        </div>

        <div class="filter-section">
          <div class="filter-header">
            <span>在</span>
            <el-select v-model="timeFilterType" placeholder="选择时间" style="width: 120px; margin: 0 8px;">
              <el-option label="全天" value="all" />
              <el-option label="工作日" value="workday" />
              <el-option label="自定义" value="custom" />
            </el-select>
            <span>时间段内，匹配以下条件：</span>
          </div>

          <div v-if="timeFilterType === 'custom'" class="time-filter-custom">
            <el-time-picker
              v-model="customTimeRange"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 240px; margin: 8px 0;"
            />
            <el-checkbox-group v-model="customDays" class="day-checkboxes">
              <el-checkbox label="1">周一</el-checkbox>
              <el-checkbox label="2">周二</el-checkbox>
              <el-checkbox label="3">周三</el-checkbox>
              <el-checkbox label="4">周四</el-checkbox>
              <el-checkbox label="5">周五</el-checkbox>
              <el-checkbox label="6">周六</el-checkbox>
              <el-checkbox label="0">周日</el-checkbox>
            </el-checkbox-group>
          </div>

          <div class="conditions-container">
            <div v-for="(group, groupIndex) in formData.filters" :key="'group-' + groupIndex" class="condition-group">
              <div class="group-header">
                <span v-if="groupIndex > 0">或</span>
                <el-button
                  v-if="groupIndex > 0"
                  type="danger"
                  size="small"
                  circle
                  @click="removeConditionGroup(groupIndex)"
                  class="remove-group-btn"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>

              <div
                v-for="(condition, condIndex) in group"
                :key="'cond-' + groupIndex + '-' + condIndex"
                class="condition-item"
              >
                <div class="condition-row">
                  <el-select
                    v-model="condition.key"
                    placeholder="选择字段"
                    class="condition-field"
                  >
                    <el-option label="告警级别" value="severity" />
                    <el-option label="标题" value="title" />
                    <el-option label="描述" value="description" />
                    <el-option label="告警名称" value="alertname" />
                    <el-option label="实例" value="instance" />
                    <el-option label="服务" value="service" />
                  </el-select>

                  <el-select
                    v-model="condition.oper"
                    placeholder="操作符"
                    class="condition-operator"
                  >
                    <el-option label="包含" value="IN" />
                    <el-option label="不包含" value="NOT_IN" />
                    <el-option label="等于" value="EQ" />
                    <el-option label="不等于" value="NEQ" />
                  </el-select>

                  <el-select
                    v-model="condition.vals"
                    placeholder="值"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    class="condition-value"
                  >
                    <el-option
                      v-for="option in getValueOptions(condition.key)"
                      :key="option"
                      :label="option"
                      :value="option"
                    />
                  </el-select>

                  <div class="condition-actions">
                    <el-button
                      v-if="condIndex > 0"
                      type="danger"
                      size="small"
                      circle
                      @click="removeCondition(groupIndex, condIndex)"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                    <el-button
                      type="primary"
                      size="small"
                      circle
                      @click="addCondition(groupIndex)"
                    >
                      <el-icon><Plus /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <div class="add-group">
              <el-button type="primary" plain @click="addConditionGroup">
                <el-icon><Plus /></el-icon>添加或条件组
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分派设置 -->
      <div class="form-section">
        <div class="section-title">
          <el-tag size="small" type="primary" class="section-tag">3</el-tag>
          分派设置
        </div>

        <div class="layer-section" v-for="(layer, layerIndex) in formData.layers" :key="'layer-' + layerIndex">
          <div class="layer-header">
            <span class="layer-title">分派层级 {{ layerIndex + 1 }}</span>
            <div class="layer-actions" v-if="formData.layers.length > 1">
              <el-button
                type="danger"
                size="small"
                @click="removeLayer(layerIndex)"
              >
                删除层级
              </el-button>
            </div>
          </div>

          <el-form-item label="最大通知次数">
            <el-input-number
              v-model="layer.max_times"
              :min="1"
              :max="10"
              controls-position="right"
            />
          </el-form-item>

          <el-form-item label="通知间隔(分钟)">
            <el-input-number
              v-model="layer.escalate_window"
              :min="1"
              :max="60"
              controls-position="right"
            />
          </el-form-item>

          <!-- 强制升级选项已移除 -->

          <el-form-item label="通知对象">
            <div class="target-section">
              <div class="target-row">
                <span class="target-label">团队:</span>
                <el-select
                  v-model="layer.target.team_ids"
                  multiple
                  placeholder="选择团队"
                  class="target-select"
                >
                  <el-option
                    v-for="team in teamOptions"
                    :key="team.id"
                    :label="team.name"
                    :value="team.id"
                  />
                </el-select>
              </div>

              <div class="target-row">
                <span class="target-label">人员:</span>
                <el-select
                  v-model="layer.target.person_ids"
                  multiple
                  filterable
                  placeholder="选择人员"
                  class="target-select"
                >
                  <el-option
                    v-for="person in personOptions"
                    :key="person.id"
                    :label="person.name"
                    :value="person.id"
                  />
                </el-select>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="通知方式">
            <div class="notify-method">
              <el-checkbox v-model="layer.target.by.follow_preference">
                遵循个人通知偏好设置
              </el-checkbox>

              <template v-if="!layer.target.by.follow_preference">
                <div class="severity-notify">
                  <div class="severity-row">
                    <span class="severity-label">严重:</span>
                    <el-checkbox-group v-model="layer.target.by.critical">
                      <el-checkbox label="email">邮件</el-checkbox>
                      <el-checkbox label="sms">短信</el-checkbox>
                      <el-checkbox label="call">电话</el-checkbox>
                    </el-checkbox-group>
                  </div>

                  <div class="severity-row">
                    <span class="severity-label">警告:</span>
                    <el-checkbox-group v-model="layer.target.by.warning">
                      <el-checkbox label="email">邮件</el-checkbox>
                      <el-checkbox label="sms">短信</el-checkbox>
                      <el-checkbox label="call">电话</el-checkbox>
                    </el-checkbox-group>
                  </div>

                  <div class="severity-row">
                    <span class="severity-label">信息:</span>
                    <el-checkbox-group v-model="layer.target.by.info">
                      <el-checkbox label="email">邮件</el-checkbox>
                      <el-checkbox label="sms">短信</el-checkbox>
                      <el-checkbox label="call">电话</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </template>
            </div>
          </el-form-item>

          <el-form-item label="通知模板">
            <el-select
              v-model="layer.template_id"
              placeholder="选择通知模板"
              class="w-100"
            >
              <el-option
                v-for="template in templateOptions"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="以群聊渠道通知">
            <el-switch v-model="layer.notify_by_group_chat" />
          </el-form-item>

          <el-form-item label="将通知发送至" v-if="layer.notify_by_group_chat">
            <el-select
              v-model="layer.group_chat_channels"
              multiple
              filterable
              allow-create
              placeholder="添加渠道"
              class="w-100"
            >
              <el-option
                v-for="channel in groupChatChannels"
                :key="channel.id"
                :label="channel.name"
                :value="channel.id"
              />
            </el-select>
            <div class="form-tip error-tip" v-if="!layer.group_chat_channels || layer.group_chat_channels.length === 0">
              请至少添加一个群聊渠道
            </div>
          </el-form-item>

          <el-form-item label="循环通知设置">
            <el-switch v-model="layer.enable_cycle_notify" />
          </el-form-item>

          <div class="cycle-notify-settings" v-if="layer.enable_cycle_notify">
            <div class="cycle-notify-row">
              <span class="setting-text">每隔</span>
              <el-input-number
                v-model="layer.cycle_notify_interval"
                :min="1"
                :max="120"
                controls-position="right"
                class="timeout-input"
              />
              <span class="setting-text">分钟通知</span>
              <el-input-number
                v-model="layer.cycle_notify_times"
                :min="1"
                :max="10"
                controls-position="right"
                class="times-input"
              />
              <span class="setting-text">次，最多循环通知</span>
              <el-input-number
                v-model="layer.cycle_notify_max_loops"
                :min="1"
                :max="10"
                controls-position="right"
                class="times-input"
              />
              <span class="setting-text">次。</span>
            </div>
          </div>

          <el-form-item label="升级设置">
            <div class="escalation-settings">
              <div class="escalation-row">
                <span class="setting-text">超过</span>
                <el-input-number
                  v-model="layer.cycle_notify_timeout"
                  :min="1"
                  :max="120"
                  controls-position="right"
                  class="timeout-input"
                />
                <span class="setting-text">分钟后如果故障仍</span>
                <el-select v-model="layer.cycle_notify_condition" class="condition-select">
                  <el-option label="未关闭" value="not_closed" />
                  <el-option label="未关闭且未认领" value="not_closed_not_claimed" />
                </el-select>
                <span class="setting-text">，则升级到下一环节。</span>
              </div>
            </div>
          </el-form-item>
        </div>

        <div class="add-layer">
          <el-button type="primary" plain @click="addLayer">
            <el-icon><Plus /></el-icon>添加分派层级
          </el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, defineExpose, onMounted, watch } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { getTeams, getPersons, getTemplates } from '@/api/mock/spaceDispatchStrategy'

const props = defineProps({
  spaceId: {
    type: [Number, String],
    required: true
  },
  strategyData: {
    type: Object,
    default: () => ({
      rule_name: '',
      description: '',
      status: 'enabled',
      priority: 1,
      layers: [
        {
          max_times: 3,
          notify_step: 1,
          escalate_window: 15,
          target: {
            team_ids: [],
            person_ids: [],
            schedule_to_role_ids: {},
            by: {
              follow_preference: true,
              critical: ["email"],
              warning: ["email"],
              info: ["email"]
            },
            webhooks: []
          },
          template_id: '',
          notify_by_group_chat: false,
          group_chat_channels: [],
          enable_cycle_notify: false,
          cycle_notify_interval: 10,
          cycle_notify_times: 1,
          cycle_notify_max_loops: 1,
          cycle_notify_timeout: 30,
          cycle_notify_condition: 'not_closed'
        }
      ],
      aggr_window: 300,
      time_filters: [],
      filters: [[]]
    })
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'cancel'])

// 表单引用
const formRef = ref(null)

// 表单数据
const formData = reactive({...props.strategyData})

// 表单验证规则
const rules = {
  rule_name: [
    { required: true, message: '请输入策略名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ]
}

// 时间过滤器类型
const timeFilterType = ref('all')
const customTimeRange = ref(['09:00', '18:00'])
const customDays = ref([1, 2, 3, 4, 5]) // 默认工作日

// 选项数据
const teamOptions = ref([])
const personOptions = ref([])
const templateOptions = ref([])
const groupChatChannels = ref([])

// 获取团队列表
const fetchTeams = async () => {
  try {
    const response = await getTeams()
    teamOptions.value = response.data
  } catch (error) {
    console.error('获取团队列表失败', error)
  }
}

// 获取人员列表
const fetchPersons = async () => {
  try {
    const response = await getPersons()
    personOptions.value = response.data
  } catch (error) {
    console.error('获取人员列表失败', error)
  }
}

// 获取模板列表
const fetchTemplates = async () => {
  try {
    const response = await getTemplates()
    templateOptions.value = response.data
  } catch (error) {
    console.error('获取模板列表失败', error)
  }
}

// 初始化群聊渠道数据
const initGroupChatChannels = () => {
  // 模拟数据
  groupChatChannels.value = [
    { id: 'channel-001', name: '告警处理群' },
    { id: 'channel-002', name: '运维群' },
    { id: 'channel-003', name: '开发群' },
    { id: 'channel-004', name: '测试群' }
  ]
}

// 根据字段获取值选项
const getValueOptions = (key) => {
  if (key === 'severity') {
    return ['Critical', 'Warning', 'Info']
  }
  return []
}

// 添加条件
const addCondition = (groupIndex) => {
  formData.filters[groupIndex].push({
    key: 'severity',
    oper: 'IN',
    vals: []
  })
}

// 移除条件
const removeCondition = (groupIndex, condIndex) => {
  formData.filters[groupIndex].splice(condIndex, 1)
}

// 添加条件组
const addConditionGroup = () => {
  formData.filters.push([
    {
      key: 'severity',
      oper: 'IN',
      vals: []
    }
  ])
}

// 移除条件组
const removeConditionGroup = (groupIndex) => {
  formData.filters.splice(groupIndex, 1)
}

// 添加分派层级
const addLayer = () => {
  formData.layers.push({
    max_times: 3,
    notify_step: 1,
    escalate_window: 15,
    target: {
      team_ids: [],
      person_ids: [],
      schedule_to_role_ids: {},
      by: {
        follow_preference: true,
        critical: ["email"],
        warning: ["email"],
        info: ["email"]
      },
      webhooks: []
    },
    template_id: '',
    notify_by_group_chat: false,
    group_chat_channels: [],
    enable_cycle_notify: false,
    cycle_notify_interval: 10,
    cycle_notify_times: 1,
    cycle_notify_max_loops: 1,
    cycle_notify_timeout: 30,
    cycle_notify_condition: 'not_closed'
  })
}

// 移除分派层级
const removeLayer = (layerIndex) => {
  formData.layers.splice(layerIndex, 1)
}

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 处理时间过滤器
      if (timeFilterType.value === 'custom') {
        formData.time_filters = [
          {
            start: customTimeRange.value[0],
            end: customTimeRange.value[1],
            repeat: customDays.value,
            is_off: false
          }
        ]
      } else if (timeFilterType.value === 'workday') {
        formData.time_filters = [
          {
            start: '09:00',
            end: '18:00',
            repeat: [1, 2, 3, 4, 5],
            is_off: false
          }
        ]
      } else {
        formData.time_filters = []
      }

      emit('submit', {...formData})
    }
  })
}

// 初始化时间过滤器设置
const initTimeFilterSettings = () => {
  if (formData.time_filters && formData.time_filters.length > 0) {
    const filter = formData.time_filters[0]

    // 检查是否是工作日设置
    const isWorkday = filter.repeat.length === 5 &&
                      filter.repeat.every(day => [1, 2, 3, 4, 5].includes(day)) &&
                      filter.start === '09:00' &&
                      filter.end === '18:00'

    if (isWorkday) {
      timeFilterType.value = 'workday'
    } else {
      timeFilterType.value = 'custom'
      customTimeRange.value = [filter.start, filter.end]
      customDays.value = filter.repeat
    }
  } else {
    timeFilterType.value = 'all'
  }
}

// 监听策略数据变化
watch(() => props.strategyData, (newVal) => {
  Object.assign(formData, newVal)
  initTimeFilterSettings()
}, { deep: true })

// 对外暴露方法
defineExpose({
  submitForm
})

// 初始化
onMounted(() => {
  fetchTeams()
  fetchPersons()
  fetchTemplates()
  initGroupChatChannels()
  initTimeFilterSettings()

  // 确保至少有一个条件
  if (!formData.filters || formData.filters.length === 0) {
    formData.filters = [[{ key: 'severity', oper: 'IN', vals: [] }]]
  }

  // 确保每个条件组至少有一个条件
  formData.filters.forEach((group, index) => {
    if (group.length === 0) {
      formData.filters[index] = [{ key: 'severity', oper: 'IN', vals: [] }]
    }
  })

  // 初始化新增的字段
  formData.layers.forEach(layer => {
    if (!layer.template_id) {
      layer.template_id = ''
    }
    if (layer.notify_by_group_chat === undefined) {
      layer.notify_by_group_chat = false
    }
    if (!layer.group_chat_channels) {
      layer.group_chat_channels = []
    }
    if (layer.enable_cycle_notify === undefined) {
      layer.enable_cycle_notify = false
    }
    if (!layer.cycle_notify_interval) {
      layer.cycle_notify_interval = 10
    }
    if (!layer.cycle_notify_times) {
      layer.cycle_notify_times = 1
    }
    if (!layer.cycle_notify_max_loops) {
      layer.cycle_notify_max_loops = 1
    }
    if (!layer.cycle_notify_timeout) {
      layer.cycle_notify_timeout = 30
    }
    if (!layer.cycle_notify_condition) {
      layer.cycle_notify_condition = 'not_closed'
    }
  })
})
</script>

<script>
export default {
  name: 'DispatchStrategyForm'
}
</script>

<style scoped>
.dispatch-strategy-form {
  padding: 0 20px;
}

.form-section {
  margin-bottom: 30px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  background-color: #f9f9f9;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 20px;
  color: #303133;
  display: flex;
  align-items: center;
}

.section-tag {
  margin-right: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.filter-section {
  margin-top: 16px;
}

.filter-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.time-filter-custom {
  margin-bottom: 16px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.day-checkboxes {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.conditions-container {
  margin-top: 16px;
}

.condition-group {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.remove-group-btn {
  margin-left: 8px;
}

.condition-item {
  margin-bottom: 12px;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.condition-field {
  width: 120px;
}

.condition-operator {
  width: 100px;
}

.condition-value {
  flex: 1;
}

.condition-actions {
  display: flex;
  gap: 4px;
}

.add-group {
  margin-top: 16px;
}

.layer-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.layer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.layer-title {
  font-weight: 500;
  color: #303133;
}

.target-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.target-row {
  display: flex;
  align-items: center;
}

.target-label {
  width: 60px;
  font-size: 14px;
  color: #606266;
  font-weight: normal;
}

.notify-method {
  margin-top: 8px;
}

.severity-notify {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.severity-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.severity-label {
  width: 60px;
}

.add-layer {
  margin-top: 16px;
}

.w-100 {
  width: 100%;
}

.error-tip {
  color: #f56c6c;
}

.cycle-notify-settings {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 16px;
}

.cycle-notify-row, .escalation-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.escalation-settings {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.setting-text {
  font-size: 14px;
  color: #606266;
}

.timeout-input {
  width: 80px;
}

.times-input {
  width: 60px;
}

.condition-select {
  width: 150px;
}

.target-select {
  flex: 1;
}
</style>
