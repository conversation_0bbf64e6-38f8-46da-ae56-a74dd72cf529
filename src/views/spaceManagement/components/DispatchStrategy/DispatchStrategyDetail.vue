<template>
  <div class="dispatch-strategy-detail">
    <!-- 基本信息 -->
    <div class="detail-section">
      <div class="section-title">基本信息</div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="策略名称">{{ strategy.rule_name }}</el-descriptions-item>
        <el-descriptions-item label="策略ID">{{ strategy.rule_id }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="strategy.status === 'enabled' ? 'success' : 'info'">
            {{ strategy.status === 'enabled' ? '启用' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="优先级">{{ strategy.priority }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ strategy.created_at }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ strategy.updated_at }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ strategy.description || '无描述' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 匹配条件 -->
    <div class="detail-section">
      <div class="section-title">匹配条件</div>

      <!-- 时间过滤器 -->
      <div class="subsection" v-if="strategy.time_filters && strategy.time_filters.length > 0">
        <div class="subsection-title">时间范围</div>
        <div class="time-filters">
          <div v-for="(filter, index) in strategy.time_filters" :key="index" class="time-filter-item">
            <div class="time-range">
              <el-tag size="small" type="info">{{ filter.start }} - {{ filter.end }}</el-tag>
            </div>
            <div class="day-range">
              <el-tag
                v-for="day in filter.repeat"
                :key="day"
                size="small"
                class="day-tag"
              >
                {{ getDayName(day) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 条件过滤器 -->
      <div class="subsection">
        <div class="subsection-title">匹配条件</div>
        <div class="filter-groups">
          <div
            v-for="(group, groupIndex) in strategy.filters"
            :key="'group-' + groupIndex"
            class="filter-group"
          >
            <div class="group-operator" v-if="groupIndex > 0">或</div>
            <div class="group-conditions">
              <div
                v-for="(condition, condIndex) in group"
                :key="'cond-' + groupIndex + '-' + condIndex"
                class="condition-item"
              >
                <div class="condition-operator" v-if="condIndex > 0">且</div>
                <div class="condition-content">
                  <span class="condition-field">{{ getFieldName(condition.key) }}</span>
                  <span class="condition-oper">{{ getOperatorName(condition.oper) }}</span>
                  <div class="condition-values">
                    <el-tag
                      v-for="(val, valIndex) in condition.vals"
                      :key="valIndex"
                      size="small"
                      class="value-tag"
                    >
                      {{ val }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分派设置 -->
    <div class="detail-section">
      <div class="section-title">分派设置</div>

      <div
        v-for="(layer, layerIndex) in strategy.layers"
        :key="'layer-' + layerIndex"
        class="layer-item"
      >
        <div class="layer-header">
          <div class="layer-title">分派层级 {{ layerIndex + 1 }}</div>
        </div>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="最大通知次数">{{ layer.max_times }}</el-descriptions-item>
          <el-descriptions-item label="通知间隔">{{ layer.escalate_window }} 分钟</el-descriptions-item>
          <!-- 强制升级选项已移除 -->
        </el-descriptions>

        <div class="target-info">
          <div class="target-section">
            <div class="target-title">通知对象</div>

            <div class="target-teams" v-if="layer.target.team_ids && layer.target.team_ids.length > 0">
              <div class="target-label">团队:</div>
              <div class="target-values">
                <el-tag
                  v-for="teamId in layer.target.team_ids"
                  :key="teamId"
                  size="small"
                  class="team-tag"
                >
                  {{ getTeamName(teamId) }}
                </el-tag>
              </div>
            </div>

            <div class="target-persons" v-if="layer.target.person_ids && layer.target.person_ids.length > 0">
              <div class="target-label">人员:</div>
              <div class="target-values">
                <el-tag
                  v-for="personId in layer.target.person_ids"
                  :key="personId"
                  size="small"
                  class="person-tag"
                >
                  {{ getPersonName(personId) }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="notify-section">
            <div class="notify-title">通知方式</div>

            <div class="notify-preference">
              <el-tag type="info" size="small">
                {{ layer.target.by.follow_preference ? '遵循个人通知偏好设置' : '自定义通知方式' }}
              </el-tag>
            </div>

            <div class="notify-methods" v-if="!layer.target.by.follow_preference">
              <div class="severity-row">
                <span class="severity-label">严重:</span>
                <div class="method-tags">
                  <el-tag
                    v-for="method in layer.target.by.critical"
                    :key="method"
                    size="small"
                    type="danger"
                  >
                    {{ getMethodName(method) }}
                  </el-tag>
                </div>
              </div>

              <div class="severity-row">
                <span class="severity-label">警告:</span>
                <div class="method-tags">
                  <el-tag
                    v-for="method in layer.target.by.warning"
                    :key="method"
                    size="small"
                    type="warning"
                  >
                    {{ getMethodName(method) }}
                  </el-tag>
                </div>
              </div>

              <div class="severity-row">
                <span class="severity-label">信息:</span>
                <div class="method-tags">
                  <el-tag
                    v-for="method in layer.target.by.info"
                    :key="method"
                    size="small"
                    type="info"
                  >
                    {{ getMethodName(method) }}
                  </el-tag>
                </div>
              </div>
            </div>

            <div class="template-info" v-if="layer.template_id">
              <div class="info-label">通知模板:</div>
              <el-tag size="small" type="success">{{ getTemplateName(layer.template_id) }}</el-tag>
            </div>

            <div class="group-chat-info" v-if="layer.notify_by_group_chat">
              <div class="info-label">群聊通知:</div>
              <el-tag size="small" type="success">已启用</el-tag>

              <div class="channels-list" v-if="layer.group_chat_channels && layer.group_chat_channels.length > 0">
                <div class="info-label">通知渠道:</div>
                <div class="channel-tags">
                  <el-tag
                    v-for="channelId in layer.group_chat_channels"
                    :key="channelId"
                    size="small"
                    class="channel-tag"
                  >
                    {{ getChannelName(channelId) }}
                  </el-tag>
                </div>
              </div>
            </div>

            <div class="cycle-notify-info" v-if="layer.enable_cycle_notify">
              <div class="info-label">循环通知:</div>
              <el-tag size="small" type="warning">已启用</el-tag>

              <div class="cycle-settings">
                每隔 {{ layer.cycle_notify_interval }} 分钟通知 {{ layer.cycle_notify_times }} 次，
                最多循环通知 {{ layer.cycle_notify_max_loops }} 次。
              </div>
            </div>

            <div class="escalation-info">
              <div class="info-label">升级设置:</div>

              <div class="escalation-settings">
                超过 {{ layer.cycle_notify_timeout }} 分钟后如故障仍
                {{ layer.cycle_notify_condition === 'not_closed' ? '未关闭' : '未关闭且未认领' }}，
                则升级到下一环节。
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, onMounted } from 'vue'
import { getTeams, getPersons, getTemplates } from '@/api/mock/spaceDispatchStrategy'

const props = defineProps({
  strategy: {
    type: Object,
    required: true
  }
})

// 数据
const teams = ref([])
const persons = ref([])
const templates = ref([])
const groupChatChannels = ref([])

// 获取团队列表
const fetchTeams = async () => {
  try {
    const response = await getTeams()
    teams.value = response.data
  } catch (error) {
    console.error('获取团队列表失败', error)
  }
}

// 获取人员列表
const fetchPersons = async () => {
  try {
    const response = await getPersons()
    persons.value = response.data
  } catch (error) {
    console.error('获取人员列表失败', error)
  }
}

// 获取模板列表
const fetchTemplates = async () => {
  try {
    const response = await getTemplates()
    templates.value = response.data
  } catch (error) {
    console.error('获取模板列表失败', error)
  }
}

// 初始化群聊渠道数据
const initGroupChatChannels = () => {
  // 模拟数据
  groupChatChannels.value = [
    { id: 'channel-001', name: '告警处理群' },
    { id: 'channel-002', name: '运维群' },
    { id: 'channel-003', name: '开发群' },
    { id: 'channel-004', name: '测试群' }
  ]
}

// 获取星期几名称
const getDayName = (day) => {
  const dayNames = {
    0: '周日',
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六'
  }
  return dayNames[day] || day
}

// 获取字段名称
const getFieldName = (key) => {
  const fieldNames = {
    'severity': '告警级别',
    'title': '标题',
    'description': '描述',
    'alertname': '告警名称',
    'instance': '实例',
    'service': '服务'
  }
  return fieldNames[key] || key
}

// 获取操作符名称
const getOperatorName = (oper) => {
  const operNames = {
    'IN': '包含',
    'NOT_IN': '不包含',
    'EQ': '等于',
    'NEQ': '不等于'
  }
  return operNames[oper] || oper
}

// 获取团队名称
const getTeamName = (teamId) => {
  const team = teams.value.find(t => t.id === teamId)
  return team ? team.name : `团队 ${teamId}`
}

// 获取人员名称
const getPersonName = (personId) => {
  const person = persons.value.find(p => p.id === personId)
  return person ? person.name : `人员 ${personId}`
}

// 获取通知方式名称
const getMethodName = (method) => {
  const methodNames = {
    'email': '邮件',
    'sms': '短信',
    'call': '电话'
  }
  return methodNames[method] || method
}

// 获取模板名称
const getTemplateName = (templateId) => {
  const template = templates.value.find(t => t.id === templateId)
  return template ? template.name : `模板 ${templateId}`
}

// 获取群聊渠道名称
const getChannelName = (channelId) => {
  const channel = groupChatChannels.value.find(c => c.id === channelId)
  return channel ? channel.name : `渠道 ${channelId}`
}

// 初始化
onMounted(() => {
  fetchTeams()
  fetchPersons()
  fetchTemplates()
  initGroupChatChannels()
})
</script>

<script>
export default {
  name: 'DispatchStrategyDetail'
}
</script>

<style scoped>
.dispatch-strategy-detail {
  padding: 20px;
}

.detail-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  color: #303133;
}

.subsection {
  margin-top: 20px;
}

.subsection-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #606266;
}

.time-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-filter-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.day-tag {
  margin-right: 4px;
}

.filter-groups {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-group {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.group-operator {
  font-weight: 500;
  margin-bottom: 8px;
  color: #409eff;
}

.group-conditions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.condition-item {
  display: flex;
  flex-direction: column;
}

.condition-operator {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.condition-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.condition-field {
  font-weight: 500;
}

.condition-oper {
  color: #606266;
}

.condition-values {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.value-tag {
  margin-right: 0;
}

.layer-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.layer-header {
  margin-bottom: 16px;
}

.layer-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.target-info {
  margin-top: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.target-section, .notify-section {
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.target-title, .notify-title {
  font-weight: 500;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.target-teams, .target-persons {
  margin-bottom: 12px;
}

.target-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.target-values {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.team-tag, .person-tag {
  margin-right: 0;
}

.notify-preference {
  margin-bottom: 12px;
}

.notify-methods {
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.severity-row {
  display: flex;
  margin-bottom: 8px;
}

.severity-label {
  width: 60px;
  font-size: 14px;
  color: #606266;
}

.method-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.template-info, .group-chat-info, .cycle-notify-info, .escalation-info {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px dashed #ebeef5;
}

.info-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.channels-list {
  margin-top: 12px;
}

.channel-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.channel-tag {
  margin-right: 0;
}

.cycle-settings, .escalation-settings {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

@media (max-width: 768px) {
  .target-info {
    grid-template-columns: 1fr;
  }
}
</style>
