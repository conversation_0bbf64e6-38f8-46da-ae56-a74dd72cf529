<template>
  <div class="dispatch-strategy-list">
    <div class="list-header">
      <h3 class="list-title">分派策略</h3>
      <div class="header-description">
        您可以设定多个分派策略，新故障触发后将依次匹配，匹配到即进行人员分派，并停止匹配。
      </div>
      <el-button type="primary" @click="$emit('add-strategy')" class="add-button">
        <el-icon><Plus /></el-icon>新增一条策略
      </el-button>
    </div>

    <div class="strategy-list-content" v-loading="isLoading">
      <div v-if="strategies.length === 0 && !isLoading" class="empty-list">
        <el-empty description="暂无分派策略" />
        <el-button type="primary" @click="$emit('add-strategy')">新增策略</el-button>
      </div>

      <div v-else class="strategy-cards">
        <!-- 策略卡片 -->
        <div
          v-for="strategy in strategies"
          :key="strategy.rule_id"
          class="strategy-card"
          :class="{ 'strategy-card--disabled': strategy.status !== 'enabled' }"
          @click="viewStrategyDetail(strategy)"
        >
          <div class="strategy-card__header">
            <div class="strategy-info">
              <div class="strategy-name">
                <el-tag v-if="strategy.status === 'enabled'" type="success" size="small" class="status-tag">
                  启用中
                </el-tag>
                <el-tag v-else type="info" size="small" class="status-tag">
                  已停用
                </el-tag>
                {{ strategy.rule_name }}
              </div>
              <div class="strategy-id">ID: {{ strategy.rule_id }}</div>
            </div>
            <div class="strategy-actions" @click.stop>
              <el-dropdown trigger="click" @command="(cmd) => handleCommand(cmd, strategy)">
                <el-button type="text" class="more-button">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="view">查看详情</el-dropdown-item>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item
                      command="toggle"
                      :divided="true"
                    >
                      {{ strategy.status === 'enabled' ? '停用' : '启用' }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="strategy-card__body">
            <div class="strategy-description">{{ strategy.description || '无描述' }}</div>
            <div class="strategy-meta">
              <div class="meta-item">
                <span class="meta-label">优先级:</span>
                <span class="meta-value">{{ strategy.priority }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">更新时间:</span>
                <span class="meta-value">{{ strategy.updated_at }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MoreFilled } from '@element-plus/icons-vue'
import { getSpaceDispatchStrategies, toggleDispatchStrategyStatus } from '@/api/mock/spaceDispatchStrategy'

const props = defineProps({
  spaceId: {
    type: [Number, String],
    required: true
  }
})

const emit = defineEmits(['add-strategy', 'edit-strategy', 'view-strategy'])

// 数据
const strategies = ref([])
const isLoading = ref(false)

// 获取策略列表
const fetchStrategies = async () => {
  try {
    isLoading.value = true
    const response = await getSpaceDispatchStrategies(props.spaceId)
    strategies.value = response.data.items || []
  } catch (error) {
    console.error('获取分派策略列表失败', error)
    ElMessage.error('获取分派策略列表失败')
  } finally {
    isLoading.value = false
  }
}

// 处理下拉菜单命令
const handleCommand = (command, strategy) => {
  if (command === 'view') {
    emit('view-strategy', strategy)
  } else if (command === 'edit') {
    emit('edit-strategy', strategy)
  } else if (command === 'toggle') {
    toggleStrategyStatus(strategy)
  }
}

// 查看策略详情
const viewStrategyDetail = (strategy) => {
  emit('view-strategy', strategy)
}

// 切换策略状态
const toggleStrategyStatus = async (strategy) => {
  try {
    const actionText = strategy.status === 'enabled' ? '停用' : '启用'
    await ElMessageBox.confirm(
      `确定要${actionText}该分派策略吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    isLoading.value = true
    const newStatus = strategy.status === 'enabled' ? 'disabled' : 'enabled'
    await toggleDispatchStrategyStatus(props.spaceId, strategy.rule_id, newStatus)

    // 更新本地数据
    const index = strategies.value.findIndex(s => s.rule_id === strategy.rule_id)
    if (index !== -1) {
      strategies.value[index].status = newStatus
    }

    ElMessage.success(`${actionText}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败', error)
      ElMessage.error('操作失败')
    }
  } finally {
    isLoading.value = false
  }
}

// 监听spaceId变化，重新获取数据
watch(() => props.spaceId, (newVal) => {
  if (newVal) {
    fetchStrategies()
  }
})

// 初始化
onMounted(() => {
  fetchStrategies()
})
</script>

<script>
export default {
  name: 'DispatchStrategyList'
}
</script>

<style scoped>
.dispatch-strategy-list {
  width: 100%;
}

.list-header {
  margin-bottom: 20px;
  position: relative;
}

.list-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #303133;
}

.header-description {
  font-size: 14px;
  color: #909399;
  margin-bottom: 16px;
}

.add-button {
  margin-bottom: 16px;
}

.strategy-list-content {
  min-height: 200px;
}

.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.strategy-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.strategy-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.strategy-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
  border-color: #409eff;
}

.strategy-card--disabled {
  opacity: 0.7;
  background-color: #f8f8f8;
}

.strategy-card__header {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.strategy-info {
  flex: 1;
  overflow: hidden;
}

.strategy-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}

.status-tag {
  margin-right: 8px;
}

.strategy-id {
  font-size: 12px;
  color: #909399;
}

.strategy-actions {
  display: flex;
}

.more-button {
  padding: 0;
  font-size: 20px;
}

.strategy-card__body {
  padding: 16px;
}

.strategy-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 16px;
  min-height: 40px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.strategy-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  font-size: 13px;
  color: #909399;
}

.meta-label {
  margin-right: 4px;
}

.meta-value {
  color: #606266;
}
</style>
