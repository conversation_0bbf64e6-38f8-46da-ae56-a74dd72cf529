<template>
  <div class="dispatch-strategy-container">
    <DispatchStrategyList
      :space-id="spaceId"
      @add-strategy="showAddStrategyForm"
      @edit-strategy="showEditStrategyForm"
      @view-strategy="showStrategyDetail"
    />

    <!-- 创建/编辑策略对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEditMode ? '编辑分派策略' : '新增分派策略'"
      width="650px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <DispatchStrategyForm
        ref="strategyFormRef"
        :space-id="spaceId"
        :strategy-data="currentStrategy"
        :is-edit="isEditMode"
        @submit="handleFormSubmit"
        @cancel="formDialogVisible = false"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="formDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitStrategyForm" :loading="isSubmitting">保 存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 策略详情对话框 -->
    <el-drawer
      v-model="detailDrawerVisible"
      :title="currentStrategy.rule_name || '分派策略详情'"
      size="60%"
      direction="rtl"
      destroy-on-close
    >
      <DispatchStrategyDetail
        :strategy="currentStrategy"
        v-if="detailDrawerVisible"
      />
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import DispatchStrategyList from './DispatchStrategyList.vue'
import DispatchStrategyForm from './DispatchStrategyForm.vue'
import DispatchStrategyDetail from './DispatchStrategyDetail.vue'
import { createDispatchStrategy, updateDispatchStrategy } from '@/api/mock/spaceDispatchStrategy'

const props = defineProps({
  spaceId: {
    type: [Number, String],
    required: true
  }
})

// 表单对话框
const formDialogVisible = ref(false)
const isEditMode = ref(false)
const currentStrategy = ref({})
const strategyFormRef = ref(null)
const isSubmitting = ref(false)

// 详情抽屉
const detailDrawerVisible = ref(false)

// 显示添加策略表单
const showAddStrategyForm = () => {
  isEditMode.value = false
  currentStrategy.value = {
    rule_name: '',
    description: '',
    status: 'enabled',
    priority: 1,
    layers: [
      {
        max_times: 3,
        notify_step: 1,
        escalate_window: 15,
        force_escalate: true,
        target: {
          team_ids: [],
          person_ids: [],
          schedule_to_role_ids: {},
          by: {
            follow_preference: true,
            critical: ["email"],
            warning: ["email"],
            info: ["email"]
          },
          webhooks: []
        }
      }
    ],
    aggr_window: 300,
    time_filters: [],
    filters: [[]]
  }
  formDialogVisible.value = true
}

// 显示编辑策略表单
const showEditStrategyForm = (strategy) => {
  isEditMode.value = true
  currentStrategy.value = { ...strategy }
  formDialogVisible.value = true
}

// 显示策略详情
const showStrategyDetail = (strategy) => {
  currentStrategy.value = { ...strategy }
  detailDrawerVisible.value = true
}

// 提交表单
const submitStrategyForm = () => {
  if (strategyFormRef.value) {
    strategyFormRef.value.submitForm()
  }
}

// 处理表单提交
const handleFormSubmit = async (formData) => {
  try {
    isSubmitting.value = true

    if (isEditMode.value) {
      // 更新策略
      await updateDispatchStrategy(props.spaceId, formData.rule_id, formData)
      ElMessage.success('策略更新成功')
    } else {
      // 创建策略
      await createDispatchStrategy(props.spaceId, formData)
      ElMessage.success('策略创建成功')
    }

    formDialogVisible.value = false
  } catch (error) {
    console.error('保存策略失败', error)
    ElMessage.error('保存策略失败')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<script>
export default {
  name: 'DispatchStrategyComponent'
}
</script>

<style scoped>
.dispatch-strategy-container {
  width: 100%;
  height: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
</style>
