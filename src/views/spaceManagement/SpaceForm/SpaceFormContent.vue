<template>
  <div class="space-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="space-form__content"
    >
      <el-form-item label="团队空间名称" prop="name" required>
        <el-input 
          v-model="formData.name" 
          placeholder="请输入团队空间名称" 
          clearable
          maxlength="50"
          show-word-limit
        >
          <template #prefix>
            <el-icon><Document /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="管理团队" prop="teamId" required>
        <div class="team-select-container">
          <el-select
            v-model="formData.teamId"
            placeholder="请选择管理团队"
            class="w-100"
            filterable
            clearable
            :loading="isTeamLoading"
            loading-text="加载中..."
            @change="(val) => console.log('团队选择变更为:', val)"
          >
            <template #prefix>
              <el-icon v-if="isTeamLoading"><Loading /></el-icon>
            </template>
            <el-option
              v-for="team in teamList"
              :key="team.id"
              :label="getTeamDisplayName(team)"
              :value="team.id"
            >
              <span>{{ getTeamDisplayName(team) }}</span>
              <span v-if="team.remark" style="color: #909399; font-size: 12px; margin-left: 8px;">
                ({{ team.remark }})
              </span>
            </el-option>
          </el-select>
          <el-button
            type="primary"
            link
            :icon="Refresh"
            :loading="isTeamLoading"
            @click="refreshTeamList"
            class="refresh-btn"
            title="刷新团队列表"
          >刷新</el-button>
        </div>
        <div v-if="teamList.length === 0 && !isTeamLoading" style="color: #909399; font-size: 12px; margin-top: 4px;">
          <el-icon><Warning /></el-icon>
          团队列表为空，请点击刷新按钮重新获取
        </div>
        <div v-else-if="!isTeamLoading && teamList.length > 0" style="color: #67c23a; font-size: 12px; margin-top: 4px;">
          <el-icon><SuccessFilled /></el-icon>
          已加载 {{ teamList.length }} 个团队
          <span v-if="selectedTeamName" style="margin-left: 8px;">
            | 当前选中: {{ selectedTeamName }}
          </span>
        </div>
        <!-- 调试信息 -->
        <div v-if="formData.teamId" style="color: #409eff; font-size: 12px; margin-top: 4px;">
          调试: teamId={{ formData.teamId }}, 团队数量={{ teamList.length }}
        </div>
      </el-form-item>

      <el-form-item label="协作空间描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入协作空间描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-divider content-position="left">访问权限设置</el-divider>

      <el-form-item label="访问级别" class="visibility-option">
        <el-radio-group v-model="formData.visibility">
          <div class="visibility-cards">
            <div 
              class="visibility-card" 
              :class="{ 'visibility-card--selected': formData.visibility === 'public' }"
              @click="formData.visibility = 'public'"
            >
              <div class="visibility-card__header">
                <el-radio label="public" />
                <el-icon><Lock /></el-icon>
              </div>
              <div class="visibility-card__title">公开</div>
              <div class="visibility-card__desc">空间下被授权的所有人员开放。</div>
            </div>

            <div 
              class="visibility-card" 
              :class="{ 'visibility-card--selected': formData.visibility === 'private' }"
              @click="formData.visibility = 'private'"
            >
              <div class="visibility-card__header">
                <el-radio label="private" />
                <el-icon><Lock /></el-icon>
              </div>
              <div class="visibility-card__title">私有</div>
              <div class="visibility-card__desc">空间下故障仅对团队管理成员、空间创建者或账户管理员开放（其它成员仅可通过分享链接查看）。</div>
            </div>
          </div>
        </el-radio-group>
      </el-form-item>

      <!-- <el-form-item label="超时自动关闭" class="auto-relation">
        <div class="switch-container">
          <el-switch v-model="formData.autoRelation" />
          <span class="switch-desc">{{ formData.autoRelation ? '已开启，将会在未操作时自动关闭事件。' : '未开启，不会自动关闭事件。' }}</span>
        </div>
        <div class="setting-hint">
          <el-icon><InfoFilled /></el-icon>
          <span>开启后可在设置中配置超时时长</span>
        </div>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, defineEmits, defineProps, defineExpose, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getTeamList } from '@/api/services/system'
import { Document, Lock, Refresh, Loading, Warning, SuccessFilled } from '@element-plus/icons-vue'

const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({
      name: '',
      teamId: '',
      description: '',
      visibility: 'public',
      autoRelation: false
    })
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'cancel'])

const formRef = ref(null)
const teamList = ref([])
const formData = ref({ ...props.initialData })
const isTeamLoading = ref(false)

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入协作空间名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  teamId: [
    { required: true, message: '请选择管理团队', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ]
}

// 获取团队显示名称
const getTeamDisplayName = (team) => {
  if (!team) return ''
  // 优先使用 trans 字段，如果没有则使用 name 字段
  return team.trans || team.name || `团队${team.id}`
}

// 计算当前选中团队的名称
const selectedTeamName = computed(() => {
  if (!formData.value.teamId || teamList.value.length === 0) {
    return ''
  }
  // 确保类型匹配，支持数字和字符串类型的ID比较
  const selectedTeam = teamList.value.find(team =>
    team.id == formData.value.teamId || team.id === formData.value.teamId
  )
  return selectedTeam ? getTeamDisplayName(selectedTeam) : `团队ID: ${formData.value.teamId}`
})

// 获取团队列表
const fetchTeamList = async (showMessage = false) => {
  try {
    isTeamLoading.value = true

    const response = await getTeamList({
      page: 1,
      pageSize: 100
    })

    console.log('团队列表API响应:', response)

    // 检查响应格式并适配
    if (response && response.code === 0) {
      teamList.value = response.data.data || []
      console.log('团队列表数据:', teamList.value)
      if (showMessage) {
        ElMessage.success(`成功获取 ${teamList.value.length} 个团队`)
      }
    } else {
      console.error('获取团队列表失败:', response?.msg || '未知错误')
      if (showMessage) {
        ElMessage.error(response?.msg || '获取团队列表失败')
      }
    }
  } catch (error) {
    console.error('获取团队列表失败', error)
    if (showMessage) {
      ElMessage.error('获取团队列表失败: ' + (error.message || '网络错误'))
    }
  } finally {
    isTeamLoading.value = false
  }
}

// 刷新团队列表
const refreshTeamList = async () => {
  await fetchTeamList(true)
}

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', { ...formData.value })
    }
  })
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  formData.value = { ...props.initialData }
}

// 对外暴露的方法
defineExpose({
  submitForm,
  resetForm
})

// 监听initialData变化
watch(() => props.initialData, (newVal) => {
  console.log('SpaceFormContent接收到新的initialData:', newVal)
  formData.value = { ...newVal }
  console.log('formData更新为:', formData.value)
}, { deep: true })

// 监听teamList变化，确保在编辑模式下能正确显示选中的团队
watch(teamList, (newTeamList) => {
  if (newTeamList.length > 0 && props.isEdit && formData.value.teamId) {
    // 检查当前选中的团队是否在列表中，支持类型转换
    const selectedTeam = newTeamList.find(team =>
      team.id == formData.value.teamId || team.id === formData.value.teamId
    )
    if (!selectedTeam) {
      console.warn('当前选中的团队不在团队列表中:', formData.value.teamId)
      console.log('可用团队列表:', newTeamList.map(t => ({ id: t.id, name: getTeamDisplayName(t) })))
    } else {
      console.log('找到选中的团队:', selectedTeam, '显示名称:', getTeamDisplayName(selectedTeam))
    }
  }
}, { immediate: true })

// 初始化
onMounted(async () => {
  console.log('SpaceFormContent组件挂载，props.initialData:', props.initialData)
  console.log('props.isEdit:', props.isEdit)

  formData.value = { ...props.initialData }
  console.log('初始化formData:', formData.value)

  // 先获取团队列表
  await fetchTeamList()

  // 如果是编辑模式且有默认的teamId，确保它在列表中
  if (props.isEdit && props.initialData.teamId) {
    console.log('编辑模式，检查teamId:', props.initialData.teamId)
    const selectedTeam = teamList.value.find(team =>
      team.id == props.initialData.teamId || team.id === props.initialData.teamId
    )
    if (!selectedTeam && teamList.value.length > 0) {
      console.log('默认团队不在列表中，刷新团队列表')
      // 如果默认团队不在列表中，再次刷新团队列表
      await fetchTeamList(false)
    } else if (selectedTeam) {
      console.log('找到默认团队:', selectedTeam)
    }
  }
})
</script>

<style lang="scss" scoped>
.space-form {
  &__content {
    width: 100%;
    max-width: 100%;
  }

  .w-100 {
    width: 100%;
  }

  .team-select-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-select {
      flex: 1;
    }

    .refresh-btn {
      flex-shrink: 0;
      padding: 8px 12px;
      font-size: 12px;
    }
  }

  .el-divider {
    margin: 20px 0;
    
    &::before, &::after {
      border-top: 1px solid #ebeef5;
    }
    
    .el-divider__text {
      font-size: 14px;
      color: #606266;
      background-color: #fff;
    }
  }

  .visibility-cards {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    width: 100%;
  }

  .visibility-card {
    flex: 1;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409eff;
    }
    
    &--selected {
      border-color: #409eff;
      background-color: #ecf5ff;
    }
    
    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      .el-icon {
        font-size: 16px;
        color: #909399;
      }
    }
    
    &__title {
      font-weight: bold;
      margin-bottom: 5px;
      font-size: 14px;
    }
    
    &__desc {
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
    }
  }

  .auto-relation {
    .switch-container {
      display: flex;
      align-items: center;
    }
    
    .switch-desc {
      margin-left: 10px;
      color: #606266;
      font-size: 13px;
    }
    
    .setting-hint {
      margin-top: 8px;
      margin-left: 0;
      display: flex;
      align-items: center;
      color: #909399;
      font-size: 12px;
      
      .el-icon {
        color: #e6a23c;
        margin-right: 5px;
        font-size: 14px;
      }
    }
  }
}
</style> 