<template>
  <SpaceFormContent 
    ref="formRef"
    :initial-data="initialData"
    :is-edit="isEdit"
    @submit="$emit('submit', $event)"
    @cancel="$emit('cancel')"
  />
</template>

<script>
import SpaceFormContent from './SpaceFormContent.vue'

export default {
  name: 'SpaceForm',
  components: {
    SpaceFormContent
  }
}
</script>

<script setup>
import { defineProps, defineEmits } from 'vue'

defineProps({
  initialData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

defineEmits(['submit', 'cancel'])
</script>

<style lang="scss" scoped>
.space-form {
  &__content {
    max-width: 600px;
  }

  .w-100 {
    width: 100%;
  }

  .radio-content {
    display: flex;
    flex-direction: column;
    margin-left: 5px;
  }

  .radio-title {
    font-weight: bold;
    margin-bottom: 5px;
  }

  .radio-desc {
    color: #909399;
    font-size: 12px;
  }

  .switch-desc {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
  }
}
</style> 