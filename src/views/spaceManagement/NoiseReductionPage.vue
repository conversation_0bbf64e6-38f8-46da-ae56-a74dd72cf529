<template>
  <div class="noise-reduction-page">
    <h1 class="page-title">降噪配置</h1>
    <noise-reduction :space-id="spaceId" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import NoiseReduction from './components/NoiseReduction/index.vue'

// 假设当前空间ID为1
const spaceId = ref(1)
</script>

<style scoped>
.noise-reduction-page {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 24px;
}
</style>
