<template>
  <div class="space-management">
    <div class="space-management__header">
      <el-tabs v-model="activeTab" class="space-tabs">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="我管理的" name="managed"></el-tab-pane>
        <el-tab-pane label="我收藏的" name="favorite"></el-tab-pane>
      </el-tabs>
      <el-input
        v-model="searchKeyword"
        placeholder="输入空间名称或标签搜索"
        class="search-input"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="space-management__content" v-loading="isLoading">
      <!-- 新建工作空间卡片 -->
      <div class="space-card space-card--new" @click="openCreateDialog">
        <el-icon class="add-icon"><Plus /></el-icon>
        <span>新建团队空间</span>
      </div>

      <!-- 协作空间卡片列表 -->
      <div
        v-for="space in filteredSpaces"
        :key="space.id"
        class="space-card"
        :class="{
          'space-card--starred': space.isStarred,
          'space-card--disabled': !space.enabled
        }"
      >
        <div class="space-card__header">
          <span class="space-name">{{ space.name }}</span>
          <div class="space-actions">
            <el-icon
              class="star-icon"
              :class="{ 'is-starred': space.isStarred }"
              @click.stop="toggleStar(space)"
            >
              <Star />
            </el-icon>
            <el-dropdown
              trigger="click"
              @command="(command) => handleCommand(command, space)"
              @click.stop
            >
              <el-icon class="more-icon"><MoreFilled /></el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item
                    command="toggle"
                    :divided="true"
                  >
                    {{ space.enabled ? '停用' : '启用' }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        <div class="space-card__content" @click="handleCardClick(space)">
           <!-- 故障统计 -->
           <div class="space-statistics">
            <el-tag class="stat-tag stat-processing" size="small" :effect="space.issueStats.processing > 0 ? 'light' : 'plain'">
              处理中 {{ space.issueStats.processing }}
            </el-tag>
            <el-tag class="stat-tag stat-pending" size="small" :effect="space.issueStats.pending > 0 ? 'light' : 'plain'">
              待处理 {{ space.issueStats.pending }}
            </el-tag>
            <el-tag class="stat-tag stat-completed" size="small" :effect="space.issueStats.completed > 0 ? 'light' : 'plain'">
              已处理 {{ space.issueStats.completed }}
            </el-tag>
          </div>

          <!-- 团队信息 -->
          <div class="space-team">
            <el-icon><User /></el-icon>
            <span>{{ space.teamName }}</span>
          </div>
          <div class="space-description">{{ space.description }}</div>

        </div>
        <div class="space-card__footer">
          <div class="space-visibility">
            <el-tag size="small" :type="space.visibility === 'public' ? 'success' : 'info'">
              {{ space.visibility === 'public' ? '公开' : '私有' }}
            </el-tag>
          </div>
          <div class="space-status-info">
            <el-tooltip
              effect="dark"
              :content="`${space.enabled ? '已启用' : '已停用'} 于 ${formatDateTime(space.statusChangedAt)}`"
              placement="top"
            >
              <div class="status-text" :class="{'status-enabled': space.enabled, 'status-disabled': !space.enabled}">
                {{ space.enabled ? '启用中' : '已停用' }}
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredSpaces.length === 0 && !isLoading" class="empty-state">
        <el-empty description="暂无协作空间" />
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑协作空间' : '新建协作空间'"
      width="650px"
      destroy-on-close
    >
      <SpaceForm
        ref="spaceFormRef"
        :initial-data="currentSpace"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="isLoading">保 存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star, User, Plus, Search, MoreFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import SpaceForm from './SpaceForm/index.vue'
import { getSpaceList, createSpace, updateSpace, toggleSpaceStatus } from '@/api/services/oncall/space'

// 路由
const router = useRouter()

// 数据
const activeTab = ref('all')
const searchKeyword = ref('')
const spaces = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentSpace = ref({
  name: '',
  teamId: '',
  description: '',
  visibility: 'public',
  autoRelation: false
})
const spaceFormRef = ref(null)
const isLoading = ref(false)

// 过滤后的空间列表
const filteredSpaces = computed(() => {
  let result = [...spaces.value]

  // 根据标签过滤
  if (activeTab.value === 'managed') {
    // 这里应该根据实际情况过滤，这里假设用户ID为1
    result = result.filter(space => space.teamId === 1)
  } else if (activeTab.value === 'favorite') {
    result = result.filter(space => space.isStarred)
  }

  // 根据关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(space =>
      space.name.toLowerCase().includes(keyword) ||
      space.description.toLowerCase().includes(keyword)
    )
  }

  return result
})

// 获取工作空间列表
const fetchSpaces = async () => {
  try {
    isLoading.value = true
    const response = await getSpaceList({
      page: 1,
      pageSize: 100
    })

    if (response.code === 0) {
      spaces.value = response.data.data.map(space => ({
        ...space,
        isStarred: false
      }))
    } else {
      ElMessage.error(response.msg || '获取工作空间列表失败')
    }
  } catch (error) {
    console.error('获取工作空间列表失败', error)
    ElMessage.error('获取工作空间列表失败')
  } finally {
    isLoading.value = false
  }
}

// 收藏/取消收藏
const toggleStar = (space) => {
  space.isStarred = !space.isStarred
  ElMessage.success(space.isStarred ? '已添加到收藏' : '已取消收藏')
}

// 点击卡片
const handleCardClick = (space) => {
  if (!space.enabled) {
    ElMessage.warning('该空间已停用，请先启用后再操作')
    return
  }
  // 跳转到空间详情页
  router.push(`/oncall/space/detail/${space.id}`)
}

// 处理下拉菜单命令
const handleCommand = (command, space) => {
  if (command === 'edit') {
    openEditDialog(space)
  } else if (command === 'toggle') {
    toggleSpaceEnabledStatus(space)
  }
}

// 切换空间启用状态
const toggleSpaceEnabledStatus = async (space) => {
  try {
    const actionText = space.enabled ? '停用' : '启用'
    await ElMessageBox.confirm(
      `确定要${actionText}该协作空间吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    isLoading.value = true
    const response = await toggleSpaceStatus(space.id, !space.enabled)

    if (response.code === 0) {
      space.enabled = !space.enabled
      space.statusChangedAt = new Date().toISOString()
      ElMessage.success(`${actionText}成功`)
    } else {
      ElMessage.error(response.msg || '操作失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败', error)
      ElMessage.error('操作失败')
    }
  } finally {
    isLoading.value = false
  }
}

// 打开创建对话框
const openCreateDialog = () => {
  isEdit.value = false
  currentSpace.value = {
    name: '',
    teamId: '',
    description: '',
    visibility: 'public',
    autoRelation: false
  }
  dialogVisible.value = true
}

// 打开编辑对话框
const openEditDialog = (space) => {
  isEdit.value = true
  currentSpace.value = { ...space }
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  spaceFormRef.value.submitForm()
}

// 处理表单提交
const handleFormSubmit = async (formData) => {
  try {
    isLoading.value = true
    let response

    if (isEdit.value) {
      response = await updateSpace(currentSpace.value.id, formData)
      if (response.code === 0) {
        ElMessage.success('更新协作空间成功')
      } else {
        ElMessage.error(response.msg || '更新协作空间失败')
        return
      }
    } else {
      response = await createSpace(formData)
      if (response.code === 0) {
        ElMessage.success('创建协作空间成功')
      } else {
        ElMessage.error(response.msg || '创建协作空间失败')
        return
      }
    }

    dialogVisible.value = false
    fetchSpaces()
  } catch (error) {
    console.error('操作失败', error)
    ElMessage.error('操作失败')
  } finally {
    isLoading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '未知'

  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 组件挂载后获取工作空间列表
onMounted(() => {
  fetchSpaces()
})
</script>

<style lang="scss" scoped>
.space-management {
  padding: 20px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .space-tabs {
      flex: 1;
    }

    .search-input {
      width: 300px;
      margin-left: 20px;

      :deep(.el-input__wrapper) {
        border-radius: 20px;
      }
    }
  }

  &__content {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    min-height: 300px;
    position: relative;
  }

  .space-card {
    width: 280px;
    min-height: 180px;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    padding: 16px;
    transition: all 0.3s;
    position: relative;
    display: flex;
    flex-direction: column;

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    &--starred {
      border-color: #409eff;

      .star-icon {
        color: #f7ba2a;
      }
    }

    &--disabled {
      opacity: 0.7;
      background-color: #f8f8f8;

      &:hover {
        box-shadow: none;
        transform: none;
      }

      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.3);
        pointer-events: none;
      }
    }

    &--new {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-style: dashed;
      color: #909399;
      cursor: pointer;

      &:hover {
        color: #409eff;
        border-color: #409eff;
      }

      .add-icon {
        font-size: 32px;
        margin-bottom: 10px;
      }
    }

    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .space-name {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .space-actions {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .star-icon {
        cursor: pointer;
        font-size: 18px;
        color: #c0c4cc;
        transition: all 0.3s;

        &:hover,
        &.is-starred {
          color: #f7ba2a;
          transform: scale(1.1);
        }
      }

      .more-icon {
        cursor: pointer;
        font-size: 18px;
        color: #909399;
        transition: all 0.3s;

        &:hover {
          color: #606266;
        }
      }
    }

    &__content {
      flex: 1;
      cursor: pointer;

      .space-status {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin-bottom: 10px;

        &.status-processing {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &.status-pending {
          background-color: #fff7e6;
          color: #fa8c16;
        }

        &.status-completed {
          background-color: #f6ffed;
          color: #52c41a;
        }
      }

      .space-team {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        color: #606266;
        font-size: 14px;

        .el-icon {
          margin-right: 6px;
        }
      }

      .space-description {
        font-size: 14px;
        color: #606266;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 1.4;
        margin-bottom: 10px;
      }

      .space-statistics {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 12px;

        .stat-tag {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          margin-right: 0;
          border: none;

          &.stat-processing {
            background-color: #e6f7ff;
            color: #1890ff;

            &.el-tag--plain {
              background-color: #f5f7fa;
              color: #909399;
            }
          }

          &.stat-pending {
            background-color: #fff7e6;
            color: #fa8c16;

            &.el-tag--plain {
              background-color: #f5f7fa;
              color: #909399;
            }
          }

          &.stat-completed {
            background-color: #f6ffed;
            color: #52c41a;

            &.el-tag--plain {
              background-color: #f5f7fa;
              color: #909399;
            }
          }
        }
      }
    }

    &__footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: auto;
      padding-top: 10px;
      border-top: 1px solid #f0f0f0;

      .space-status-info {
        font-size: 12px;

        .status-text {
          padding: 2px 6px;
          border-radius: 2px;

          &.status-enabled {
            color: #67c23a;
            background-color: #f0f9eb;
          }

          &.status-disabled {
            color: #909399;
            background-color: #f4f4f5;
          }
        }
      }
    }
  }

  .empty-state {
    padding: 40px 0;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>