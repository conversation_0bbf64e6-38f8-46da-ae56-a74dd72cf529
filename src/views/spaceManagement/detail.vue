<template>
  <div class="space-detail-container">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="space-title">{{ space.name || '测试告警服务' }}</h2>
        <div class="space-id">ID: {{ space.id || spaceId }}</div>
      </div>
      <div class="header-right">
        <el-tag size="small" type="info" class="team-tag">{{ space.teamName || 'SRE 团队' }}</el-tag>
      </div>
    </div>

    <!-- 头部区域 - 指标卡片 -->
    <div class="metrics-cards">
      <!-- MTTA 卡片 -->
      <div class="metric-card mtta-card">
        <div class="metric-title">过去一周MTTA(平均认领耗时)</div>
        <div class="metric-value">
          {{ metrics.mtta.value }}
          <span class="metric-unit">{{ metrics.mtta.unit }}</span>
        </div>
        <div class="metric-trend" v-if="metrics.mtta.value > 0">
          <el-tooltip
            :content="getTrendTooltip(metrics.mtta.trend, metrics.mtta.change, 'MTTA')"
            placement="bottom"
            effect="light"
          >
            <div class="trend-wrapper">
              <el-icon :class="getTrendClass(metrics.mtta.trend)">
                <component :is="getTrendIcon(metrics.mtta.trend)"></component>
              </el-icon>
              <span>{{ getTrendText(metrics.mtta.trend, metrics.mtta.change) }}</span>
            </div>
          </el-tooltip>
        </div>
      </div>

      <!-- MTTR 卡片 -->
      <div class="metric-card mttr-card">
        <div class="metric-title">过去一周MTTR(平均恢复用时)</div>
        <div class="metric-value">
          {{ metrics.mttr.value }}
          <span class="metric-unit">{{ metrics.mttr.unit }}</span>
        </div>
        <div class="metric-trend" v-if="metrics.mttr.value > 0">
          <el-tooltip
            :content="getTrendTooltip(metrics.mttr.trend, metrics.mttr.change, 'MTTR')"
            placement="bottom"
            effect="light"
          >
            <div class="trend-wrapper">
              <el-icon :class="getTrendClass(metrics.mttr.trend)">
                <component :is="getTrendIcon(metrics.mttr.trend)"></component>
              </el-icon>
              <span>{{ getTrendText(metrics.mttr.trend, metrics.mttr.change) }}</span>
            </div>
          </el-tooltip>
        </div>
      </div>

      <!-- 故障数量卡片 -->
      <div class="metric-card incident-card">
        <div class="metric-title">过去一周故障数量</div>
        <div class="metric-value">
          {{ metrics.incidentCount.value }}
        </div>
        <div class="metric-trend" v-if="metrics.incidentCount.value > 0">
          <el-tooltip
            :content="getTrendTooltip(metrics.incidentCount.trend, metrics.incidentCount.change, '故障数量')"
            placement="bottom"
            effect="light"
          >
            <div class="trend-wrapper">
              <el-icon :class="getTrendClass(metrics.incidentCount.trend)">
                <component :is="getTrendIcon(metrics.incidentCount.trend)"></component>
              </el-icon>
              <span>{{ getTrendText(metrics.incidentCount.trend, metrics.incidentCount.change) }}</span>
            </div>
          </el-tooltip>
        </div>
      </div>

      <!-- 降噪比卡片 -->
      <div class="metric-card noise-card">
        <div class="metric-title">过去一周降噪比</div>
        <div class="metric-value" v-if="metrics.noiseReduction.value > 0">
          {{ metrics.noiseReduction.value }}
          <span class="metric-unit">{{ metrics.noiseReduction.unit }}</span>
        </div>
        <div class="metric-value" v-else>
          设置<span class="setup-link">降噪规则</span>即可查看
        </div>
        <div class="metric-trend" v-if="metrics.noiseReduction.value > 0">
          <el-tooltip
            :content="getTrendTooltip(metrics.noiseReduction.trend, metrics.noiseReduction.change, '降噪比')"
            placement="bottom"
            effect="light"
          >
            <div class="trend-wrapper">
              <el-icon :class="getTrendClass(metrics.noiseReduction.trend)">
                <component :is="getTrendIcon(metrics.noiseReduction.trend)"></component>
              </el-icon>
              <span>{{ getTrendText(metrics.noiseReduction.trend, metrics.noiseReduction.change) }}</span>
            </div>
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 标签页区域 -->
    <el-tabs v-model="activeTab" class="detail-tabs">
      <!-- 故障列表标签页 (修改为消息列表) -->
      <el-tab-pane label="消息列表" name="messages">
        <div class="tab-content">
          <!-- 消息列表 -->
          <el-table
            :data="messageList"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
            @row-dblclick="handleRowDblClick"
          >
            <el-table-column type="selection" width="40" />
            <el-table-column width="5">
              <template #default="scope">
                <div class="status-line" :class="getStatusClass(scope.row.status)" />
              </template>
            </el-table-column>
            <el-table-column prop="id" label="ID" min-width="120">
              <template #default="scope">
                <div>
                  <div>#{{ scope.row.id }} {{ scope.row.source }} / {{ scope.row.platform }}</div>
                  <div>
                    <el-tag size="small" :type="getTagType(scope.row.status)">{{ scope.row.statusText }}</el-tag>
                    <span class="time-info">{{ scope.row.timeInfo }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="currentProcessor" label="处理人" width="120" />
            <el-table-column prop="space" label="协作空间" width="120" />
            <el-table-column prop="date" label="日期" width="160" />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <div class="total-info">总计 {{ totalCount }} 项</div>
            <el-pagination
              background
              layout="prev, pager, next"
              :total="totalCount"
              :page-size="pageSize"
              @current-change="handleCurrentChange"
            />
            <div class="page-size-selector">
              {{ pageSize }} 条/页
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 集成配置标签页 -->
      <el-tab-pane label="集成配置" name="integration">
        <empty-view />
      </el-tab-pane>

      <!-- 分派策略标签页 -->
      <el-tab-pane label="分派策略" name="dispatch">
        <div class="tab-content">
          <dispatch-strategy :space-id="spaceId" />
        </div>
      </el-tab-pane>

      <!-- 降噪配置标签页 -->
      <el-tab-pane label="降噪配置" name="noise">
        <div class="tab-content">
          <noise-reduction :space-id="spaceId" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 消息详情抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="currentMessage ? `#${currentMessage.id} ${currentMessage.source} / ${currentMessage.platform}` : '消息详情'"
      size="60%"
      direction="rtl"
      destroy-on-close
    >
      <detail-component :message-id="currentMessageId" v-if="drawerVisible" />
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import { getSpaceList } from '@/api/mock/spaceManagement'
import { getSpaceMetrics, getSpaceMessages } from '@/api/mock/spaceMetrics'
import DetailComponent from '../msgManagement/detail.vue'
import EmptyView from '../demo/emptyView.vue'
import DispatchStrategy from './components/DispatchStrategy/index.vue'
import NoiseReduction from './components/NoiseReduction/index.vue'

// 路由和导航
const route = useRoute()
const router = useRouter()

// 数据
const spaceId = computed(() => route.params.id)
const space = ref({})
const metrics = ref({
  mtta: { value: 0, unit: '分钟', trend: 'stable', change: 0 },
  mttr: { value: 0, unit: '分钟', trend: 'stable', change: 0 },
  incidentCount: { value: 0, trend: 'stable', change: 0 },
  noiseReduction: { value: 0, unit: '%', trend: 'stable', change: 0 }
})
const activeTab = ref('dispatch')
const messageList = ref([])
const totalCount = ref(0)
const pageSize = ref(10)
const selectedItems = ref([])
const drawerVisible = ref(false)
const currentMessageId = ref('')
const currentMessage = ref(null)

// 获取空间信息
const fetchSpaceInfo = async () => {
  try {
    const response = await getSpaceList()
    const spaceData = response.data.find(s => s.id.toString() === spaceId.value)
    if (spaceData) {
      space.value = spaceData
    } else {
      ElMessage.error('未找到该空间信息')
      router.push('/oncall/space/spacepageindex')
    }
  } catch (error) {
    console.error('获取空间信息失败', error)
    ElMessage.error('获取空间信息失败')
  }
}

// 获取空间指标
const fetchSpaceMetrics = async () => {
  try {
    const response = await getSpaceMetrics(parseInt(spaceId.value))
    metrics.value = response.data
  } catch (error) {
    console.error('获取空间指标失败', error)
    ElMessage.error('获取空间指标失败')
  }
}

// 获取空间消息列表
const fetchSpaceMessages = async () => {
  try {
    const response = await getSpaceMessages(parseInt(spaceId.value))
    messageList.value = response.data
    totalCount.value = messageList.value.length
  } catch (error) {
    console.error('获取空间消息列表失败', error)
    ElMessage.error('获取空间消息列表失败')
  }
}

// 获取趋势图标
const getTrendIcon = (trend) => {
  if (trend === 'up') return ArrowUp
  if (trend === 'down') return ArrowDown
  return Minus
}

// 获取趋势类名
const getTrendClass = (trend) => {
  if (trend === 'up') return 'trend-up'
  if (trend === 'down') return 'trend-down'
  return 'trend-stable'
}

// 获取趋势文本
const getTrendText = (trend, change) => {
  if (trend === 'stable') return '持平'
  return `${change}%`
}

// 获取趋势提示文本
const getTrendTooltip = (trend, change, metricName) => {
  if (trend === 'stable') {
    return `与上一周相比，${metricName}持平无变化`
  } else if (trend === 'up') {
    return `与上一周相比，${metricName}上升了${change}%`
  } else {
    return `与上一周相比，${metricName}下降了${change}%`
  }
}

// 获取状态类名
const getStatusClass = (status) => {
  switch (status) {
    case 'pending': return 'status-pending'
    case 'processing': return 'status-processing'
    case 'resolved': return 'status-resolved'
    case 'closed': return 'status-closed'
    default: return ''
  }
}

// 获取标签类型
const getTagType = (status) => {
  switch (status) {
    case 'pending': return 'danger'
    case 'processing': return 'warning'
    case 'resolved': return 'success'
    case 'closed': return 'info'
    default: return ''
  }
}

// 处理选择变更
const handleSelectionChange = (val) => {
  selectedItems.value = val
}

// 处理分页变更
const handleCurrentChange = (val) => {
  console.log(`切换到第${val}页`)
  // 实现分页切换逻辑
}

// 点击行处理
const handleRowClick = (row, column) => {
  // 如果点击的是选择框，不处理
  if (column.type === 'selection') {
    return
  }
  currentMessageId.value = row.id
  currentMessage.value = row
  drawerVisible.value = true
}

// 双击行时跳转到详情页
const handleRowDblClick = (row) => {
  navigateToDetail(row.id)
}

// 处理路由跳转到详情页
const navigateToDetail = (id) => {
  router.push(`/oncall/msg/detail/${id}`)
}

// 页面加载时获取数据
onMounted(async () => {
  await fetchSpaceInfo()
  await fetchSpaceMetrics()
  await fetchSpaceMessages()
})
</script>

<style scoped>
.space-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #2c3e50;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.space-title {
  margin: 0;
  font-size: 20px;
  color: #909399;
  font-weight: 400;
}

.space-id {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.team-tag {
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: rgba(103, 194, 58, 0.1);
  border: 1px solid #67c23a;
  color: #67c23a;
}

.metrics-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.metric-card {
  border-radius: 8px;
  padding: 16px;
  position: relative;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.mtta-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #fff;
}

.mttr-card {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: #fff;
}

.incident-card {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: #fff;
}

.noise-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.metric-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12px;
  font-weight: 500;
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.metric-unit {
  font-size: 16px;
  font-weight: normal;
  margin-left: 4px;
}

.metric-trend {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-top: auto;
}

.trend-wrapper {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  cursor: pointer;
}

.trend-up {
  color: #ffffff;
  font-weight: bold;
}

.trend-down {
  color: #ffffff;
  font-weight: bold;
}

.trend-stable {
  color: #ffffff;
  font-weight: bold;
}

.trend-wrapper .el-icon {
  margin-right: 4px;
}

.trend-up .el-icon {
  color: #ff4949;
}

.trend-down .el-icon {
  color: #13ce66;
}

.trend-stable .el-icon {
  color: #ffffff;
}

.setup-link {
  color: #ffffff;
  text-decoration: underline;
  cursor: pointer;
  margin-left: 4px;
  font-weight: bold;
}

.detail-tabs {
  margin-top: 20px;
}

.tab-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
}

.status-line {
  width: 4px;
  height: 100%;
  border-radius: 2px;
}

.status-pending {
  background-color: #f56c6c;
}

.status-processing {
  background-color: #e6a23c;
}

.status-resolved {
  background-color: #67c23a;
}

.status-closed {
  background-color: #909399;
}

.time-info {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.total-info, .page-size-selector {
  color: #606266;
  font-size: 14px;
}
</style>

<script>
export default {
  name: 'SpaceDetailPage'
}
</script>
