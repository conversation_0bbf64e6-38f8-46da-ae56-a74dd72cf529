<template>
  <!-- 抽屉模式 -->
  <el-drawer
    v-if="!isPage"
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="'告警详情 #' + alertData.id"
    size="100%"
    direction="rtl"
    :destroy-on-close="false"
    :before-close="handleClose"
    :with-header="true"
    custom-class="alert-detail-drawer"
  >
    <template #header>
      <div class="drawer-header">
        <div class="drawer-title">告警详情 #{{ alertData.id }}</div>
        <el-button 
          class="close-btn" 
          type="text" 
          @click="handleClose"
          icon="el-icon-close">
        </el-button>
      </div>
    </template>
    
    <!-- 抽屉内容 -->
    <div class="alert-detail-container" v-if="alertData">
      <!-- 头部状态区域 -->
      <div class="status-bar">
        <div class="alert-title">
          {{ alertData.source }} / {{ alertData.platform }}
        </div>
        <!-- 操作按钮区域 -->
        <div class="action-bar">
            <div class="buttons">
            <el-tooltip content="复制详情页链接" placement="top">
                <el-button 
                type="primary" 
                size="small"
                @click="copyDetailLink" 
                class="copy-btn">
                复制链接
                </el-button>
            </el-tooltip>
            <!-- <el-button type="text" size="small" class="comment-btn" @click="goToDetail">
                查看独立页面
            </el-button> -->
            </div>
        </div>
        <div class="status-tags">
          <span class="tag-prefix">ID:</span>
          <span class="id-value">{{ alertData.id }}</span>
          <span class="tag-divider">|</span>
          <el-tag :type="getTagType(alertData.status)" size="small">{{ alertData.statusText }}</el-tag>
          <span class="tag-divider">|</span>
          <span class="time-info">共持续 {{ alertData.timeInfo }}</span>
        </div>
      </div>
      

      <!-- 2x2卡片信息区域 -->
      <div class="card-grid">
        <!-- 第一行卡片 -->
        <div class="card-row">
          <!-- 发生时间卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-time"></i>
              <span>发生时间</span>
            </div>
            <div class="card-content">
              <div class="card-main-text">{{ alertData.timeInfo }} 前</div>
              <div class="card-sub-text">关闭于2小时27分前</div>
            </div>
          </div>
          
          <!-- 告警来源卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-data-line"></i>
              <span>告警来源</span>
            </div>
            <div class="card-content">
              <div class="card-main-text">阿里云监控 CM 指标</div>
              <div class="card-sub-text"><span class="tag-special">专属</span></div>
            </div>
          </div>
        </div>
        
        <!-- 第二行卡片 -->
        <div class="card-row">
          <!-- 所属故障卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-warning"></i>
              <span>所属故障</span>
            </div>
            <div class="card-content">
              <div class="card-main-text">{{ alertData.incident || '无关联故障' }}</div>
              <div class="card-sub-text">{{ alertData.incidentDesc || '' }}</div>
            </div>
          </div>
          
          <!-- 告警描述卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-document"></i>
              <span>告警描述</span>
            </div>
            <div class="card-content">
              <div class="card-main-text description-text">{{ alertData.description || '暂无描述' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 标签页区域 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab">
          <!-- 告警标签/参数标签页 -->
          <el-tab-pane label="告警标签" name="parameters">
            <div class="tab-content">
              <div class="parameters-section">
                <div class="section-header">
                  <div class="section-title">标签信息</div>
                  <div class="section-subtitle">共{{ alertData.parameters ? alertData.parameters.length : 0 }}项</div>
                </div>
                <div class="section-content parameters" v-if="alertData.parameters && alertData.parameters.length > 0">
                  <div class="parameter-grid">
                    <div class="parameter-item" v-for="(param, index) in alertData.parameters" :key="index">
                      <div class="param-label">{{ param.label }}</div>
                      <div class="param-value">{{ param.value }}</div>
                    </div>
                  </div>
                </div>
                <div class="empty-data" v-else>暂无参数数据</div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 时间线标签页 -->
          <el-tab-pane label="时间线" name="timeline">
            <div class="tab-content">
              <div class="timeline-section">
                <div class="timeline-item" v-for="(index) in 2" :key="index">
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <div class="timeline-header">
                      <div class="user-tag" v-if="index === 1">
                        <span class="avatar-tag">X</span>
                        <span>XSS-GOAT</span>
                      </div>
                      <div class="user-tag system-tag" v-else>
                        <span class="avatar-tag system">系</span>
                        <span>系统</span>
                      </div>
                      <div class="timeline-action" v-if="index === 1">
                        手动关闭该告警
                      </div>
                      <div class="timeline-action" v-else>
                        收到新的告警事件，系统自动处理告警，严重程度为 
                        <span class="level-tag">Info</span>
                      </div>
                      <div class="timeline-time">
                        {{ index === 1 ? '2025-04-23 15:04:12' : '2025-04-10 11:46:31' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 关联事件标签页 -->
          <el-tab-pane label="关联事件" name="related">
            <div class="tab-content">
              <div class="json-section">
                <pre class="json-content">{{ JSON.stringify(alertData, null, 2) }}</pre>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </el-drawer>
  
  <!-- 页面模式 -->
  <div v-else class="alert-detail-page">
    <div class="page-header">
      <div class="header-title">告警详情 #{{ alertData.id }}</div>
    </div>
    
    <div class="alert-detail-container" v-if="alertData">
      <!-- 头部状态区域 -->
      <div class="status-bar">
        <div class="alert-title">
          {{ alertData.source }} / {{ alertData.platform }}
        </div>
        <div class="status-tags">
          <span class="tag-prefix">ID:</span>
          <span class="id-value">{{ alertData.id }}</span>
          <span class="tag-divider">|</span>
          <el-tag :type="getTagType(alertData.status)" size="small">{{ alertData.statusText }}</el-tag>
          <span class="tag-divider">|</span>
          <span class="time-info">共持续 {{ alertData.timeInfo }}</span>
          
          <div class="action-buttons">
            <el-tooltip content="复制详情页链接" placement="top">
              <el-button 
                type="primary" 
                size="small"
                @click="copyDetailLink" 
                class="copy-btn">
                复制链接
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 2x2卡片信息区域 -->
      <div class="card-grid">
        <!-- 第一行卡片 -->
        <div class="card-row">
          <!-- 发生时间卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-time"></i>
              <span>发生时间</span>
            </div>
            <div class="card-content">
              <div class="card-main-text">{{ alertData.timeInfo }} 前</div>
              <div class="card-sub-text">关闭于2小时27分前</div>
            </div>
          </div>
          
          <!-- 告警来源卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-data-line"></i>
              <span>告警来源</span>
            </div>
            <div class="card-content">
              <div class="card-main-text">阿里云监控 CM 指标</div>
              <div class="card-sub-text"><span class="tag-special">专属</span></div>
            </div>
          </div>
        </div>
        
        <!-- 第二行卡片 -->
        <div class="card-row">
          <!-- 所属故障卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-warning"></i>
              <span>所属故障</span>
            </div>
            <div class="card-content">
              <div class="card-main-text">{{ alertData.incident || '无关联故障' }}</div>
              <div class="card-sub-text">{{ alertData.incidentDesc || '' }}</div>
            </div>
          </div>
          
          <!-- 告警描述卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-document"></i>
              <span>告警描述</span>
            </div>
            <div class="card-content">
              <div class="card-main-text description-text">{{ alertData.description || '暂无描述' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 标签页区域 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab">
          <!-- 告警标签/参数标签页 -->
          <el-tab-pane label="告警标签" name="parameters">
            <div class="tab-content">
              <div class="parameters-section">
                <div class="section-header">
                  <div class="section-title">标签信息</div>
                  <div class="section-subtitle">共{{ alertData.parameters ? alertData.parameters.length : 0 }}项</div>
                </div>
                <div class="section-content parameters" v-if="alertData.parameters && alertData.parameters.length > 0">
                  <div class="parameter-grid">
                    <div class="parameter-item" v-for="(param, index) in alertData.parameters" :key="index">
                      <div class="param-label">{{ param.label }}</div>
                      <div class="param-value">{{ param.value }}</div>
                    </div>
                  </div>
                </div>
                <div class="empty-data" v-else>暂无参数数据</div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 时间线标签页 -->
          <el-tab-pane label="时间线" name="timeline">
            <div class="tab-content">
              <div class="timeline-section">
                <div class="timeline-item" v-for="(index) in 2" :key="index">
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <div class="timeline-header">
                      <div class="user-tag" v-if="index === 1">
                        <span class="avatar-tag">X</span>
                        <span>XSS-GOAT</span>
                      </div>
                      <div class="user-tag system-tag" v-else>
                        <span class="avatar-tag system">系</span>
                        <span>系统</span>
                      </div>
                      <div class="timeline-action" v-if="index === 1">
                        手动关闭该告警
                      </div>
                      <div class="timeline-action" v-else>
                        收到新的告警事件，系统自动处理告警，严重程度为 
                        <span class="level-tag">Info</span>
                      </div>
                      <div class="timeline-time">
                        {{ index === 1 ? '2025-04-23 15:04:12' : '2025-04-10 11:46:31' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 关联事件标签页 -->
          <el-tab-pane label="关联事件" name="related">
            <div class="tab-content">
              <div class="json-section">
                <pre class="json-content">{{ JSON.stringify(alertData, null, 2) }}</pre>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { alertDetailsData } from '@/api/mock/alertDetail'

const router = useRouter()
const route = useRoute()

const props = defineProps({
  alertId: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  },
  isPage: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'close'])

const activeTab = ref('parameters')

// 获取告警数据
const alertData = computed(() => {
  // 根据模式选择ID来源
  const id = props.isPage ? route.params.id : props.alertId
  return alertDetailsData.find(item => item.id === id) || {}
})

// 复制链接功能 - 指向独立页面
const copyDetailLink = () => {
  const url = window.location.origin + '/#/alert-detail/' + alertData.value.id
  navigator.clipboard.writeText(url).then(() => {
    ElMessage({
      message: '链接已复制到剪贴板',
      type: 'success',
      duration: 2000
    })
  }).catch(() => {
    ElMessage({
      message: '复制失败，请手动复制',
      type: 'error',
      duration: 2000
    })
  })
}

// 跳转到独立页面
const goToDetail = () => {
  router.push(`/alert-detail/${alertData.value.id}`)
  handleClose()
}

// 返回上一页 (页面模式)
const goBack = () => {
  router.back()
}

// 获取状态对应的标签类型
const getTagType = (status) => {
  const typeMap = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return typeMap[status] || 'warning'
}

// 操作方法
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

watch(() => props.visible, (newVal) => {
  if (newVal) {
    activeTab.value = 'parameters'
  }
})

onMounted(() => {
  // 页面模式下的初始化
  if (props.isPage && (!alertData.value || !alertData.value.id)) {
    ElMessage({
      message: '未找到告警信息',
      type: 'error',
      duration: 2000
    })
    setTimeout(() => {
      router.push('/oncall/msg/msgpageindex')
    }, 2000)
  }
})
</script>

<style scoped>
.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.drawer-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.close-btn {
  color: #606266;
  font-size: 20px;
  transition: all 0.3s;
}

.close-btn:hover {
  color: #409eff;
  transform: rotate(90deg);
}

.alert-detail-drawer :deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 16px 20px;
  background-color: #f4f5f7;
  border-bottom: 1px solid #e6e8eb;
  font-weight: 600;
  color: #303133;
}

.alert-detail-drawer :deep(.el-drawer__body) {
  background-color: #fff;
  color: #303133;
  padding: 0;
}

/* 页面模式样式 */
.alert-detail-page {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background-color: #f4f5f7;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e6e8eb;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
  margin-right: 16px;
}

.back-button:hover {
  color: #409eff;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.action-buttons {
  margin-left: auto;
}

/* 共用内容样式 */
.alert-detail-container {
  padding: 16px;
  position: relative;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 页面模式调整 */
.alert-detail-page .alert-detail-container {
  padding: 20px;
  flex: 1;
}

.alert-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.status-bar {
  margin-bottom: 16px;
}

.alert-detail-page .status-bar {
  margin-bottom: 20px;
}

.status-tags {
  display: flex;
  align-items: center;
  gap: 12px;
}

.alert-detail-page .status-tags {
  flex-wrap: wrap;
}

.tag-prefix, .tag-divider {
  color: #909399;
  font-size: 13px;
}

.id-value {
  color: #303133;
  font-weight: 500;
  font-size: 13px;
}

.time-info {
  color: #606266;
  font-size: 13px;
}

/* 卡片网格布局 */
.card-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.card-row {
  display: flex;
  gap: 16px;
}

.info-card {
  flex: 1;
  min-width: 0;
  background-color: #f7f8fa;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.card-header i {
  font-size: 16px;
  color: #909399;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-main-text {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
  word-break: break-all;
}

.card-sub-text {
  color: #909399;
  font-size: 12px;
}

.description-text {
  max-height: 80px;
  overflow-y: auto;
  line-height: 1.5;
}

/* 标签页样式 */
.tabs-section {
  margin-bottom: 16px;
}

.alert-detail-page .tabs-section {
  flex: 1;
}

.tab-content {
  padding: 16px 0;
}

/* 参数区域样式 */
.parameters-section {
  background-color: #f7f8fa;
  border-radius: 4px;
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.section-subtitle {
  font-size: 12px;
  color: #909399;
}

.parameter-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 12px;
}

.alert-detail-page .parameter-grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.parameter-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.param-label {
  font-size: 12px;
  color: #909399;
}

.param-value {
  font-size: 13px;
  color: #303133;
  word-break: break-all;
}

/* 时间线样式 */
.timeline-section {
  margin-bottom: 16px;
  border-left: 2px solid #e6e8eb;
  padding-left: 20px;
}

.timeline-item {
  position: relative;
  padding-bottom: 16px;
}

.timeline-dot {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #409eff;
  left: -25px;
  top: 6px;
}

.timeline-content {
  flex: 1;
}

.timeline-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  flex-wrap: wrap;
  gap: 12px;
}

.user-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  min-width: 80px;
}

.avatar-tag {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  background-color: #5e6ebf;
  color: #fff;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  margin-right: 4px;
}

.avatar-tag.system {
  background-color: #909399;
}

.system-tag {
  color: #606266;
}

.timeline-action {
  color: #303133;
  font-size: 13px;
  flex: 1;
}

.level-tag {
  display: inline-block;
  background-color: #ecf5ff;
  color: #409eff;
  padding: 0 6px;
  height: 20px;
  line-height: 20px;
  border-radius: 2px;
  font-size: 12px;
}

.timeline-time {
  color: #909399;
  font-size: 12px;
  min-width: 140px;
  text-align: right;
}

/* JSON展示区域 */
.json-section {
  background-color: #f7f8fa;
  border-radius: 4px;
  padding: 16px;
}

.json-content {
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #303133;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.empty-data {
  padding: 16px;
  text-align: center;
  color: #909399;
  background-color: #f7f8fa;
  border-radius: 4px;
  font-size: 12px;
}

.tag-special {
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
  color: #606266;
}

/* 操作按钮区域 */
.action-bar {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.buttons {
  display: flex;
  gap: 8px;
}

.copy-btn, .comment-btn {
  font-size: 12px;
}

:deep(.el-button--default) {
  background-color: #fff;
  border-color: #dcdfe6;
  color: #606266;
}

:deep(.el-button--default:hover) {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-tag) {
  background-color: transparent;
}

:deep(.el-tag--warning) {
  color: #e6a23c;
  border-color: #e6a23c;
}

:deep(.el-tag--primary) {
  color: #409eff;
  border-color: #409eff;
}

:deep(.el-tag--success) {
  color: #67c23a;
  border-color: #67c23a;
}

:deep(.el-tag--info) {
  color: #909399;
  border-color: #909399;
}

:deep(.el-button--text) {
  color: #7b67ee;
}

:deep(.el-button--text:hover) {
  color: #9186eb;
}

:deep(.el-tabs__item) {
  font-size: 14px;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
}

:deep(.el-tabs__active-bar) {
  background-color: #409eff;
}

@media (max-width: 768px) {
  .card-row {
    flex-direction: column;
  }
  
  .parameter-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}
</style> 