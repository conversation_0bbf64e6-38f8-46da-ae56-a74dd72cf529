<template>
  <div class="message-detail-container">
    <!-- 头部区域 -->
    <div class="detail-header">
      <div class="left-actions">
        <div class="status-info">
          <span class="warning-text" v-if="message.status === 'pending'">Warning</span>
          <span class="processing-text" v-else-if="message.status === 'processing'">处理中</span>
          <span class="resolved-text" v-else-if="message.status === 'resolved'">已解决</span>
          <span class="closed-text" v-else-if="message.status === 'closed'">已关闭</span>
          <span class="info-divider">|</span>
          <span class="id-info">ID:{{ message.id }}</span>
          <span class="info-divider">|</span>
          <span class="time-info">{{ message.timeInfo }}</span>
          <span class="info-divider">|</span>
          <span class="processor-info">测试告警服务</span>
        </div>
        <div class="status-tag">
          <el-tag :type="getTagType(message.status)" size="medium">{{ message.statusText }}</el-tag>
        </div>
      </div>
      <div class="right-actions"></div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="main-actions">
      <el-button plain @click="handleClaim">认领</el-button>
      <el-button plain @click="handleClose">关闭</el-button>
      <el-dropdown @command="handleUpgrade">
        <el-button plain>
          升级 <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="normal">Info</el-dropdown-item>
            <el-dropdown-item command="urgent">Warning</el-dropdown-item>
            <el-dropdown-item command="critical">Critical</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="spacer"></div>
      <el-tooltip content="复制详情页链接" placement="top">
        <el-button 
          type="primary" 
          icon="el-icon-document-copy" 
          @click="copyDetailLink" 
          class="copy-btn" 
          size="medium">
          复制链接
        </el-button>
      </el-tooltip>
    </div>

    <!-- 详情区域 -->
    <el-tabs v-model="activeTab" class="detail-tabs">
      <el-tab-pane label="故障概述" name="overview">
        <div class="detail-section">
          <div class="section-title">故障概述</div>
          <div class="section-content">{{ message.source }} / {{ message.platform }}</div>
        </div>
        <div class="detail-section">
          <div class="section-title">故障描述</div>
          <div class="section-content description">
            {{ detailInfo.description || '暂无描述' }}
          </div>
        </div>
        <div class="detail-section">
          <div class="section-title">故障影响</div>
          <div class="section-content description">
            {{ detailInfo.impact || '暂无影响描述' }}
          </div>
        </div>
        <div class="detail-section">
          <div class="section-title">故障参数 共{{ detailInfo.parameters ? detailInfo.parameters.length : 0 }}项</div>
          <div class="section-content parameters" v-if="detailInfo.parameters && detailInfo.parameters.length > 0">
            <el-descriptions :column="1" border>
              <el-descriptions-item 
                v-for="(param, index) in detailInfo.parameters" 
                :key="index" 
                :label="param.label">
                {{ param.value }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="empty-data" v-else>暂无参数数据</div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="关联告警" name="relations">
        <div class="related-alerts">
          <el-table :data="relatedAlerts" style="width: 100%" @row-click="handleRelatedAlertClick">
            <el-table-column prop="id" label="告警编号" width="150" />
            <el-table-column prop="source" label="来源" width="150" />
            <el-table-column prop="time" label="发生时间" width="180" />
            <el-table-column prop="platform" label="平台" width="150" />
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="时间线" name="timeline">
        <div class="timeline-container">
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :type="activity.type"
              :color="activity.color"
              :timestamp="activity.timestamp"
            >
              <div class="timeline-user">
                <el-tag size="small" effect="plain" type="info" class="user-tag">{{ activity.user }}</el-tag>
              </div>
              <div class="timeline-content">{{ activity.content }}</div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-tab-pane>
      <el-tab-pane label="历史变更" name="history">
        <div class="history-container">
          <div class="history-item" v-for="(item, index) in historyChanges" :key="index">
            <div class="history-time">{{ item.time }}</div>
            <div class="history-user">{{ item.user }}</div>
            <div class="history-content">{{ item.content }}</div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 底部操作区域 -->
    <div class="bottom-actions">
      <el-button type="danger" plain @click="handleMergeToIssue">合并至故障</el-button>
      <el-button type="success" plain @click="handleQuickResolve">快速屏蔽</el-button>
    </div>

    <!-- 告警详情抽屉 -->
    <el-drawer
      v-model="alertDetailVisible"
      :size="'40%'"
      :title="`告警详情 - ${selectedAlertId}`"
      :destroy-on-close="true"
      direction="rtl"
      class="alert-detail-drawer"
    >
      <AlertDetail 
        v-if="alertDetailVisible" 
        :alertId="selectedAlertId" 
        v-model:visible="alertDetailVisible"
        :isPage="false"
        @close="alertDetailVisible = false"
      />
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { messageListData } from '@/api/mock/messageList'
import { 
  alertDetailsData, 
  alertActivitiesData, 
  alertHistoryData, 
  relatedAlertsData 
} from '@/api/mock/alertDetail'
import { ElMessage } from 'element-plus'
import AlertDetail from './AlertDetail.vue'

const props = defineProps({
  messageId: {
    type: String,
    default: ''
  }
})

const route = useRoute()
const activeTab = ref('overview')
const alertDetailVisible = ref(false)
const selectedAlertId = ref('')

// 根据路由参数或组件props获取消息ID
const msgId = computed(() => {
  return props.messageId || route.params.id
})

// 获取消息信息
const message = computed(() => {
  const msg = messageListData.find(item => item.id === msgId.value)
  return msg || {
    id: '',
    source: '',
    platform: '',
    status: 'pending',
    statusText: '未知',
    space: '',
    timeInfo: ''
  }
})

// 获取详情信息
const detailInfo = computed(() => {
  // 尝试从告警详情数据中获取
  const detailData = alertDetailsData.find(item => item.id === msgId.value)
  if (detailData) {
    return detailData
  }
  // 默认空数据
  return {
    description: '',
    impact: '',
    parameters: []
  }
})

// 获取关联告警
const relatedAlerts = computed(() => {
  return relatedAlertsData[msgId.value] || []
})

// 获取活动数据
const activities = computed(() => {
  return alertActivitiesData[msgId.value] || []
})

// 获取历史变更数据
const historyChanges = computed(() => {
  return alertHistoryData[msgId.value] || []
})

// 点击关联告警，打开告警详情抽屉
const handleRelatedAlertClick = (row) => {
  selectedAlertId.value = row.id
  alertDetailVisible.value = true
}

// 复制详情链接
const copyDetailLink = () => {
  const url = window.location.origin + `/oncall/msg/alert/detail/${msgId.value}`
  navigator.clipboard.writeText(url).then(() => {
    ElMessage({
      message: '链接已复制到剪贴板',
      type: 'success',
      duration: 2000
    })
  }).catch(() => {
    ElMessage({
      message: '复制失败，请手动复制',
      type: 'error',
      duration: 2000
    })
  })
}

// 获取状态对应的标签类型
const getTagType = (status) => {
  const typeMap = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return typeMap[status] || 'warning'
}

// 操作方法
const handleClaim = () => {
  console.log('认领消息:', msgId.value)
}

const handleClose = () => {
  console.log('关闭消息:', msgId.value)
}

const handleUpgrade = (command) => {
  console.log('升级消息:', msgId.value, '级别:', command)
}

const handleMergeToIssue = () => {
  console.log('合并至故障:', msgId.value)
}

const handleQuickResolve = () => {
  console.log('快速屏蔽:', msgId.value)
}

onMounted(() => {
  console.log('详情组件已加载，消息ID:', msgId.value)
})
</script>

<style scoped>
.message-detail-container {
  padding: 20px;
  position: relative;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  background-color: #f9f9f9;
  padding: 8px 12px;
  border-radius: 4px;
}

.left-actions {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.info-divider {
  color: #dcdfe6;
  font-weight: 300;
}

.warning-text {
  color: #e6a23c;
  font-weight: 600;
}

.processing-text {
  color: #409eff;
  font-weight: 600;
}

.resolved-text {
  color: #67c23a;
  font-weight: 600;
}

.closed-text {
  color: #909399;
  font-weight: 600;
}

.id-info {
  color: #606266;
}

.time-info {
  color: #909399;
}

.processor-info {
  color: #606266;
}

.right-actions {
  display: flex;
  align-items: center;
}

.main-actions {
  display: flex;
  gap: 12px;
  margin-top: 12px;
  margin-bottom: 16px;
  align-items: center;
}

.spacer {
  flex: 1;
}

.copy-btn {
  font-size: 14px;
  background-color: #409eff;
  border: 1px solid #409eff;
  color: #ffffff;
  font-weight: 500;
  padding: 8px 15px;
}

.copy-btn:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
  color: #ffffff;
}

.detail-tabs {
  margin-top: 10px;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 12px;
}

.section-content {
  font-size: 14px;
  color: #303133;
}

.description {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 60px;
}

.parameters {
  margin-top: 12px;
}

.timeline-container {
  padding: 12px;
}

.timeline-user {
  margin-bottom: 6px;
}

.timeline-content {
  font-size: 14px;
  color: #303133;
}

.history-container {
  padding: 12px;
}

.history-item {
  display: flex;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.history-time {
  width: 180px;
  color: #909399;
  font-size: 13px;
}

.history-user {
  width: 120px;
  color: #409EFF;
  font-size: 13px;
}

.history-content {
  flex: 1;
  font-size: 13px;
}

.bottom-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.empty-data {
  padding: 24px;
  text-align: center;
  color: #909399;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.related-alerts .el-table__row {
  cursor: pointer;
}

.related-alerts .el-table__row:hover {
  background-color: #f0f7ff !important;
}
</style>

<script>
export default {
  name: 'MessageDetailComponent'
}
</script> 