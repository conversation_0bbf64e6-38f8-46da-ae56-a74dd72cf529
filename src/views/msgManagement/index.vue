<template>
  <div class="msg-management-container">
    <!-- 顶部操作栏 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button type="primary" plain @click="assignToMe">分派给我</el-button>
        <el-button plain @click="toMyWorkspace">我的团队空间</el-button>
        <el-dropdown @command="handleProcessStatusChange">
          <el-button plain>
            处理进度 <i class="el-icon-arrow-down"></i>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in processStatusOptions" :key="item.value" :command="item.value">
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown @command="handleDateRangeChange">
          <el-button plain>
            最近 30 天 <i class="el-icon-arrow-down"></i>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in dateRangeOptions" :key="item.value" :command="item.value">
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-input 
          placeholder="请输入故障ID" 
          v-model="searchId" 
          prefix-icon="el-icon-search"
          class="id-search"
        />
      </div>
      <div class="right-actions">
        <el-button @click="showFilterPanel">+ 筛选条件</el-button>
        <el-button type="primary" @click="createMsg">创建</el-button>
        <el-button icon="el-icon-setting" circle />
      </div>
    </div>

    <!-- 筛选面板 -->
    <div v-if="showFilter" class="filter-panel">
      <el-input placeholder="输入内容以搜索" v-model="filterKeyword" />
      <div class="filter-items">
        <div class="filter-item" @click="selectFilter('故障概述')">故障概述</div>
        <div class="filter-item" @click="selectFilter('与我相关')">与我相关</div>
        <div class="filter-item" @click="selectFilter('发起人员')">发起人员</div>
        <div class="filter-item" @click="selectFilter('处理人员')">处理人员</div>
        <div class="filter-item" @click="selectFilter('认领人员')">认领人员</div>
        <div class="filter-item" @click="selectFilter('关闭人员')">关闭人员</div>
        <div class="filter-item" @click="selectFilter('团队空间')">团队空间</div>
        <div class="filter-item" @click="selectFilter('覆盖状态')">覆盖状态</div>
        <div class="filter-item" @click="selectFilter('故障概率')">故障概率</div>
        <div class="filter-item" @click="selectFilter('自定义字段')">自定义字段</div>
      </div>
    </div>

    <!-- 消息列表 -->
    <el-table
      :data="messageList"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      @row-dblclick="handleRowDblClick"
    >
      <el-table-column type="selection" width="40" />
      <el-table-column width="5">
        <template #default="scope">
          <div class="status-line" :class="getStatusClass(scope.row.status)" />
        </template>
      </el-table-column>
      <el-table-column prop="id" label="ID" min-width="120">
        <template #default="scope">
          <div>
            <div>#{{ scope.row.id }} {{ scope.row.source }} / {{ scope.row.platform }}</div>
            <div>
              <el-tag size="small" :type="getTagType(scope.row.status)">{{ scope.row.statusText }}</el-tag>
              <span class="time-info">{{ scope.row.timeInfo }}</span>
              <span class="relation-info">关联告警：{{ scope.row.relationCount }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="所属空间" width="120">
        <template #default="scope">
          <div class="space-info">
            <el-tag size="small" effect="plain" :type="getSpaceTagType(scope.row.space)">{{ scope.row.space }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="当前处理人" width="150">
        <template #default="scope">
          <div class="processor-info">
            <el-tag size="small" effect="plain" type="info">zhangyupeng2</el-tag>
            <!-- <div class="processor-name">{{ scope.row.currentProcessor }}</div> -->
            <div class="service-provider">
              <el-icon><el-icon-service /></el-icon>
              <span>{{ scope.row.space }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="日期" width="160" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <div class="total-info">总计 {{ totalCount }} 项</div>
      <el-pagination
        background
        layout="prev, pager, next"
        :total="totalCount"
        :page-size="pageSize"
        @current-change="handleCurrentChange"
      />
      <div class="page-size-selector">
        {{ pageSize }} 条/页
      </div>
    </div>

    <!-- 多选操作栏 -->
    <div v-if="selectedItems.length > 0" class="multi-select-actions">
      <div class="selected-count">已选{{ selectedItems.length }}项</div>
      <div class="action-buttons">
        <el-button 
          plain 
          @click="handleClaim" 
          :disabled="!canClaim"
        >认领</el-button>
        <el-button 
          plain 
          @click="handleResolve" 
          :disabled="!canResolve"
        >暂缓</el-button>
        <el-button 
          plain 
          @click="handleClose" 
          :disabled="!canClose"
        >关闭</el-button>
        <el-button 
          plain 
          @click="handleMerge" 
          :disabled="selectedItems.length < 2"
        >合并</el-button>
      </div>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="currentMessage ? `#${currentMessage.id} ${currentMessage.source} / ${currentMessage.platform}` : '消息详情'"
      size="60%"
      direction="rtl"
      destroy-on-close
    >
      <detail-component :message-id="currentMessageId" v-if="drawerVisible" />
    </el-drawer>

    <!-- 告警详情抽屉 -->
    <AlertDetail 
      v-if="alertDetailVisible" 
      v-model:visible="alertDetailVisible" 
      :alertId="selectedAlertId"
      @close="alertDetailVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { messageListData } from '@/api/mock/messageList'
import DetailComponent from './detail.vue'
import AlertDetail from './AlertDetail.vue'

const router = useRouter()

// 数据
const searchId = ref('')
const showFilter = ref(false)
const filterKeyword = ref('')
const totalCount = ref(messageListData.length)
const pageSize = ref(10)
const selectedItems = ref([])
const drawerVisible = ref(false)
const currentMessageId = ref('')
const currentMessage = ref(null)
const alertDetailVisible = ref(false)
const selectedAlertId = ref('')

// 处理状态选项
const processStatusOptions = ref([
  { label: '全部', value: 'all' },
  { label: '待处理', value: 'pending' },
  { label: '处理中', value: 'processing' },
  { label: '已解决', value: 'resolved' },
  { label: '已关闭', value: 'closed' },
])

// 日期范围选项
const dateRangeOptions = ref([
  { label: '最近7天', value: '7d' },
  { label: '最近30天', value: '30d' },
  { label: '最近90天', value: '90d' },
  { label: '自定义范围', value: 'custom' },
])

// 消息列表 - 从mock数据导入
const messageList = ref(messageListData)

// 计算多选操作按钮状态
const canClaim = computed(() => {
  return selectedItems.value.some(item => ['pending'].includes(item.status))
})

const canResolve = computed(() => {
  return selectedItems.value.some(item => ['pending', 'processing'].includes(item.status))
})

const canClose = computed(() => {
  return selectedItems.value.some(item => ['pending', 'processing', 'resolved'].includes(item.status))
})

// 方法
const assignToMe = () => {
  // 实现分派给我的逻辑
  console.log('分派给我')
}

const toMyWorkspace = () => {
  // 实现进入我的团队空间的逻辑
  console.log('进入我的团队空间')
}

const handleProcessStatusChange = (command) => {
  console.log('处理状态变更为:', command)
  // 实现处理状态筛选逻辑
}

const handleDateRangeChange = (command) => {
  console.log('日期范围变更为:', command)
  // 实现日期范围筛选逻辑
}

const showFilterPanel = () => {
  showFilter.value = !showFilter.value
}

const selectFilter = (filter) => {
  console.log('选择筛选条件:', filter)
  // 实现筛选条件选择逻辑
}

const createMsg = () => {
  console.log('创建新消息')
  // 实现创建消息的逻辑
}

const handleSelectionChange = (val) => {
  selectedItems.value = val
  console.log('已选中项:', selectedItems.value)
}

const handleCurrentChange = (val) => {
  console.log(`切换到第${val}页`)
  // 实现分页切换逻辑
}

// 点击行处理
const handleRowClick = (row, column) => {
  // 如果点击的是选择框，不处理
  if (column.type === 'selection') {
    return
  }
  currentMessageId.value = row.id
  currentMessage.value = row
  drawerVisible.value = true
}

// 双击行时跳转到详情页
const handleRowDblClick = (row) => {
  navigateToDetail(row.id)
}

// 处理路由跳转到详情页
const navigateToDetail = (id) => {
  router.push(`/oncall/msg/detail/${id}`)
}

const getStatusClass = (status) => {
  const statusMap = {
    pending: 'status-pending',
    processing: 'status-processing',
    resolved: 'status-resolved',
    closed: 'status-closed'
  }
  return statusMap[status] || 'status-pending'
}

const getTagType = (status) => {
  const typeMap = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return typeMap[status] || 'warning'
}

// 根据空间名称返回不同的标签类型（颜色）
const getSpaceTagType = (space) => {
  const spaceTypeMap = {
    '研发空间': 'success',
    '测试空间': 'warning',
    '运维空间': 'primary',
    'SRE': 'danger',
    // 'SRE': 'info'
  }
  return spaceTypeMap[space] || 'info'
}

// 多选操作处理函数
const handleClaim = () => {
  console.log('认领操作:', selectedItems.value)
  // 实现认领逻辑
}

const handleResolve = () => {
  console.log('智能处理操作:', selectedItems.value)
  // 实现智能处理逻辑
}

const handleClose = () => {
  console.log('关闭操作:', selectedItems.value)
  // 实现关闭逻辑
}

const handleMerge = () => {
  console.log('合并操作:', selectedItems.value)
  // 实现合并逻辑
}

onMounted(() => {
  // 初始化数据加载
  console.log('消息管理页面已加载')
})
</script>

<style scoped>
.msg-management-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  position: relative;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.id-search {
  width: 200px;
}

.filter-panel {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.filter-items {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
}

.filter-item {
  padding: 8px 16px;
  margin: 4px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.filter-item:hover {
  background-color: #f0f2f5;
}

.status-line {
  width: 4px;
  height: 40px;
  border-radius: 2px;
}

.status-pending {
  background-color: #e6a23c;
}

.status-processing {
  background-color: #409eff;
}

.status-resolved {
  background-color: #67c23a;
}

.status-closed {
  background-color: #909399;
}

.time-info, .relation-info {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.processor-info {
  display: flex;
  flex-direction: column;
}

.processor-name {
  margin: 4px 0;
  font-size: 14px;
}

.service-provider {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.service-provider i {
  margin-right: 4px;
}

.space-info {
  display: flex;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.total-info, .page-size-selector {
  color: #606266;
  font-size: 14px;
}

/* 表格行鼠标悬停样式 */
:deep(.el-table__row) {
  cursor: pointer;
}

/* 多选操作栏样式 */
.multi-select-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.selected-count {
  color: #303133;
  margin-right: 16px;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-button {
  margin-left: 0;
  background-color: #f5f7fa;
  color: #303133;
  border: 1px solid #dcdfe6;
}

.action-buttons .el-button:hover {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
  color: #409eff;
}

.action-buttons .el-button:disabled {
  background-color: #f5f7fa;
  color: #c0c4cc;
  border-color: #e4e7ed;
}
</style>

<script>
export default {
  name: 'MsgManagementPage'
}
</script>
