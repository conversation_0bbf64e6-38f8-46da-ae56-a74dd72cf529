<template>
  <div class="detail-integration-container">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" icon="ArrowLeft" size="small">返回</el-button>
        <h2 class="page-title">{{ integrationData.name || '集成详情' }}</h2>
      </div>
      <div class="header-actions">
        <el-switch
          v-model="integrationData.status"
          @change="toggleStatus"
          :active-value="'enabled'"
          :inactive-value="'disabled'">
        </el-switch>
        <span class="status-text">{{ integrationData.status === 'enabled' ? '启用' : '禁用' }}</span>
      </div>
    </div>

    <!-- 添加标签栏导航 -->
    <TabNav v-model:activeTab="activeTab" />

    <!-- 主要内容区域 -->
    <div class="page-content" v-loading="loading">
      <!-- 配置标签内容 -->
      <div v-if="activeTab === 'config'" class="form-doc-container">
        <div class="form-panel">
          <IntegrationForm
            mode="edit"
            :form-model="integrationData"
            :integration-types="integrationTypes"
            @submit="handleFormSubmit"
            @cancel="goBack"
          />
        </div>
        <div class="doc-panel">
          <h2 class="doc-title">集成文档</h2>
          <div class="doc-content">
            <template v-if="integrationData.typeId === 4">
              <!-- Prometheus 文档 -->
              <DocViewer
                doc-type="prometheus"
                class="full-height-doc"
              />
            </template>
            <template v-else-if="integrationData.typeId === 1">
              <!-- 阿里云 文档 -->
              <DocViewer
                doc-type="aliyun"
                class="full-height-doc"
              />
            </template>

            <template v-else>
              <!-- 通用说明 -->
              <h3>集成配置</h3>
              <p>请选择集成类型来查看详细配置说明。</p>
            </template>
          </div>
        </div>
      </div>

      <!-- 标签增强标签内容 -->
      <div v-else-if="activeTab === 'tag'" class="tab-content tag-content">
        <TagEnhancementComponent :integration-id="integrationId" />
      </div>

      <!-- 告警处理标签内容 -->
      <div v-else-if="activeTab === 'alert'" class="tab-content alert-content">
        <AlertProcessingComponent :integration-id="integrationId" />
      </div>

      <!-- 路由标签内容 -->
      <div v-else-if="activeTab === 'route'" class="tab-content route-content">
        <RoutingRulesComponent :integration-id="integrationId" />
      </div>


    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import IntegrationForm from './components/IntegrationForm.vue';
import DocViewer from './components/DocViewer.vue';
import TabNav from './components/TabNav.vue';
import TagEnhancementComponent from './components/TagEnhancementComponent.vue';
import AlertProcessingComponent from './components/AlertProcessingComponent.vue';
import RoutingRulesComponent from './components/RoutingRulesComponent.vue';
import { getIntegrationTypes, getIntegrationById, toggleIntegrationStatus, updateIntegration } from '@/api/adapters/integration';

// 组件名称定义
defineOptions({
  name: 'DetailIntegrationForm'
});

// 路由数据
const route = useRoute();
const router = useRouter();
const integrationId = computed(() => route.params.id);

// 页面状态
const loading = ref(false);
const activeTab = ref('config');

// 集成类型数据
const integrationTypes = ref([]);

// 集成数据
const integrationData = ref({
  id: null,
  name: '',
  dataSource: null,
  typeId: null,
  type: '',
  status: 'enabled',
  webhook: '',
  lastEventTime: ''
});

// 返回上一页
const goBack = () => {
  router.push('/oncall/integration/integrationpageindex');
};

// 根据类型名称获取类型ID（与mock数据保持一致）
const getTypeIdByName = (typeName) => {
  const typeMap = {
    '阿里云监控 CM 事件': 1,
    'Grafana': 2,
    'Zabbix': 3,
    'Prometheus': 4,
    'promethues-zabbix': 4, // API返回的类型名
    '邮件 Email': 5,
    '标准告警事件': 7
  };
  return typeMap[typeName] || 4;
};

// 切换状态
const toggleStatus = async () => {
  const originalStatus = integrationData.value.status;
  try {
    // 构造当前行数据，与列表页面保持一致
    const currentRowData = {
      id: integrationData.value.id,
      name: integrationData.value.name,
      type: integrationData.value.type,
      status: integrationData.value.status
    };

    await toggleIntegrationStatus(integrationData.value.id, integrationData.value.status, currentRowData);
    ElMessage.success(`已${integrationData.value.status === 'enabled' ? '启用' : '禁用'}集成：${integrationData.value.name}`);

    // 重新加载数据以确保同步
    await loadIntegrationDetail();
  } catch (error) {
    console.error('切换集成状态失败:', error);
    ElMessage.error(error.message || '切换状态失败');
    // 恢复原状态
    integrationData.value.status = originalStatus;
  }
};

// 表单提交处理
const handleFormSubmit = async (data) => {
  // 显示保存中提示
  const loadingMessage = ElMessage({
    message: '正在保存...',
    type: 'info',
    duration: 0
  });

  try {
    // 准备更新数据
    const updateData = {
      id: integrationData.value.id,
      name: data.name,
      kind: 'public', 
      type: integrationData.value.type,
      status: integrationData.value.status === 'enabled'
    };

    // 调用真实API更新集成
    await updateIntegration(integrationData.value.id, updateData);

    // 更新本地数据
    Object.assign(integrationData.value, data);

    // 关闭保存中提示
    loadingMessage.close();
    ElMessage.success(`集成 ${data.name} 已更新`);

    // 重新加载数据以确保同步
    await loadIntegrationDetail();
  } catch (error) {
    console.error('更新集成失败:', error);
    loadingMessage.close();
    ElMessage.error(error.message || '更新集成失败');
  }
};

// 加载集成详情
const loadIntegrationDetail = async () => {
  if (!integrationId.value) return;

  loading.value = true;
  try {
    const detail = await getIntegrationById(integrationId.value);
    // 处理API响应数据格式
    const integrationDetail = detail.data || detail;
    Object.assign(integrationData.value, {
      id: integrationDetail.id,
      name: integrationDetail.name,
      dataSource: integrationDetail.dataSource,
      typeId: getTypeIdByName(integrationDetail.type),
      type: integrationDetail.type,
      status: integrationDetail.status ? 'enabled' : 'disabled',
      webhook: integrationDetail.webhook || '',
      lastEventTime: integrationDetail.lastEventTime || '暂未收到告警事件'
    });
  } catch (error) {
    console.error('获取集成详情失败:', error);
    ElMessage.error('获取集成详情失败');
    goBack();
  } finally {
    loading.value = false;
  }
};

// 加载集成类型
const loadIntegrationTypes = async () => {
  try {
    const data = await getIntegrationTypes();
    integrationTypes.value = data;
  } catch (error) {
    console.error('加载集成类型失败:', error);
    ElMessage.error('加载集成类型失败');
  }
};

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    loadIntegrationTypes(),
    loadIntegrationDetail()
  ]);
});
</script>

<style lang="scss" scoped>
.detail-integration-container {
  padding: 0;
  background-color: #fff;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #f5f7fa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  color: #606266;
  font-size: 14px;
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.page-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.form-doc-container {
  display: flex;
  height: calc(100vh - 180px); /* 调整高度，因为有了标签栏 */
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  overflow: hidden;
}

.form-panel {
  width: 45%;
  padding: 16px;
  overflow-y: auto;
  border-right: 1px solid #eaeaea;
}

.doc-panel {
  width: 55%;
  padding: 16px;
  overflow-y: auto;
}

.doc-title {
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  position: sticky;
  top: 0;
  background-color: #fff;
  padding: 5px 0;
  z-index: 1;
}

.doc-content {
  color: #333;

  h3 {
    font-size: 18px;
    margin-top: 0;
    margin-bottom: 20px;
    color: #303133;
  }

  h4 {
    font-size: 16px;
    margin: 16px 0;
    color: #303133;
  }

  p {
    margin: 12px 0;
    line-height: 1.6;
  }
}

.full-height-doc {
  height: 100%;
  overflow-y: auto;
  padding-right: 2px;
}

.tab-content {
  padding: 40px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  height: calc(100vh - 180px);
  // display: flex;
  // justify-content: center;
  align-items: center;
}

.tag-content, .alert-content, .route-content {
  padding: 0;
  overflow: hidden;
}
</style>