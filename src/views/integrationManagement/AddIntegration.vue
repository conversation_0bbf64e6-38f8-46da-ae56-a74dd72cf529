<template>
  <div class="add-integration-container">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" icon="ArrowLeft" size="small">返回</el-button>
        <h2 class="page-title">添加{{ getIntegrationName }}集成</h2>
      </div>
    </div>

    <div class="page-content">
      <div class="form-doc-container">
        <div class="form-panel">
          <IntegrationForm
            mode="add"
            :form-model="formData"
            :integration-types="integrationTypes"
            @submit="handleFormSubmit"
            @cancel="goBack"
          />
        </div>
        <div class="doc-panel">
          <h2 class="doc-title">文档说明</h2>
          <div class="doc-content">
            <template v-if="formData.typeId === 4">
              <!-- Prometheus 文档 -->
              <DocViewer
                doc-type="prometheus"
                class="full-height-doc"
              />
            </template>
            <template v-else-if="formData.typeId === 1">
              <!-- 阿里云 文档 -->
              <DocViewer
                doc-type="aliyun"
                class="full-height-doc"
              />
            </template>

            <template v-else>
              <!-- 通用说明 -->
              <h3>集成配置</h3>
              <p>请选择集成类型来查看详细配置说明。</p>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import IntegrationForm from './components/IntegrationForm.vue';
import DocViewer from './components/DocViewer.vue';
import { getIntegrationTypes, getIntegrationKey, createIntegration } from '@/api/adapters/integration';
import { ElMessage } from 'element-plus';

// 组件名称定义
defineOptions({
  name: 'AddIntegrationForm'
});

// 路由数据
const route = useRoute();
const router = useRouter();
const integrationType = computed(() => route.params.type || '');

// 集成类型数据
const integrationTypes = ref([]);
const typeLoading = ref(false);

// 表单数据
const formData = ref({
  id: null,
  name: '',
  typeId: null,
  type: '',
  status: 'enabled',
  webhook: ''
});

// 加载状态
const keyLoading = ref(false);

// 获取集成类型名称
const getIntegrationName = computed(() => {
  const type = integrationTypes.value.find(t => t.name.toLowerCase() === integrationType.value.toLowerCase());
  return type ? ` ${type.name}` : '';
});

// 返回上一页
const goBack = () => {
  router.push('/oncall/integration/integrationpageindex');
};

// 获取集成密钥（UUID）
const loadIntegrationKey = async () => {
  keyLoading.value = true;
  try {
    const response = await getIntegrationKey();
    // 处理API响应数据格式
    const keyData = response.data || response;
    formData.value.id = keyData.id || keyData.key || keyData;
    console.log('获取到集成密钥:', formData.value.id);
  } catch (error) {
    console.error('获取集成密钥失败:', error);
    ElMessage.error('获取集成密钥失败');
  } finally {
    keyLoading.value = false;
  }
};

// 表单提交处理
const handleFormSubmit = async (data) => {
  // 显示保存中提示
  const loadingMessage = ElMessage({
    message: '正在创建集成...',
    type: 'info',
    duration: 0
  });

  try {
    // 准备创建数据，按照API要求的格式
    const createData = {
      id: formData.value.id, // 使用获取的UUID
      name: data.name,
      kind: 'public', // 固定为public
      type: data.type, // 使用真实的类型全名
      status: data.status === 'enabled' // 转换为boolean
    };

    console.log('创建集成数据:', createData);

    // 调用真实API创建集成
    await createIntegration(createData);

    // 关闭保存中提示
    loadingMessage.close();
    ElMessage.success(`集成 ${data.name} 已创建`);

    // 返回列表页
    goBack();
  } catch (error) {
    console.error('创建集成失败:', error);
    loadingMessage.close();
    ElMessage.error(error.message || '创建集成失败');
  }
};



// 加载集成类型数据
const loadIntegrationTypes = async () => {
  typeLoading.value = true;
  try {
    const data = await getIntegrationTypes();
    integrationTypes.value = data;

    // 如果URL中有集成类型，则自动设置表单数据中的类型
    if (integrationType.value) {
      const typeObj = data.find(t => t.name.toLowerCase() === integrationType.value.toLowerCase());
      if (typeObj) {
        formData.value.typeId = typeObj.id;
        formData.value.type = typeObj.name;
        // 存储类型ID以备后用
        localStorage.setItem('lastViewedTypeId', typeObj.id.toString());
      }
    }
  } catch (error) {
    console.error('加载集成类型失败:', error);
    ElMessage.error('加载集成类型失败');
  } finally {
    typeLoading.value = false;
  }
};

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    loadIntegrationTypes(),
    loadIntegrationKey() // 获取集成密钥
  ]);
});
</script>

<style lang="scss" scoped>
.add-integration-container {
  padding: 0;
  background-color: #fff;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #f5f7fa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.page-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.form-doc-container {
  display: flex;
  height: calc(100vh - 130px);
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  overflow: hidden;
}

.form-panel {
  width: 45%;
  padding: 16px;
  overflow-y: auto;
  border-right: 1px solid #eaeaea;
}

.doc-panel {
  width: 55%;
  padding: 16px;
  overflow-y: auto;
}

.doc-title {
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  position: sticky;
  top: 0;
  background-color: #fff;
  padding: 5px 0;
  z-index: 1;
}

.doc-content {
  color: #333;

  h3 {
    font-size: 18px;
    margin-top: 0;
    margin-bottom: 20px;
    color: #303133;
  }

  h4 {
    font-size: 16px;
    margin: 16px 0;
    color: #303133;
  }

  p {
    margin: 12px 0;
    line-height: 1.6;
  }
}

.full-height-doc {
  height: 100%;
  overflow-y: auto;
  padding-right: 2px;
}
</style>