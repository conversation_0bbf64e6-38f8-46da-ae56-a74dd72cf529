<template>
  <div class="tag-enhancement-container">
    <!-- 左侧标签增强编辑区域 -->
    <div class="tag-editor-panel">
      <div class="panel-header">
        <h3>标签增强</h3>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="showAddRuleDialog">
            <el-icon><Plus /></el-icon>添加规则
          </el-button>
        </div>
      </div>

      <div class="rules-container">
        <div v-if="!rules.length" class="empty-rules">
          <el-empty description="暂无标签增强规则" />
          <el-button type="primary" @click="showAddRuleDialog">添加规则</el-button>
        </div>

        <div v-else class="rule-list">
          <!-- 提取标签规则 -->
          <div v-for="(rule, index) in rules" :key="index" class="rule-card">
            <div class="rule-header" :class="{ 'extraction-rule': rule.kind === 'extraction', 'composition-rule': rule.kind === 'composition', 'drop-rule': rule.kind === 'drop' }">
              <div class="rule-title">
                <span class="rule-number">规则{{ index + 1 }}</span>
                <span class="rule-type">
                  {{ getRuleTypeLabel(rule.kind) }}
                </span>
              </div>
              <div class="rule-actions">
                <el-button color="write" size="small" text @click="editRule(index)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button color="write" size="small" text @click="removeRule(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>

            <div class="rule-content">
              <!-- 提取标签规则内容 -->
              <template v-if="rule.kind === 'extraction'">
                <div class="rule-summary">
                  <div class="rule-condition" v-if="rule.if && rule.if.length">
                    <span class="condition-label">规则1 - 提取标签</span>
                    <div class="condition-code">
                      {{ formatCondition(rule.if) }} && title == "*"
                    </div>
                  </div>
                  <div class="rule-description">
                    从字段 <span class="highlight">{{ rule.settings.source_field }}</span> 中，通过正则表达式 <span class="highlight">{{ rule.settings.pattern }}</span> 提取内容到标签 <span class="highlight">{{ rule.settings.result_label }}</span> 。
                  </div>
                </div>
              </template>

              <!-- 组合标签规则内容 -->
              <template v-else-if="rule.kind === 'composition'">
                <div class="rule-summary">
                  <div class="rule-condition">
                    <span class="condition-label">规则2 - 组合标签</span>
                  </div>
                  <div class="rule-description">
                    组合标签 <span class="highlight">{{ rule.settings.result_label }}</span> ，值为 <span class="highlight">{{ rule.settings.template }}</span>
                  </div>
                </div>
              </template>

              <!-- 删除标签规则内容 -->
              <template v-else-if="rule.kind === 'drop'">
                <div class="rule-summary">
                  <div class="rule-condition">
                    <span class="condition-label">规则3 - 删除标签</span>
                  </div>
                  <div class="rule-description">
                    删除标签 <span class="highlight">{{ rule.settings.drop_labels.join(', ') }}</span>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧告警关联区域 -->
    <AlertAssociationComponent :integration-id="props.integrationId" />

    <!-- 添加/编辑规则对话框 -->
    <el-dialog
      v-model="ruleDialogVisible"
      :title="isEditMode ? '编辑规则' : '添加规则'"
      width="650px"
      destroy-on-close>
      <div class="rule-dialog-content">
        <!-- 规则类型选择 -->
        <el-form :model="currentRule" label-position="top">
          <el-form-item label="规则类型" v-if="!isEditMode">
            <el-select v-model="currentRule.kind" placeholder="选择规则类型" style="width: 100%">
              <el-option label="提取标签" value="extraction" />
              <el-option label="组合标签" value="composition" />
              <el-option label="删除标签" value="drop" />
            </el-select>
          </el-form-item>

          <!-- 提取标签类型的表单 -->
          <template v-if="currentRule.kind === 'extraction'">
            <!-- 条件设置 -->
            <el-form-item label="应用条件">
              <div class="condition-list">
                <div v-for="(condition, condIndex) in currentRule.if" :key="condIndex" class="condition-item">
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-input v-model="condition.key" placeholder="字段名" />
                    </el-col>
                    <el-col :span="6">
                      <el-select v-model="condition.oper" placeholder="操作符">
                        <el-option label="包含" value="IN" />
                        <el-option label="不包含" value="NOT_IN" />
                        <el-option label="等于" value="EQ" />
                        <el-option label="不等于" value="NEQ" />
                        <el-option label="包含字符串" value="CONTAINS" />
                        <el-option label="正则匹配" value="REGEX" />
                      </el-select>
                    </el-col>
                    <el-col :span="8">
                      <el-select
                        v-if="['IN', 'NOT_IN'].includes(condition.oper)"
                        v-model="condition.vals"
                        multiple
                        filterable
                        allow-create
                        placeholder="值">
                        <el-option label="*" value="*" />
                        <el-option label="Critical" value="Critical" />
                        <el-option label="Warning" value="Warning" />
                        <el-option label="Info" value="Info" />
                      </el-select>
                      <el-input v-else v-model="condition.vals[0]" placeholder="值" />
                    </el-col>
                    <el-col :span="2">
                      <el-button type="danger" circle @click="removeCondition(currentRule, condIndex)">
                        <el-icon><Close /></el-icon>
                      </el-button>
                    </el-col>
                  </el-row>
                </div>

                <el-button type="primary" plain size="small" @click="addCondition(currentRule)">
                  <el-icon><Plus /></el-icon>添加条件
                </el-button>
              </div>
            </el-form-item>

            <!-- 提取设置 -->
            <el-form-item label="源字段">
              <el-select v-model="currentRule.settings.source_field" placeholder="选择源字段" style="width: 100%">
                <el-option label="标题" value="title" />
                <el-option label="描述" value="description" />
                <el-option label="告警名称" value="alertname" />
                <el-option label="实例" value="instance" />
                <el-option label="服务" value="service" />
              </el-select>
            </el-form-item>

            <el-form-item label="正则表达式">
              <el-input v-model="currentRule.settings.pattern" placeholder="例如: /(Web)/" />
              <div class="form-tip">使用正则表达式从源字段中提取内容，括号内的内容将被提取</div>
            </el-form-item>

            <el-form-item label="结果标签">
              <el-input v-model="currentRule.settings.result_label" placeholder="例如: webTitle" />
            </el-form-item>

            <el-form-item>
              <el-checkbox v-model="currentRule.settings.override">覆盖已有标签</el-checkbox>
            </el-form-item>
          </template>

          <!-- 组合标签类型的表单 -->
          <template v-else-if="currentRule.kind === 'composition'">
            <el-form-item label="结果标签">
              <el-input v-model="currentRule.settings.result_label" placeholder="例如: runbook_url" />
            </el-form-item>

            <el-form-item label="模板">
              <el-input
                v-model="currentRule.settings.template"
                placeholder="例如: https://example.com/runbook/{{.Labels.service}}/{{.Labels.check}}"
                type="textarea"
                :rows="3" />
              <div class="form-tip">使用 \{\{.Labels.field\}\} 语法引用现有标签</div>
            </el-form-item>

            <el-form-item>
              <el-checkbox v-model="currentRule.settings.override">覆盖已有标签</el-checkbox>
            </el-form-item>
          </template>

          <!-- 删除标签类型的表单 -->
          <template v-else-if="currentRule.kind === 'drop'">
            <el-form-item label="要删除的标签">
              <el-select
                v-model="currentRule.settings.drop_labels"
                multiple
                filterable
                allow-create
                placeholder="输入要删除的标签名称"
                style="width: 100%">
                <el-option label="cpu" value="cpu" />
                <el-option label="memory" value="memory" />
                <el-option label="disk" value="disk" />
                <el-option label="network" value="network" />
              </el-select>
            </el-form-item>
          </template>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCurrentRule" :loading="saving">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Close, Edit } from '@element-plus/icons-vue';
import { getTagRules, updateTagRules } from '@/api/mock/integration';
import AlertAssociationComponent from './AlertAssociationComponent.vue';

const props = defineProps({
  integrationId: {
    type: [Number, String],
    required: true
  }
});

// 标签规则数据
const rules = ref([]);
const originalRules = ref([]);
const saving = ref(false);

// 对话框控制
const ruleDialogVisible = ref(false);
const isEditMode = ref(false);
const currentRule = ref(null);
const currentRuleIndex = ref(-1);



// 获取规则类型标签
const getRuleTypeLabel = (kind) => {
  switch (kind) {
    case 'extraction':
      return '提取标签';
    case 'composition':
      return '组合标签';
    case 'drop':
      return '删除标签';
    default:
      return '未知类型';
  }
};

// 格式化条件
const formatCondition = (conditions) => {
  if (!conditions || !conditions.length) return '';

  return conditions.map(condition => {
    const key = condition.key;
    const oper = condition.oper;
    let value = '';

    if (oper === 'IN' || oper === 'NOT_IN') {
      value = condition.vals.join(', ');
    } else {
      value = condition.vals[0] || '';
    }

    switch (oper) {
      case 'IN':
        return `${key} == "${value}"`;
      case 'NOT_IN':
        return `${key} != "${value}"`;
      case 'EQ':
        return `${key} == "${value}"`;
      case 'NEQ':
        return `${key} != "${value}"`;
      case 'CONTAINS':
        return `${key} contains "${value}"`;
      case 'REGEX':
        return `${key} matches "${value}"`;
      default:
        return `${key} ${oper} "${value}"`;
    }
  }).join(' && ');
};





// 显示添加规则对话框
const showAddRuleDialog = () => {
  isEditMode.value = false;
  currentRule.value = {
    id: Date.now(),
    kind: 'extraction',
    if: [
      {
        key: 'severity',
        oper: 'IN',
        vals: ['Critical', 'Warning']
      }
    ],
    settings: {
      source_field: 'title',
      pattern: '/(\\w+)/',
      override: false,
      result_label: 'extractedValue'
    }
  };
  ruleDialogVisible.value = true;
};

// 编辑规则
const editRule = (index) => {
  isEditMode.value = true;
  currentRuleIndex.value = index;
  currentRule.value = JSON.parse(JSON.stringify(rules.value[index]));

  // 确保规则有必要的字段
  if (currentRule.value.kind === 'extraction') {
    if (!currentRule.value.if) {
      currentRule.value.if = [
        {
          key: 'severity',
          oper: 'IN',
          vals: ['Critical', 'Warning']
        }
      ];
    }
    if (!currentRule.value.settings) {
      currentRule.value.settings = {
        source_field: 'title',
        pattern: '/(\\w+)/',
        override: false,
        result_label: 'extractedValue'
      };
    }
  } else if (currentRule.value.kind === 'composition') {
    if (!currentRule.value.settings) {
      currentRule.value.settings = {
        template: 'https://example.com/runbook/{{.Labels.service}}/{{.Labels.check}}',
        override: true,
        result_label: 'runbook_url'
      };
    }
  } else if (currentRule.value.kind === 'drop') {
    if (!currentRule.value.settings) {
      currentRule.value.settings = {
        drop_labels: ['cpu']
      };
    }
  }

  ruleDialogVisible.value = true;
};

// 保存当前规则
const saveCurrentRule = async () => {
  // 验证规则
  if (currentRule.value.kind === 'extraction') {
    if (!currentRule.value.settings.source_field) {
      ElMessage.warning('请选择源字段');
      return;
    }
    if (!currentRule.value.settings.pattern) {
      ElMessage.warning('请输入正则表达式');
      return;
    }
    if (!currentRule.value.settings.result_label) {
      ElMessage.warning('请输入结果标签名称');
      return;
    }
  } else if (currentRule.value.kind === 'composition') {
    if (!currentRule.value.settings.result_label) {
      ElMessage.warning('请输入结果标签名称');
      return;
    }
    if (!currentRule.value.settings.template) {
      ElMessage.warning('请输入模板');
      return;
    }
  } else if (currentRule.value.kind === 'drop') {
    if (!currentRule.value.settings.drop_labels || currentRule.value.settings.drop_labels.length === 0) {
      ElMessage.warning('请选择要删除的标签');
      return;
    }
  }

  if (isEditMode.value) {
    // 更新规则
    rules.value[currentRuleIndex.value] = currentRule.value;
  } else {
    // 添加新规则
    rules.value.push(currentRule.value);
  }

  // 保存到服务器
  saving.value = true;
  try {
    const { success } = await updateTagRules(props.integrationId, rules.value);
    if (success) {
      ElMessage.success(isEditMode.value ? '规则更新成功' : '规则添加成功');
      originalRules.value = JSON.parse(JSON.stringify(rules.value));
      ruleDialogVisible.value = false;
    } else {
      ElMessage.error('保存失败，请重试');
    }
  } catch (error) {
    console.error('保存标签增强规则失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

// 移除规则
const removeRule = (index) => {
  ElMessageBox.confirm('确定要删除此规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    rules.value.splice(index, 1);

    // 保存到服务器
    saving.value = true;
    try {
      const { success } = await updateTagRules(props.integrationId, rules.value);
      if (success) {
        ElMessage.success('规则删除成功');
        originalRules.value = JSON.parse(JSON.stringify(rules.value));
      } else {
        ElMessage.error('删除失败，请重试');
      }
    } catch (error) {
      console.error('删除标签增强规则失败:', error);
      ElMessage.error('删除失败，请重试');
    } finally {
      saving.value = false;
    }
  }).catch(() => {});
};

// 添加条件
const addCondition = (rule) => {
  if (!rule.if) {
    rule.if = [];
  }
  rule.if.push({
    key: '',
    oper: 'IN',
    vals: ['*']
  });
};

// 移除条件
const removeCondition = (rule, index) => {
  if (rule.if && rule.if.length > 1) {
    rule.if.splice(index, 1);
  } else {
    ElMessage.warning('至少需要保留一个条件');
  }
};

// 加载标签规则
const loadTagRules = async () => {
  try {
    const { success, data } = await getTagRules(props.integrationId);
    if (success) {
      // 确保每个规则都有必要的字段
      const processedData = data.map(rule => {
        if (rule.kind === 'extraction') {
          if (!rule.if) {
            rule.if = [
              {
                key: 'severity',
                oper: 'IN',
                vals: ['Critical', 'Warning']
              }
            ];
          }
          if (!rule.settings) {
            rule.settings = {
              source_field: 'title',
              pattern: '/(\\w+)/',
              override: false,
              result_label: 'extractedValue'
            };
          }
        } else if (rule.kind === 'composition') {
          if (!rule.settings) {
            rule.settings = {
              template: 'https://example.com/runbook/{{.Labels.service}}/{{.Labels.check}}',
              override: true,
              result_label: 'runbook_url'
            };
          }
        } else if (rule.kind === 'drop') {
          if (!rule.settings) {
            rule.settings = {
              drop_labels: ['cpu']
            };
          }
        }
        return rule;
      });

      rules.value = processedData;
      originalRules.value = JSON.parse(JSON.stringify(processedData));
    }
  } catch (error) {
    console.error('加载标签增强规则失败:', error);
    ElMessage.error('加载标签增强规则失败');
  }
};



// 监听集成ID变化
watch(() => props.integrationId, () => {
  loadTagRules();
}, { immediate: true });

// 组件挂载时加载数据
onMounted(() => {
  loadTagRules();
});
</script>

<style lang="scss" scoped>
.tag-enhancement-container {
  display: flex;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  color: #303133;
}

.tag-editor-panel {
  width: 50%;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}



.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.header-actions {
  display: flex;
  gap: 10px;
}

.rules-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #fff;
  color: #303133;
}

.empty-rules {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 20px;
}

.rule-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-card {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  border: 1px solid #ebeef5;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;

  &.extraction-rule {
    background-color: #409eff;
    color: #fff;
  }

  &.composition-rule {
    background-color: #67c23a;
    color: #fff;
  }

  &.drop-rule {
    background-color: #f56c6c;
    color: #fff;
  }
}

.rule-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rule-number {
  font-weight: 600;
}

.rule-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.2);
}

.rule-content {
  padding: 16px;
  color: #303133;
}

.rule-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rule-condition {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.condition-label {
  font-weight: 600;
  color: #a0a0a0;
  font-size: 14px;
}

.condition-code {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
  color: #303133;
  border: 1px solid #ebeef5;
}

.rule-description {
  color: #a0a0a0;
  line-height: 1.5;
}

.highlight {
  color: #409eff;
  font-weight: 600;
  font-family: monospace;
}

.rule-section {
  margin-bottom: 20px;
}

.section-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: #606266;
}

.condition-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.condition-item {
  margin-bottom: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.panel-footer {
  padding: 16px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #fff;
}

.alert-preview-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.alert-json {
  border: 1px solid #eaeaea;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
}

.json-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #eaeaea;
  font-weight: 600;
}

.json-content {
  padding: 16px;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
  background-color: #fff;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  transition: max-height 0.3s ease;
}

.json-expanded {
  max-height: 500px;
}

.extracted-tags {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  border: 1px solid #eaeaea;

  h4 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 600;
  }
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  margin-right: 0;
}

.rule-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
