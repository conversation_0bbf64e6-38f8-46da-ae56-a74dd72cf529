<template>
  <div class="integration-tab-nav">
    <ul class="tab-list">
      <li
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-item', { active: activeTab === tab.key }]"
        @click="changeTab(tab.key)">
        {{ tab.label }}
      </li>
    </ul>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

defineProps({
  tabs: {
    type: Array,
    default: () => [
      { key: 'config', label: '配置' },
      { key: 'tag', label: '标签增强' },
      { key: 'alert', label: '告警处理' },
      { key: 'route', label: '路由' }
    ]
  },
  activeTab: {
    type: String,
    default: 'config'
  }
});

const emit = defineEmits(['update:activeTab']);

const changeTab = (tabKey) => {
  emit('update:activeTab', tabKey);
};
</script>

<style lang="scss" scoped>
.integration-tab-nav {
  border-bottom: 1px solid #dcdfe6;
  background-color: #f0f2f5;
}

.tab-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.tab-item {
  padding: 12px 24px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  margin-bottom: -1px;
  transition: all 0.3s;

  &:hover {
    color: #409eff;
  }

  &.active {
    color: #409eff;
    border-bottom-color: #409eff;
    background-color: #fff;
  }
}
</style>