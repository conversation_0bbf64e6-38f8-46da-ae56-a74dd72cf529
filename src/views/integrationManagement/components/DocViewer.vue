<template>
  <div class="doc-viewer-container">
    <div class="doc-viewer" v-loading="loading">
      <template v-if="renderedContent">
        <div v-html="renderedContent" class="markdown-content"></div>
      </template>
      <div v-else-if="loading" class="loading-placeholder">
        <p>加载文档内容中...</p>
      </div>
      <div v-else class="empty-placeholder">
        <el-empty description="暂无文档内容" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { marked } from 'marked';
import { getIntegrationDoc } from '@/api/mock/docs';

const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  docType: {
    type: String,
    default: ''
  }
});

const loading = ref(props.loading);
const markdownContent = ref(props.content);

// 监听加载状态变化
watch(() => props.loading, (newVal) => {
  loading.value = newVal;
});

// 监听内容变化
watch(() => props.content, (newVal) => {
  markdownContent.value = newVal;
});

// 监听文档类型变化，自动加载文档
// watch(() => props.docType, async (newType) => {
//   if (newType) {
//     await loadDocForType(newType);
//   }
// }, { immediate: true });

// 加载对应类型的文档
const loadDocForType = async (type) => {
  if (!type) return;
  
  loading.value = true;
  try {
    let typeId = null;
    
    // 将文档类型转换为typeId
    if (type === 'prometheus') {
      typeId = 4;
    } else if (type === 'aliyun') {
      typeId = 1;
    }
    
    if (typeId) {
      const { success, data } = await getIntegrationDoc(typeId);
      if (success) {
        markdownContent.value = data;
      }
    }
  } catch (error) {
    console.error('加载文档失败:', error);
  } finally {
    loading.value = false;
  }
};

// 在组件挂载时加载文档
onMounted(() => {
  if (props.docType && !props.content) {
    loadDocForType(props.docType);
  }
});

// 设置marked选项
marked.setOptions({
  gfm: true,
  breaks: true,
  highlight: function(code, lang) {
    return code;
  }
});

// 渲染Markdown内容
const renderedContent = computed(() => {
  return markdownContent.value ? marked(markdownContent.value) : '';
});
</script>

<style lang="scss" scoped>
.doc-viewer-container {
  height: 100%;
  overflow: hidden;
  border-top: none;
  margin-top: 0;
  padding-top: 0;
}

.doc-viewer {
  padding: 5px 0;
  height: 100%;
  overflow-y: auto !important;
  color: #333;
  position: relative;
}

.empty-placeholder {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-placeholder {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.markdown-content) {
  height: auto !important;
  min-height: 100%;
  overflow-y: visible;
  padding-right: 5px;
  padding-bottom: 80px;

  h1 {
    font-size: 24px;
    margin-top: 0;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
  }
  
  h2 {
    font-size: 20px;
    margin-top: 24px;
    margin-bottom: 16px;
    color: #303133;
  }
  
  h3 {
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 14px;
    color: #303133;
  }
  
  p {
    margin: 14px 0;
    line-height: 1.6;
    color: #606266;
  }
  
  code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    background-color: #f5f7fa;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 14px;
    color: #f56c6c;
  }
  
  pre {
    background-color: #f5f7fa;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 16px 0;
    border: 1px solid #e4e7ed;
    white-space: pre-wrap;
    max-width: 100%;
    word-break: break-all;
    
    code {
      background-color: transparent;
      padding: 0;
      font-size: 14px;
      line-height: 1.5;
      color: #606266;
      white-space: pre-wrap;
    }
  }
  
  ul, ol {
    padding-left: 24px;
    margin: 16px 0;
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
      color: #606266;
    }
  }
  
  a {
    color: #409eff;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  blockquote {
    border-left: 4px solid #409eff;
    margin: 16px 0;
    padding: 0 16px;
    color: #909399;
    background-color: #f5f7fa;
  }
  
  table {
    border-collapse: collapse;
    margin: 15px 0;
    width: 100%;
    
    th, td {
      border: 1px solid #dcdfe6;
      padding: 8px 12px;
    }
    
    th {
      background-color: #f5f7fa;
      font-weight: 500;
    }
    
    tr:nth-child(even) {
      background-color: #fafafa;
    }
  }
}
</style> 