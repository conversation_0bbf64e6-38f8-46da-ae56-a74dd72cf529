<template>
  <div class="prometheus-form">
    <el-form :model="formData" label-width="100px" :rules="rules" ref="formRef">
      <el-form-item label="推送地址" prop="webhook">
        <el-input v-model="formData.webhook" readonly placeholder="系统自动生成推送地址">
          <template #append>
            <el-button type="primary" @click="copyPushUrl">复制</el-button>
          </template>
        </el-input>
        <div class="form-tip">
          通过推送地址将Prometheus告警事件推送到zeroduty。
        </div>
      </el-form-item>
      <el-form-item label="认证来源" prop="authSource">
        <el-select v-model="formData.authSource" placeholder="请选择认证来源">
          <el-option label="请选择操作空间" value=""></el-option>
          <el-option label="默认空间" value="default"></el-option>
          <el-option label="生产环境" value="production"></el-option>
          <el-option label="测试环境" value="testing"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="告警级别映射" prop="alertLevelMap">
        <el-checkbox-group v-model="formData.alertLevelMap">
          <el-checkbox label="critical">严重</el-checkbox>
          <el-checkbox label="warning">警告</el-checkbox>
          <el-checkbox label="info">信息</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="标签过滤" prop="labelFilter">
        <el-input v-model="formData.labelFilter" placeholder="请输入标签过滤条件" type="textarea" :rows="3"></el-input>
        <div class="form-tip">
          格式：{key1=value1,key2=value2}，支持正则表达式。例如：{severity=critical|warning,env=prod}
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { generatePushUrl } from '@/utils/webhook';

const props = defineProps({
  formModel: {
    type: Object,
    default: () => ({
      webhook: '',
      authSource: '',
      alertLevelMap: ['critical', 'warning'],
      labelFilter: ''
    })
  }
});

const emit = defineEmits(['update:formModel']);

// 表单数据
const formData = reactive({
  webhook: props.formModel.webhook || '',
  authSource: props.formModel.authSource || '',
  alertLevelMap: props.formModel.alertLevelMap || ['critical', 'warning'],
  labelFilter: props.formModel.labelFilter || ''
});

// 表单引用
const formRef = ref(null);

// 表单验证规则 - webhook不再需要验证，因为是自动生成的
const rules = {};

// 复制推送地址
const copyPushUrl = () => {
  if (formData.webhook) {
    navigator.clipboard.writeText(formData.webhook)
      .then(() => {
        ElMessage.success('推送地址已复制到剪贴板');
      })
      .catch(() => {
        ElMessage.error('复制失败，请手动复制');
      });
  }
};

// 自动生成推送地址（如果为空）
const generatePushAddress = () => {
  if (!formData.webhook) {
    // 假设Prometheus的typeId为4
    formData.webhook = generatePushUrl('Prometheus', props.formModel.id || 'new');
  }
};

// 当props中的formModel变化时，更新本地formData
watch(() => props.formModel, (newVal) => {
  formData.webhook = newVal.webhook || '';
  formData.authSource = newVal.authSource || '';
  formData.alertLevelMap = newVal.alertLevelMap || ['critical', 'warning'];
  formData.labelFilter = newVal.labelFilter || '';
  
  // 如果webhook为空，生成一个新的
  if (!formData.webhook) {
    generatePushAddress();
  }
}, { deep: true });

// 监听formData变化，更新父组件的formModel
watch(formData, (newVal) => {
  emit('update:formModel', {
    webhook: newVal.webhook,
    authSource: newVal.authSource,
    alertLevelMap: newVal.alertLevelMap,
    labelFilter: newVal.labelFilter
  });
}, { deep: true });

// 组件挂载时，生成推送地址（如果需要）
onMounted(() => {
  generatePushAddress();
});

// 对外暴露validate方法供父组件调用
defineExpose({
  validate: async () => {
    if (!formRef.value) return false;
    return await formRef.value.validate()
      .then(() => true)
      .catch(() => false);
  }
});
</script>

<style lang="scss" scoped>
.prometheus-form {
  padding: 20px 0;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style> 