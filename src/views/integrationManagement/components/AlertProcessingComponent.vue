<template>
  <div class="alert-processing-container">
    <!-- 左侧告警处理编辑区域 -->
    <div class="alert-editor-panel">
      <div class="panel-header">
        <h3>告警处理</h3>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="showAddRuleDialog">
            <el-icon><Plus /></el-icon>添加规则
          </el-button>
        </div>
      </div>

      <div class="rules-container">
        <div v-if="!rules.length" class="empty-rules">
          <el-empty description="暂无告警处理规则" />
          <el-button type="primary" @click="showAddRuleDialog">添加规则</el-button>
        </div>

        <div v-else class="rule-list">
          <!-- 告警处理规则 -->
          <div v-for="(rule, index) in rules" :key="index" class="rule-card">
            <div class="rule-header" :class="getRuleHeaderClass(rule.kind)">
              <div class="rule-title">
                <span class="rule-number">规则{{ index + 1 }}</span>
                <span class="rule-type">
                  {{ getRuleTypeLabel(rule.kind) }}
                </span>
              </div>
              <div class="rule-actions">
                <el-button color="write" size="small" text @click="editRule(index)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button color="write" size="small" text @click="removeRule(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>

            <div class="rule-content">
              <!-- 更新标题规则内容 -->
              <template v-if="rule.kind === 'title_reset'">
                <div class="rule-summary">
                  <div class="rule-condition" v-if="rule.if && rule.if.length">
                    <span class="condition-label">规则{{ index + 1 }} - 更新标题</span>
                    <div class="condition-code">
                      {{ formatCondition(rule.if) }}
                    </div>
                  </div>
                  <div class="rule-description">
                    将告警标题更新为 <span class="highlight">{{ rule.settings.title }}</span>
                  </div>
                </div>
              </template>

              <!-- 告警过滤规则内容 -->
              <template v-else-if="rule.kind === 'alert_drop'">
                <div class="rule-summary">
                  <div class="rule-condition" v-if="rule.if && rule.if.length">
                    <span class="condition-label">规则{{ index + 1 }} - 告警过滤</span>
                    <div class="condition-code">
                      {{ formatCondition(rule.if) }}
                    </div>
                  </div>
                  <div class="rule-description">
                    符合条件的告警将被过滤，不会触发通知
                  </div>
                </div>
              </template>

              <!-- 更新描述规则内容 -->
              <template v-else-if="rule.kind === 'description_reset'">
                <div class="rule-summary">
                  <div class="rule-condition" v-if="rule.if && rule.if.length">
                    <span class="condition-label">规则{{ index + 1 }} - 更新描述</span>
                    <div class="condition-code">
                      {{ rule.if ? formatCondition(rule.if) : '应用于所有告警' }}
                    </div>
                  </div>
                  <div class="rule-description">
                    将告警描述更新为 <span class="highlight">{{ rule.settings.description }}</span>
                  </div>
                </div>
              </template>

              <!-- 更新告警级别规则内容 -->
              <template v-else-if="rule.kind === 'severity_reset'">
                <div class="rule-summary">
                  <div class="rule-condition" v-if="rule.if && rule.if.length">
                    <span class="condition-label">规则{{ index + 1 }} - 更新告警级别</span>
                    <div class="condition-code">
                      {{ formatCondition(rule.if) }}
                    </div>
                  </div>
                  <div class="rule-description">
                    将告警级别更新为 <span class="highlight">{{ rule.settings.severity }}</span>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧告警关联区域 -->
    <AlertAssociationComponent :integration-id="props.integrationId" />

    <!-- 添加/编辑规则对话框 -->
    <el-dialog
      v-model="ruleDialogVisible"
      :title="isEditMode ? '编辑规则' : '添加规则'"
      width="650px"
      destroy-on-close>
      <div class="rule-dialog-content">
        <!-- 规则类型选择 -->
        <el-form :model="currentRule" label-position="top">
          <el-form-item label="规则类型" v-if="!isEditMode">
            <el-select v-model="currentRule.kind" placeholder="选择规则类型" style="width: 100%">
              <el-option label="更新标题" value="title_reset" />
              <el-option label="更新描述" value="description_reset" />
              <el-option label="更新告警级别" value="severity_reset" />
              <el-option label="告警过滤" value="alert_drop" />
            </el-select>
          </el-form-item>

          <!-- 条件设置 (除了description_reset可以为null外，其他都需要条件) -->
          <el-form-item label="应用条件" v-if="currentRule.kind !== 'description_reset' || (currentRule.kind === 'description_reset' && currentRule.if)">
            <div class="condition-list">
              <div v-for="(condition, condIndex) in currentRule.if || []" :key="condIndex" class="condition-item">
                <el-row :gutter="10">
                  <el-col :span="8">
                    <el-input v-model="condition.key" placeholder="字段名" />
                  </el-col>
                  <el-col :span="6">
                    <el-select v-model="condition.oper" placeholder="操作符">
                      <el-option label="包含" value="IN" />
                      <el-option label="不包含" value="NOT_IN" />
                      <el-option label="等于" value="EQ" />
                      <el-option label="不等于" value="NEQ" />
                      <el-option label="包含字符串" value="CONTAINS" />
                      <el-option label="正则匹配" value="REGEX" />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select
                      v-if="['IN', 'NOT_IN'].includes(condition.oper)"
                      v-model="condition.vals"
                      multiple
                      filterable
                      allow-create
                      placeholder="输入值"
                      style="width: 100%">
                    </el-select>
                    <el-input
                      v-else
                      v-model="condition.vals[0]"
                      placeholder="输入值" />
                  </el-col>
                  <el-col :span="2">
                    <el-button
                      type="danger"
                      circle
                      size="small"
                      @click="removeCondition(condIndex)"
                      :disabled="currentRule.kind !== 'description_reset' && currentRule.if.length <= 1">
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div class="condition-actions">
              <el-button type="primary" plain size="small" @click="addCondition(currentRule)">
                <el-icon><Plus /></el-icon>添加条件
              </el-button>
            </div>
          </el-form-item>

          <el-form-item v-if="currentRule.kind === 'description_reset' && !currentRule.if">
            <el-alert
              title="此规则将应用于所有告警"
              type="info"
              :closable="false"
              show-icon>
            </el-alert>
            <div class="condition-actions" style="margin-top: 10px;">
              <el-button type="primary" plain size="small" @click="initCondition(currentRule)">
                <el-icon><Plus /></el-icon>添加条件
              </el-button>
            </div>
          </el-form-item>

          <!-- 更新标题设置 -->
          <template v-if="currentRule.kind === 'title_reset'">
            <el-form-item label="新标题">
              <el-input v-model="currentRule.settings.title" placeholder="输入新的告警标题" />
            </el-form-item>
          </template>

          <!-- 更新描述设置 -->
          <template v-else-if="currentRule.kind === 'description_reset'">
            <el-form-item label="新描述">
              <el-input
                v-model="currentRule.settings.description"
                placeholder="输入新的告警描述"
                type="textarea"
                :rows="3" />
            </el-form-item>
          </template>

          <!-- 更新告警级别设置 -->
          <template v-else-if="currentRule.kind === 'severity_reset'">
            <el-form-item label="新告警级别">
              <el-select v-model="currentRule.settings.severity" placeholder="选择告警级别" style="width: 100%">
                <el-option label="Critical" value="Critical" />
                <el-option label="Warning" value="Warning" />
                <el-option label="Info" value="Info" />
              </el-select>
            </el-form-item>
          </template>

          <!-- 告警过滤不需要额外设置 -->
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCurrentRule" :loading="saving">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Close, Edit } from '@element-plus/icons-vue';
import { getAlertProcessingRules, updateAlertProcessingRules } from '@/api/mock/integration';
import AlertAssociationComponent from './AlertAssociationComponent.vue';

const props = defineProps({
  integrationId: {
    type: [Number, String],
    required: true
  }
});

// 告警处理规则数据
const rules = ref([]);
const originalRules = ref([]);
const saving = ref(false);

// 对话框控制
const ruleDialogVisible = ref(false);
const isEditMode = ref(false);
const currentRule = ref(null);
const currentRuleIndex = ref(-1);

// 获取规则类型标签
const getRuleTypeLabel = (kind) => {
  switch (kind) {
    case 'title_reset':
      return '更新标题';
    case 'description_reset':
      return '更新描述';
    case 'severity_reset':
      return '更新告警级别';
    case 'alert_drop':
      return '告警过滤';
    default:
      return '未知类型';
  }
};

// 获取规则头部样式类
const getRuleHeaderClass = (kind) => {
  switch (kind) {
    case 'title_reset':
      return 'title-rule';
    case 'description_reset':
      return 'description-rule';
    case 'severity_reset':
      return 'severity-rule';
    case 'alert_drop':
      return 'drop-rule';
    default:
      return '';
  }
};

// 格式化条件
const formatCondition = (conditions) => {
  if (!conditions || !conditions.length) return '';

  return conditions.map(condition => {
    const key = condition.key;
    const oper = condition.oper;
    let value = '';

    if (oper === 'IN' || oper === 'NOT_IN') {
      value = condition.vals.join(', ');
    } else {
      value = condition.vals[0] || '';
    }

    switch (oper) {
      case 'IN':
        return `${key} == "${value}"`;
      case 'NOT_IN':
        return `${key} != "${value}"`;
      case 'EQ':
        return `${key} == "${value}"`;
      case 'NEQ':
        return `${key} != "${value}"`;
      case 'CONTAINS':
        return `${key} contains "${value}"`;
      case 'REGEX':
        return `${key} matches "${value}"`;
      default:
        return `${key} ${oper} "${value}"`;
    }
  }).join(' && ');
};

// 显示添加规则对话框
const showAddRuleDialog = () => {
  isEditMode.value = false;
  currentRule.value = {
    id: Date.now(),
    kind: 'title_reset',
    if: [
      {
        key: 'severity',
        oper: 'IN',
        vals: ['Info']
      }
    ],
    settings: {
      title: '测试告警'
    }
  };
  ruleDialogVisible.value = true;
};

// 编辑规则
const editRule = (index) => {
  isEditMode.value = true;
  currentRuleIndex.value = index;
  currentRule.value = JSON.parse(JSON.stringify(rules.value[index]));

  // 确保规则有必要的字段
  if (currentRule.value.kind === 'title_reset') {
    if (!currentRule.value.if) {
      currentRule.value.if = [
        {
          key: 'severity',
          oper: 'IN',
          vals: ['Info']
        }
      ];
    }
    if (!currentRule.value.settings) {
      currentRule.value.settings = {
        title: '测试告警'
      };
    }
  } else if (currentRule.value.kind === 'description_reset') {
    if (!currentRule.value.settings) {
      currentRule.value.settings = {
        description: '测试更新描述'
      };
    }
  } else if (currentRule.value.kind === 'severity_reset') {
    if (!currentRule.value.if) {
      currentRule.value.if = [
        {
          key: 'title',
          oper: 'CONTAINS',
          vals: ['测试']
        }
      ];
    }
    if (!currentRule.value.settings) {
      currentRule.value.settings = {
        severity: 'Warning'
      };
    }
  } else if (currentRule.value.kind === 'alert_drop') {
    if (!currentRule.value.if) {
      currentRule.value.if = [
        {
          key: 'title',
          oper: 'IN',
          vals: ['test']
        }
      ];
    }
  }

  ruleDialogVisible.value = true;
};

// 保存当前规则
const saveCurrentRule = async () => {
  // 验证规则
  if (currentRule.value.kind === 'title_reset') {
    if (!currentRule.value.if || !currentRule.value.if.length) {
      ElMessage.warning('请添加至少一个条件');
      return;
    }
    if (!currentRule.value.settings || !currentRule.value.settings.title) {
      ElMessage.warning('请输入新标题');
      return;
    }
  } else if (currentRule.value.kind === 'description_reset') {
    if (!currentRule.value.settings || !currentRule.value.settings.description) {
      ElMessage.warning('请输入新描述');
      return;
    }
  } else if (currentRule.value.kind === 'severity_reset') {
    if (!currentRule.value.if || !currentRule.value.if.length) {
      ElMessage.warning('请添加至少一个条件');
      return;
    }
    if (!currentRule.value.settings || !currentRule.value.settings.severity) {
      ElMessage.warning('请选择新告警级别');
      return;
    }
  } else if (currentRule.value.kind === 'alert_drop') {
    if (!currentRule.value.if || !currentRule.value.if.length) {
      ElMessage.warning('请添加至少一个条件');
      return;
    }
  }

  // 验证条件
  if (currentRule.value.if) {
    for (const condition of currentRule.value.if) {
      if (!condition.key) {
        ElMessage.warning('请输入条件字段名');
        return;
      }
      if (!condition.oper) {
        ElMessage.warning('请选择条件操作符');
        return;
      }
      if (!condition.vals || !condition.vals.length || (condition.vals.length === 1 && !condition.vals[0])) {
        ElMessage.warning('请输入条件值');
        return;
      }
    }
  }

  // 保存规则
  if (isEditMode.value) {
    rules.value[currentRuleIndex.value] = currentRule.value;
  } else {
    rules.value.push(currentRule.value);
  }

  // 保存到服务器
  saving.value = true;
  try {
    const { success } = await updateAlertProcessingRules(props.integrationId, rules.value);
    if (success) {
      ElMessage.success(isEditMode.value ? '规则更新成功' : '规则添加成功');
      originalRules.value = JSON.parse(JSON.stringify(rules.value));
      ruleDialogVisible.value = false;
    } else {
      ElMessage.error('保存失败，请重试');
    }
  } catch (error) {
    console.error('保存告警处理规则失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

// 移除规则
const removeRule = (index) => {
  ElMessageBox.confirm('确定要删除此规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    rules.value.splice(index, 1);

    // 保存到服务器
    saving.value = true;
    try {
      const { success } = await updateAlertProcessingRules(props.integrationId, rules.value);
      if (success) {
        ElMessage.success('规则删除成功');
        originalRules.value = JSON.parse(JSON.stringify(rules.value));
      } else {
        ElMessage.error('删除失败，请重试');
      }
    } catch (error) {
      console.error('删除告警处理规则失败:', error);
      ElMessage.error('删除失败，请重试');
    } finally {
      saving.value = false;
    }
  }).catch(() => {});
};

// 添加条件
const addCondition = (rule) => {
  if (!rule.if) {
    rule.if = [];
  }
  rule.if.push({
    key: '',
    oper: 'IN',
    vals: ['']
  });
};

// 初始化条件
const initCondition = (rule) => {
  rule.if = [
    {
      key: '',
      oper: 'IN',
      vals: ['']
    }
  ];
};

// 移除条件
const removeCondition = (index) => {
  if (currentRule.value.if.length > 1 || currentRule.value.kind === 'description_reset') {
    currentRule.value.if.splice(index, 1);
    
    // 如果是description_reset且没有条件了，设置if为null
    if (currentRule.value.kind === 'description_reset' && currentRule.value.if.length === 0) {
      currentRule.value.if = null;
    }
  }
};

// 加载告警处理规则
const loadAlertProcessingRules = async () => {
  try {
    const { success, data } = await getAlertProcessingRules(props.integrationId);
    if (success) {
      // 确保每个规则都有必要的字段
      const processedData = data.map(rule => {
        if (rule.kind === 'title_reset') {
          if (!rule.if) {
            rule.if = [
              {
                key: 'severity',
                oper: 'IN',
                vals: ['Info']
              }
            ];
          }
          if (!rule.settings) {
            rule.settings = {
              title: '测试告警'
            };
          }
        } else if (rule.kind === 'description_reset') {
          if (!rule.settings) {
            rule.settings = {
              description: '测试更新描述'
            };
          }
        } else if (rule.kind === 'severity_reset') {
          if (!rule.if) {
            rule.if = [
              {
                key: 'title',
                oper: 'CONTAINS',
                vals: ['测试']
              }
            ];
          }
          if (!rule.settings) {
            rule.settings = {
              severity: 'Warning'
            };
          }
        } else if (rule.kind === 'alert_drop') {
          if (!rule.if) {
            rule.if = [
              {
                key: 'title',
                oper: 'IN',
                vals: ['test']
              }
            ];
          }
        }
        return rule;
      });

      rules.value = processedData;
      originalRules.value = JSON.parse(JSON.stringify(processedData));
    }
  } catch (error) {
    console.error('加载告警处理规则失败:', error);
    ElMessage.error('加载告警处理规则失败');
  }
};

// 监听集成ID变化
watch(() => props.integrationId, () => {
  loadAlertProcessingRules();
}, { immediate: true });

// 组件挂载时加载数据
onMounted(() => {
  loadAlertProcessingRules();
});
</script>

<style lang="scss" scoped>
.alert-processing-container {
  display: flex;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  color: #303133;
}

.alert-editor-panel {
  width: 50%;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.header-actions {
  display: flex;
  gap: 10px;
}

.rules-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #fff;
  color: #303133;
}

.empty-rules {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 20px;
}

.rule-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-card {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  border: 1px solid #ebeef5;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;

  &.title-rule {
    background-color: #409eff;
    color: #fff;
  }

  &.description-rule {
    background-color: #67c23a;
    color: #fff;
  }

  &.severity-rule {
    background-color: #e6a23c;
    color: #fff;
  }

  &.drop-rule {
    background-color: #f56c6c;
    color: #fff;
  }
}

.rule-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rule-number {
  font-weight: 600;
}

.rule-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.2);
}

.rule-content {
  padding: 16px;
  color: #303133;
}

.rule-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rule-condition {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.condition-label {
  font-weight: 600;
  color: #a0a0a0;
  font-size: 14px;
}

.condition-code {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
  color: #303133;
  border: 1px solid #ebeef5;
}

.rule-description {
  color: #a0a0a0;
  line-height: 1.5;
}

.highlight {
  color: #409eff;
  font-weight: 600;
  font-family: monospace;
}

.rule-section {
  margin-bottom: 20px;
}

.section-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: #606266;
}

.condition-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.condition-item {
  margin-bottom: 10px;
}

.condition-actions {
  margin-top: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.panel-footer {
  padding: 16px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #fff;
}

.rule-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
