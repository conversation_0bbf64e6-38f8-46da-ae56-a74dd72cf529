<template>
  <div class="alert-association-container">
    <div class="panel-header">
      <h3>关联告警</h3>
    </div>

    <div class="filter-bar">
      <el-button size="small" circle>
        <el-icon><Refresh /></el-icon>
      </el-button>

      <el-select v-model="filterSeverity" placeholder="全部告警" size="small">
        <el-option label="全部告警" value=""></el-option>
        <el-option label="Critical" value="Critical"></el-option>
        <el-option label="Warning" value="Warning"></el-option>
        <el-option label="Info" value="Info"></el-option>
      </el-select>

      <el-select v-model="filterPeriod" placeholder="最近30天" size="small">
        <el-option label="最近30天" value="30"></el-option>
        <el-option label="最近7天" value="7"></el-option>
        <el-option label="最近24小时" value="1"></el-option>
      </el-select>

      <el-select v-model="filterSpace" placeholder="勿作空间" size="small">
        <el-option label="勿作空间" value=""></el-option>
        <el-option label="默认空间" value="default"></el-option>
        <el-option label="测试空间" value="test"></el-option>
      </el-select>

      <el-input
        v-model="searchKeyword"
        placeholder="请输入告警标题"
        size="small"
        style="width: 200px;"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="alert-table-header">
      <div class="alert-title-col">告警标题</div>
      <div class="alert-time-col">发生时间</div>
    </div>

    <div class="alert-list-container">
      <div v-if="!filteredAlerts.length" class="empty-alerts">
        <el-empty description="暂无关联告警" />
      </div>

      <div v-else class="alert-list">
        <div v-for="(alert, index) in filteredAlerts" :key="index" class="alert-item" @click="expandAlert(index)">
          <div class="alert-header" :class="{ 'expanded': expandedAlertIndex === alert.id }">
            <div class="alert-status">
              <div class="status-dot" :class="getSeverityClass(alert.alert_severity)"></div>
            </div>
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-time">{{ formatTime(alert.startsAt) }}</div>
            <div class="alert-expand">
              <el-icon v-if="expandedAlertIndex === alert.id"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
            </div>
          </div>

          <div v-if="expandedAlertIndex === alert.id" class="alert-content">
            <pre class="json-content">{{ JSON.stringify(alert, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { ArrowDown, ArrowUp, Refresh, Search } from '@element-plus/icons-vue';
import { getAlertExamples } from '@/api/mock/integration';

const props = defineProps({
  integrationId: {
    type: [Number, String],
    required: true
  }
});

// 筛选条件
const filterSeverity = ref('');
const filterPeriod = ref('30');
const filterSpace = ref('');
const searchKeyword = ref('');

// 告警示例数据
const alertExamples = ref([]);
const expandedAlertIndex = ref(null);

// 筛选后的告警列表
const filteredAlerts = computed(() => {
  return alertExamples.value.filter(alert => {
    // 按严重程度筛选
    if (filterSeverity.value && alert.alert_severity !== filterSeverity.value) {
      return false;
    }

    // 按标题关键词筛选
    if (searchKeyword.value && !alert.title.toLowerCase().includes(searchKeyword.value.toLowerCase())) {
      return false;
    }

    // 按时间筛选 (简化实现，实际应该比较日期)
    // 这里只是模拟，实际实现应该比较日期
    if (filterPeriod.value) {
      // 实际项目中应该根据日期进行筛选
      // 这里只是模拟
    }

    // 按空间筛选
    if (filterSpace.value && (!alert.labels || !alert.labels.namespace || !alert.labels.namespace.includes(filterSpace.value))) {
      return false;
    }

    return true;
  });
});

// 展开/收起告警详情
const expandAlert = (index) => {
  // 使用实际的告警对象来标识展开的告警
  const alertId = filteredAlerts.value[index]?.id || null;

  if (expandedAlertIndex.value === alertId) {
    expandedAlertIndex.value = null;
  } else {
    expandedAlertIndex.value = alertId;
  }
};

// 获取告警严重程度对应的样式类
const getSeverityClass = (severity) => {
  switch (severity?.toLowerCase()) {
    case 'critical':
      return 'severity-critical';
    case 'warning':
      return 'severity-warning';
    case 'info':
      return 'severity-info';
    default:
      return 'severity-info';
  }
};

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return '';

  const date = new Date(timeString);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${month}月${day}日 ${hours}:${minutes}:${seconds}`;
};

// 加载告警示例
const loadAlertExamples = async () => {
  try {
    const { success, data } = await getAlertExamples(props.integrationId);
    if (success) {
      alertExamples.value = data;
    }
  } catch (error) {
    console.error('加载告警示例失败:', error);
  }
};

// 监听集成ID变化
watch(() => props.integrationId, () => {
  loadAlertExamples();
}, { immediate: true });

// 组件挂载时加载数据
onMounted(() => {
  loadAlertExamples();
});
</script>

<style lang="scss" scoped>
.alert-association-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 50%;
  overflow: hidden;
  background-color: #fff;
  color: #303133;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.filter-bar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 10px;
  border-bottom: 1px solid #ebeef5;
}

.alert-table-header {
  display: flex;
  padding: 10px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  color: #606266;

  .alert-title-col {
    flex: 1;
  }

  .alert-time-col {
    width: 150px;
    text-align: right;
  }
}

.header-actions {
  display: flex;
  gap: 10px;
}

.alert-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #fff;
}

.empty-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 20px;
}

.alert-list {
  display: flex;
  flex-direction: column;
}

.alert-item {
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa;
  }
}

.alert-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;

  &.expanded {
    background-color: #f5f7fa;
  }
}

.alert-status {
  margin-right: 12px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;

  &.severity-critical {
    background-color: #f56c6c;
  }

  &.severity-warning {
    background-color: #e6a23c;
  }

  &.severity-info {
    background-color: #409eff;
  }
}

.alert-title {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.alert-time {
  width: 150px;
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  text-align: right;
}

.alert-expand {
  margin-left: 16px;
  color: #909399;
}

.alert-content {
  padding: 0;
  background-color: #f5f7fa;
  overflow-x: auto;
  border-top: 1px solid #ebeef5;
}

.json-content {
  margin: 0;
  padding: 16px;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  color: #303133;
}
</style>
