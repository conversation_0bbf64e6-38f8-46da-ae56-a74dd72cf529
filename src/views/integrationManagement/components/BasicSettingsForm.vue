<template>
  <div class="basic-settings-form">
    <el-form :model="formData" label-width="100px" :rules="rules" ref="formRef">
      <el-form-item label="集成名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入集成名称"></el-input>
      </el-form-item>
      <el-form-item label="集成类型" prop="typeId">
        <el-select 
          v-model="formData.typeId" 
          placeholder="请选择集成类型" 
          :disabled="mode === 'edit'"
          @change="handleTypeChange">
          <el-option
            v-for="item in integrationTypes"
            :key="item.id"
            :label="item.name"
            :value="item.id">
            <div class="type-option">
              <div :class="['option-icon', getIconColorClass(item.name)]">
                <component :is="item.icon" class="icon"></component>
              </div>
              <span>{{ item.name }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-switch
          v-model="formData.status"
          :active-value="'enabled'"
          :inactive-value="'disabled'">
        </el-switch>
        <span class="status-text">{{ formData.status === 'enabled' ? '启用' : '禁用' }}</span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  mode: {
    type: String,
    default: 'add', // 'add' 或 'edit'
    validator: (value) => ['add', 'edit'].includes(value)
  },
  formModel: {
    type: Object,
    default: () => ({
      id: null,
      name: '',
      typeId: null,
      type: '',
      status: 'enabled'
    })
  },
  integrationTypes: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:formModel', 'typeChange']);

// 表单数据
const formData = reactive({...props.formModel});

// 表单引用
const formRef = ref(null);

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入集成名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  typeId: [
    { required: true, message: '请选择集成类型', trigger: 'change' }
  ]
};

// 当props中的formModel变化时，更新本地formData
watch(() => props.formModel, (newVal) => {
  Object.assign(formData, newVal);
}, { deep: true });

// 监听formData变化，更新父组件的formModel
watch(formData, (newVal) => {
  emit('update:formModel', {...newVal});
}, { deep: true });

// 处理类型变更
const handleTypeChange = (typeId) => {
  // 找到对应的类型信息
  const selectedType = props.integrationTypes.find(item => item.id === typeId);
  if (selectedType) {
    formData.type = selectedType.name;
    emit('typeChange', selectedType);
  }
};

// 获取图标颜色类
const getIconColorClass = (type) => {
  const typeColorMap = {
    'Prometheus': 'prometheus-color',
    '阿里云监控 CM 事件': 'aliyun-color',
    'Grafana': 'grafana-color',
    'Zabbix': 'zabbix-color',
    '邮件 Email': 'email-color',
    '夜莺/Flashcat': 'flashcat-color',
    '标准告警事件': 'standard-color'
  };
  return typeColorMap[type] || 'default-color';
};

// 对外暴露validate方法供父组件调用
defineExpose({
  validate: async () => {
    if (!formRef.value) return false;
    return await formRef.value.validate()
      .then(() => true)
      .catch(() => false);
  }
});
</script>

<style lang="scss" scoped>
.basic-settings-form {
  padding: 20px 0;
}

.type-option {
  display: flex;
  align-items: center;
  gap: 10px;
}

.option-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.prometheus-color { background-color: #e74c3c; }
  &.aliyun-color { background-color: #ff6a00; }
  &.grafana-color { background-color: #f7941d; }
  &.zabbix-color { background-color: #d40000; }
  &.email-color { background-color: #3498db; }
  &.flashcat-color { background-color: #2c3e50; }
  &.standard-color { background-color: #4a69bd; }
  &.default-color { background-color: #95a5a6; }
}

.icon {
  font-size: 14px;
  color: white;
}

.status-text {
  margin-left: 10px;
  color: #666;
}
</style> 