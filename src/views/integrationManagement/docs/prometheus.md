# Prometheus集成指南

## 概述
Prometheus是一个开源的系统监控和警报工具包，最初是在SoundCloud构建的。自2012年成立以来，许多公司和组织已经采用了Prometheus，该项目有一个非常活跃的开发者和用户社区。

## 配置步骤
### 1. 安装并配置Prometheus
确保您已经正确安装了Prometheus服务器。您可以从[Prometheus官方网站](https://prometheus.io/download/)下载最新版本。

### 2. 配置AlertManager
AlertManager处理由客户端应用程序（如Prometheus服务器）发送的警报。它负责重复数据删除、分组和路由到正确的接收器。

```yaml
global:
  resolve_timeout: 5m

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'flashcat'

receivers:
- name: 'flashcat'
  webhook_configs:
  - url: 'YOUR_INTEGRATION_PUSH_URL'
    send_resolved: true
```

### 3. 创建告警规则
在Prometheus中配置告警规则，例如：

```yaml
groups:
- name: example
  rules:
  - alert: HighCPULoad
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU load (instance {{ $labels.instance }})"
      description: "CPU负载超过80%\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
```

## 告警示例
当CPU负载超过阈值时，AlertManager将发送如下格式的告警到zeroduty：

```json
{
  "version": "4",
  "groupKey": "{}:{alertname=\"HighCPULoad\"}",
  "status": "firing",
  "receiver": "flashcat",
  "groupLabels": {
    "alertname": "HighCPULoad"
  },
  "commonLabels": {
    "alertname": "HighCPULoad",
    "severity": "warning"
  },
  "commonAnnotations": {
    "description": "CPU负载超过80%\n  VALUE = 87.5\n  LABELS = map[instance:server01]",
    "summary": "High CPU load (instance server01)"
  },
  "externalURL": "http://prometheus:9093",
  "alerts": [
    {
      "status": "firing",
      "labels": {
        "alertname": "HighCPULoad",
        "instance": "server01",
        "severity": "warning"
      },
      "annotations": {
        "description": "CPU负载超过80%\n  VALUE = 87.5\n  LABELS = map[instance:server01]",
        "summary": "High CPU load (instance server01)"
      },
      "startsAt": "2023-01-01T00:00:00.000Z",
      "endsAt": "0001-01-01T00:00:00.000Z",
      "generatorURL": "http://prometheus:9090/graph?g0.expr=100+-+%28avg+by%28instance%29+%28irate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29+%3E+80",
      "fingerprint": "ce3f3600ef3b86d1"
    }
  ]
}
```

## 故障排除
如果您没有收到告警，请检查：
1. Prometheus服务器是否正常运行
2. AlertManager是否正确配置
3. 网络连接是否正常
4. 集成地址是否正确

更多信息，请参考[Prometheus官方文档](https://prometheus.io/docs/introduction/overview/)。 