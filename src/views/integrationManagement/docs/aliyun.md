# 阿里云监控集成指南

## 概述
阿里云监控服务可以监控阿里云资源的指标、事件、自定义监控数据等，帮助您实时掌握系统状态并在出现异常时触发告警。

## 配置步骤
### 1. 登录阿里云控制台
访问[阿里云控制台](https://home.console.aliyun.com/)并使用您的账号登录。

### 2. 配置云监控服务
1. 在控制台首页找到并点击"云监控"服务
2. 在左侧导航栏选择"报警服务" > "报警规则"
3. 点击"创建报警规则"按钮

### 3. 设置推送通知方式
1. 创建报警规则时，在通知方式中选择"WebHook"
2. 在推送地址中填入本集成系统提供的推送URL
3. 完成报警规则设置并保存

## 告警示例
当触发告警条件时，阿里云监控会发送如下格式的告警到集成系统：

```json
{
  "alertName": "CPU使用率",
  "alertState": "ALERT",
  "dimensions": {
    "userId": "12345",
    "instanceId": "i-abcdefg"
  },
  "expression": "表达式",
  "metricName": "cpu_total",
  "metricProject": "acs_ecs_dashboard",
  "namespace": "acs_ecs_dashboard",
  "curValue": "95.6",
  "triggerLevel": "WARN",
  "triggeredTime": 1612345678000
}
```

## 故障排除
如果您没有收到告警，请检查：
1. 阿里云账号权限是否配置正确
2. 报警规则是否正确设置
3. 推送URL是否正确填写
4. 网络连接是否正常

更多信息，请参考[阿里云监控官方文档](https://help.aliyun.com/product/28572.html)。 