// 导入所有markdown文件
// 相对路径导入当前目录下的markdown文件
const markdownFiles = import.meta.glob('../components/markdown/*.md?raw', { eager: true })

// 添加明确的导入，确保文件被正确加载
import notifyAppMd from '../components/markdown/notify-app.md?raw'
import kingsoftAppMd from '../components/markdown/kingsoft-app.md?raw'
import kingsoftRobotMd from '../components/markdown/kingsoft-robot.md?raw'
import emailAppMd from '../components/markdown/email-app.md?raw'
// 直接映射文件，避免路径解析问题
const directMappings = {
  'notify-app': notifyAppMd,
  'kingsoft-app': kingsoftAppMd,
  'kingsoft-robot': kingsoftRobotMd,
  'email-app': emailAppMd,
}

// 调试信息：打印已加载的文件
console.log('已加载的Markdown文件:', Object.keys(markdownFiles))
console.log('直接映射的文件:', Object.keys(directMappings))

// 获取文件名称列表（不含扩展名）
export const getMarkdownFileList = () => {
  // 返回直接映射的文件列表
  return Object.keys(directMappings)
}

// 获取指定名称的Markdown文档内容
export const getMarkdownDoc = async (name) => {
  try {
    console.log('请求加载的文档名称:', name)
    
    // 首先检查直接映射
    if (directMappings[name]) {
      console.log('从直接映射中找到文档:', name)
      return {
        success: true,
        data: directMappings[name]
      }
    }
    
    // 如果直接映射中没有，尝试从glob导入中查找
    console.log('在glob导入中查找文档:', name)
    const targetPath = Object.keys(markdownFiles).find(path => {
      const fileName = path.split('/').pop().replace('.md', '')
      console.log('比较文件名:', fileName, '与请求名称:', name)
      return fileName === name
    })
    
    if (targetPath) {
      console.log('在glob导入中找到文档:', targetPath)
      return {
        success: true,
        data: markdownFiles[targetPath]
      }
    }
    
    // 如果没有找到对应的文件，返回默认内容
    console.warn(`找不到对应的Markdown文件: ${name}`)
    const defaultContent = `# ${name.replace(/-/g, ' ').replace(/\b\w/g, c => c.toUpperCase())} 文档\n\n暂无文档内容，请联系管理员添加。`
    return {
      success: false,
      message: `找不到对应的Markdown文件: ${name}`,
      data: defaultContent
    }
  } catch (error) {
    console.error('获取Markdown文档失败:', error)
    return {
      success: false,
      message: error.message,
      data: '# 加载失败\n\n文档加载失败，请稍后重试。'
    }
  }
} 