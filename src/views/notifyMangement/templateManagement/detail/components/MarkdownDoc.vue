<template>
  <div class="doc-viewer-container">
    <div v-if="loading" class="loading">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else class="doc-viewer">
      <div v-html="renderedContent" class="markdown-content"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
import { getMarkdownDoc } from '../services/markdownService'

// 配置marked使用highlight.js进行代码高亮
marked.setOptions({
  gfm: true,
  breaks: true,
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) {
        console.error(err)
      }
    }
    return hljs.highlightAuto(code).value
  }
})

defineOptions({
  name: 'MarkdownDoc'
})

const props = defineProps({
  docName: {
    type: String,
    required: true
  }
})

const loading = ref(true)
const renderedContent = ref('')

// 加载Markdown文件
const loadMarkdownFile = async (name) => {
  loading.value = true
  try {
    // 使用markdownService获取文档
    const result = await getMarkdownDoc(name)
    
    if (result.success) {
      renderedContent.value = marked(result.data)
    } else {
      console.warn(`文档${name}加载失败:`, result.message)
      renderedContent.value = marked(result.data) // 显示默认内容
    }
  } catch (error) {
    console.error('渲染Markdown文件失败:', error)
    renderedContent.value = '<div class="error-message">加载文档失败</div>'
  } finally {
    loading.value = false
  }
}

// 监听文档名称变化
watch(() => props.docName, (newVal) => {
  if (newVal) {
    loadMarkdownFile(newVal)
  }
})

onMounted(() => {
  loadMarkdownFile(props.docName)
})
</script>

<style lang="scss" scoped>
.doc-viewer-container {
  height: 100%;
  overflow: hidden;
  border-top: none;
  margin-top: 0;
  padding-top: 0;
  background-color: #f5f7fa;
}

.loading {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin: 10px;
}

.doc-viewer {
  padding: 20px;
  height: 100%;
  overflow-y: auto !important;
  color: #333;
  position: relative;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin: 10px;
}

.error-message {
  color: #f56c6c;
  text-align: center;
  padding: 20px;
}

:deep(.markdown-content) {
  height: auto !important;
  min-height: 100%;
  overflow-y: visible;
  padding-right: 5px;
  padding-bottom: 40px;

  h1 {
    font-size: 24px;
    margin-top: 0;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
  }
  
  h2 {
    font-size: 20px;
    margin-top: 24px;
    margin-bottom: 16px;
    color: #303133;
  }
  
  h3 {
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 14px;
    color: #303133;
  }
  
  p {
    margin: 14px 0;
    line-height: 1.6;
    color: #606266;
  }
  
  code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    background-color: #f5f7fa;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 14px;
    color: #f56c6c;
  }
  
  pre {
    background-color: #f5f7fa;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 16px 0;
    border: 1px solid #e4e7ed;
    white-space: pre-wrap;
    max-width: 100%;
    word-break: break-all;
    
    code {
      background-color: transparent;
      padding: 0;
      font-size: 14px;
      line-height: 1.5;
      color: #606266;
      white-space: pre-wrap;
    }
  }
  
  ul, ol {
    padding-left: 24px;
    margin: 16px 0;
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
      color: #606266;
    }
  }
  
  a {
    color: #409eff;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  blockquote {
    border-left: 4px solid #409eff;
    margin: 16px 0;
    padding: 0 16px;
    color: #909399;
    background-color: #f5f7fa;
  }
  
  table {
    border-collapse: collapse;
    margin: 15px 0;
    width: 100%;
    
    th, td {
      border: 1px solid #dcdfe6;
      padding: 8px 12px;
    }
    
    th {
      background-color: #f5f7fa;
      font-weight: 500;
    }
    
    tr:nth-child(even) {
      background-color: #fafafa;
    }
  }
}
</style> 