<template>
  <div class="template-content-wrapper">
    <template-form 
      :template-data="templateData" 
      :active-tab="activeTab" 
      @show-preview="$emit('show-preview', $event)" 
    />
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import TemplateForm from './TemplateForm.vue'

defineOptions({
  name: 'TemplateContent'
})

defineProps({
  templateData: {
    type: Object,
    required: true
  },
  activeTab: {
    type: String,
    required: true
  }
})

defineEmits(['show-preview'])
</script>

<style scoped>
.template-content-wrapper {
  height: 100%;
  width: 100%;
  overflow: auto;
}
</style> 