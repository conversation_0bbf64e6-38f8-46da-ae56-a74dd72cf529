<template>
  <div class="template-content">
    <div class="template-header">
      <h3 class="template-title">{{ getTemplateTitle() }}</h3>
    </div>
    <div class="editor-container">
      <template-editor 
        v-model="content" 
        height="calc(100vh - 320px)" 
        language="template"
      />
    </div>
    <div class="template-footer">
      <el-button type="primary" @click="handleSave">保存配置</el-button>
      <el-button @click="handlePreview">预览效果</el-button>
      <el-button type="success">测试发送</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue'
import TemplateEditor from '@/components/TemplateEditor/index.vue'
import { ElMessage } from 'element-plus'

// 定义组件接收的属性
const props = defineProps({
  templateId: {
    type: String,
    default: ''
  },
  activeTab: {
    type: String,
    default: 'notify-app'
  }
})

// 定义事件
const emit = defineEmits(['show-preview'])

// 表单数据
const content = ref('')

// 获取模板标题
const getTemplateTitle = () => {
  const titles = {
    'notify-app': '通知应用模板内容',
    'kingsoft-app': '金山协作应用模板内容',
    'kingsoft-robot': '金山协作机器人模板内容',
    'email-app': '邮件应用模板内容',
    'feishu-app': '飞书应用模板内容',
    'wechat-app': '微信应用模板内容',
    'dingtalk-app': '钉钉应用模板内容'
  }
  return titles[props.activeTab] || '模板内容'
}

// 编辑器自动补全建议
// const templateSuggestions = [
//   {
//     label: 'fireReason',
//     detail: '告警原因',
//     documentation: '告警的触发原因',
//     insertText: 'fireReason'
//   },
//   {
//     label: 'Title',
//     detail: '标题',
//     documentation: '告警标题',
//     insertText: 'Title'
//   },
//   {
//     label: 'Num',
//     detail: '编号',
//     documentation: '告警编号',
//     insertText: 'Num'
//   },
//   {
//     label: 'DetailUrl',
//     detail: '详情链接',
//     documentation: '告警详情链接',
//     insertText: 'DetailUrl'
//   },
//   {
//     label: 'ChannelName',
//     detail: '告警渠道',
//     documentation: '告警通知渠道',
//     insertText: 'ChannelName'
//   },
//   {
//     label: 'IncidentSeverity',
//     detail: '严重程度',
//     documentation: '事件严重程度',
//     insertText: 'IncidentSeverity'
//   },
//   {
//     label: 'AlertCnt',
//     detail: '告警数量',
//     documentation: '相关联的告警数量',
//     insertText: 'AlertCnt'
//   },
//   {
//     label: 'Responders',
//     detail: '处理人',
//     documentation: '告警处理人列表',
//     insertText: 'Responders'
//   },
//   {
//     label: 'PersonName',
//     detail: '人员姓名',
//     documentation: '人员姓名',
//     insertText: 'PersonName'
//   },
//   {
//     label: 'Progress',
//     detail: '处理进度',
//     documentation: '告警处理进度',
//     insertText: 'Progress'
//   },
//   {
//     label: 'IncidentStatus',
//     detail: '故障状态',
//     documentation: '事件故障状态',
//     insertText: 'IncidentStatus'
//   },
//   {
//     label: 'check',
//     detail: '检查项',
//     documentation: '告警检查项',
//     insertText: 'check'
//   },
//   {
//     label: 'alertname',
//     detail: '告警名称',
//     documentation: '告警的名称',
//     insertText: 'alertname'
//   },
//   {
//     label: 'instance',
//     detail: '实例',
//     documentation: '告警发生的实例',
//     insertText: 'instance'
//   },
//   {
//     label: 'resource',
//     detail: '资源',
//     documentation: '告警相关的资源',
//     insertText: 'resource'
//   }
// ]

// Mock API 请求模板数据
const getTemplateData = () => {
  // 模拟不同应用类型的API请求
  console.log('加载模板ID:', props.templateId, '应用类型:', props.activeTab)
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = {
        'notify-app': `{
  "msg_type": "interactive",
  "card": {
    "config": {
      "wide_screen_mode": true,
      "enable_forward": true
    },
    "header": {
      "template": "{{if eq .IncidentSeverity "Critical"}}red{{else if eq .IncidentSeverity "Warning"}}orange{{else}}yellow{{end}}",
      "title": {
        "content": "{{fireReason .}}INC #{{.Num}} {{toHtml .Title}}",
        "tag": "plain_text"
      }
    },
    "elements": [{
      "tag": "div",
      "fields": [{
          "text": {
            "tag": "lark_md",
            "content": "**🏢 协作空间:**{{if .ChannelName}}{{.ChannelName}}{{else}}无{{end}}"
          }
        },
        {
          "text": {
            "tag": "lark_md",
            "content": "**{{if eq .IncidentSeverity "Critical"}}🔴{{else if eq .IncidentSeverity "Warning"}}⚠️{{else}}ℹ️{{end}} 严重程度:**{{.IncidentSeverity}}"
          }
        },
        {
          "text": {
            "tag": "lark_md",
            "content": "**⏰ 触发时间:**{{date "2006-01-02 15:04:05" .StartTime}}{{if gt .AlertCnt 1}}"
          }
        },
        {
          "text": {
            "tag": "lark_md",
            "content": "**🔔 聚合告警:**{{.AlertCnt}}条 {{end}}{{if .Labels.resource}}"
          }
        },
        {
          "tag": "div",
          "text": {
            "tag": "lark_md",
            "content": "**📌 告警对象:**{{toHtml (joinAlertLabels . "resource" ",")}} {{end}}{{if .Description}}"
          }
        },
        {
          "tag": "div",
          "text": {
            "tag": "lark_md",
            "content": "**🔍 故障描述:**{{.Description}}{{end}}{{if gt (len .Responders) 0}}"
          }
        },
        {
          "tag": "div",
          "text": {
            "tag": "lark_md",
            "content": "**👨‍💻 处理人员:**{{range .Responders}}@{{.PersonName}} {{end}}{{end}}"
          }
        }
      ]
    },
    {
      "tag": "hr"
    },
    {
      "tag": "action",
      "actions": [{
          "tag": "button",
          "text": {
            "tag": "plain_text",
            "content": "故障详情"
          },
          "type": "primary",
          "url": "{{.DetailUrl}}"
        },
        {
          "tag": "button",
          "text": {
            "tag": "plain_text",
            "content": "认领"
          },
          "type": "primary",
          "url": "{{.DetailUrl}}?ack=1"
        }
      ]
    }]
  }
}`,
        'kingsoft-app': `【金山协作通知】
告警名称: {{.Annotations.summary}}
告警级别: {{.Labels.severity}}
告警时间: {{.StartsAt}}
告警描述: {{.Annotations.description}}
{{if .Labels.resource}}资源: {{(joinAlertLabels . "resource" ", ")}}{{end}}`,
        'kingsoft-robot': `{
  "msgtype": "markdown",
  "markdown": {
    "title": "告警通知",
    "text": "### 【告警通知】{{.Labels.alertname}}\n**级别**: {{.Labels.severity}}\n**描述**: {{.Annotations.description}}\n**时间**: {{.StartsAt}}\n{{if .Labels.resource}}**资源**: {{(joinAlertLabels . "resource" ", ")}}{{end}}"
  }
}`,
        'email-app': `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>{{.Title}}</title>
<html lang="zh">

  <head data-id="__react-email-head">
	<style>
	  /* Light mode colors */
	  :root {
		--bg-color: rgb(255, 255, 255);
		--text-color: rgb(55, 65, 81);
		--label-key-color: #000;
		--section-bg: rgb(243, 244, 246);
		--border-color: rgb(229, 231, 235);
	  }

	  /* Dark mode colors */
	  @media (prefers-color-scheme: dark) {
		:root {
		  --bg-color: rgb(31, 41, 55);
		  --text-color: rgb(209, 213, 219);
		  --label-key-color: rgb(229, 231, 235);
		  --section-bg: rgb(17, 24, 39);
		  --border-color: rgb(75, 85, 99);
		}
	  }

	  .bg-Critical { background-color: #C80000; }
	  .bg-Warning { background-color: #FA7D00; }
	  .bg-Info { background-color: #6C53B1; }
	  .bg-Ok { background-color: rgb(132 204 22); }
	  .text-Critical { color: #ff4444; }
	  .text-Warning { color: #ffaa44; }
	  .text-Info { color: #9f7aea; }
	  .text-Ok { color: rgb(132 204 22); }

	  /* Label layout styles */
	  .label-row {
		display: flex;
		margin-bottom: 0.5rem;
		width: 100%;
		flex-wrap: nowrap;
	  }
	  .label-key {
		color: var(--label-key-color);
		font-weight: 500;
		width: 100px;
		min-width: 100px;
		margin-right: 1rem;
	  }
	  .label-value {
		color: var(--text-color);
		flex: 1;
		word-break: break-word;
		overflow-wrap: break-word;
	  }

	  body {
		background-color: var(--bg-color);
		color: var(--text-color);
	  }

	  .incident-section {
		background-color: var(--section-bg);
		border: 1px solid var(--border-color);
	  }

	  .view-details-btn {
		background-color: var(--bg-color) !important;
		color: var(--text-color) !important;
		border-color: var(--border-color) !important;
	  }
	</style>
  </head>

  <body data-id="__react-email-body" style="border-radius:0.25rem;margin-top:2.5rem;margin-bottom:2.5rem;margin-left:auto;margin-right:auto;padding:1rem;min-width:400px;max-width:660px;font-family:ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji">
	<div style="width:100%;height:0.375rem;margin-bottom:2rem" class="bg-{{.IncidentSeverity}}"></div>
	<div style="display:flex;align-items:center;margin-bottom:1.5rem">
	  <div style="display:flex;"><img width="120" data-id="react-email-img" src="" height="40" style="margin-right:1rem;display:block;outline:none;border:none;text-decoration:none" /><span style="margin-top:0.5rem;font-size:1.25rem;line-height:1.75rem;font-weight:600">您有故障待处理</span></div>
	</div>
	<div class="incident-section" style="padding:2rem;margin-top:1rem;border-radius:0.5rem">
	  <div>
		<div class="label-row">
		  <div class="label-key">故障标题</div>
		  <div class="label-value">{{.Title}}</div>
		</div>
		<div class="label-row">
		  <div class="label-key">严重程度</div>
		  <div class="label-value text-{{.IncidentSeverity}}">{{.IncidentSeverity}}</div>
		</div>
		<div class="label-row">
		  <div class="label-key">协作空间</div>
		  <div class="label-value">{{if .ChannelName}}{{.ChannelName}}{{else}}无{{end}}</div>
		</div>
		<div class="label-row">
		  <div class="label-key">触发时间</div>
		  <div class="label-value">{{date "2006-01-02 15:04:05" .StartTime}}</div>
		</div>
		{{if .CreatorID}}
		<div class="label-row">
		  <div class="label-key">发起人员</div>
		  <div class="label-value">{{.Creator.PersonName}}</div>
		</div>
		{{end}}
		{{if gt (len .Responders) 0}}
		<div class="label-row">
		  <div class="label-key">处理人员</div>
		  <div class="label-value">{{range .Responders}}@{{.PersonName}} {{end}}</div>
		</div>
		{{end}}
		<div class="label-row">
		  <div class="label-key">处理进度</div>
		  <div class="label-value">{{.Progress}}</div>
		</div>
		<div class="label-row">
		  <div class="label-key">故障描述</div>
		  <div class="label-value">
			<div data-id="react-email-markdown">{{if .Labels.body_text}}{{.Labels.body_text}}{{else if .Description}}{{.Description}}{{end}}</div>
		  </div>
		</div>
		{{if .Labels.resource}}
		<div class="label-row">
		  <div class="label-key">告警对象</div>
		  <div class="label-value">
			<div data-id="react-email-markdown">{{(joinAlertLabels . "resource" ", ")}}</div>
		  </div>
		</div>
		{{end}}
	  </div>
	  <div style="display:flex;margin-top:2rem">
		<a href="{{.DetailUrl}}?ack=1" data-id="react-email-button" target="_blank" style="margin-right:1rem;line-height:100%;text-decoration:none;display:inline-block;max-width:100%;padding:0px 0px">
		  <span style="max-width:100%;display:inline-block;line-height:120%;mso-padding-alt:0px;mso-text-raise:0">
			<div style="padding-left:2rem;padding-right:2rem;padding-top:0.5rem;padding-bottom:0.5rem;background-color:rgb(108,83,177);border-radius:0.25rem;font-size:1rem;line-height:1.5rem;color:rgb(255,255,255);font-weight:600;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms">
			  立即认领
			</div>
		  </span>
		</a>
		<a href="{{.DetailUrl}}" data-id="react-email-button" target="_blank" style="line-height:100%;text-decoration:none;display:inline-block;max-width:100%;padding:0px 0px">
		  <span style="max-width:100%;display:inline-block;line-height:120%;mso-padding-alt:0px;mso-text-raise:0">
			<div class="view-details-btn" style="padding-left:2rem;padding-right:2rem;padding-top:0.5rem;padding-bottom:0.5rem;border-width:1px;border-style:solid;border-radius:0.25rem;font-size:1rem;line-height:1.5rem;font-weight:600">
			  查看详情
			</div>
		  </span>
		</a>
	  </div>
	</div>
	<div style="display:flex;justify-content:flex-end;align-items:flex-end;margin-top:2rem">
	  <div style="font-size:0.875rem;line-height:1.25rem;font-weight:500">版权所有 © SeasunGames</div>
	</div>
  </body>

</html>`,
        'default': `{{if .Labels.body_text}}**description** :{{.Labels.body_text}}{{else if .Description}}**description** :{{.Description}}{{end}}
{{if .Labels.resource}}**resource** : {{(joinAlertLabels . "resource" ", ")}}{{end}}
{{range $k, $v := .Labels}}
{{if not (in $k "resource" "body_text" "body_text_with_table")}}**{{$k}}** : {{$v}}{{end}}{{end}}`
      }
      
      resolve({
        content: templates[props.activeTab] || templates.default
      })
    }, 300)
  })
}

// Mock API 保存模板数据
const saveTemplateData = (templateData) => {
  // 模拟API请求延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('保存的模板数据:', templateData, '模板ID:', props.templateId, '应用类型:', props.activeTab)
      resolve({ success: true })
    }, 300)
  })
}

// 监听 activeTab 变化，重新加载模板数据
watch(() => props.activeTab, async () => {
  try {
    const data = await getTemplateData()
    content.value = data.content
  } catch (error) {
    console.error('获取模板数据失败:', error)
    ElMessage.error('获取模板数据失败')
  }
})

// 初始化数据
onMounted(async () => {
  try {
    const data = await getTemplateData()
    content.value = data.content
  } catch (error) {
    console.error('获取模板数据失败:', error)
    ElMessage.error('获取模板数据失败')
  }
})

// 保存模板配置
const handleSave = async () => {
  try {
    await saveTemplateData({
      content: content.value,
      appType: props.activeTab
    })
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存模板数据失败:', error)
    ElMessage.error('保存失败')
  }
}

// 预览模板效果
const handlePreview = () => {
  emit('show-preview', {
    content: content.value,
    appType: props.activeTab
  })
}
</script>

<style scoped>
.template-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.template-header {
  margin-bottom: 16px;
}

.template-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.editor-container {
  flex: 1;
  margin-bottom: 16px;
}

.template-footer {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}
</style> 