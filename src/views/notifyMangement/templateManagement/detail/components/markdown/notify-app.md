# 通知应用指南

## 简介

通知应用是系统将告警信息传递给接收方的媒介。本文档介绍了如何配置和使用不同类型的通知应用。

## 支持的通知应用类型

系统目前支持以下通知应用类型：

| 应用类型 | 说明 |
|---------|------|
| 邮件 | 通过电子邮件发送告警通知 |
| 短信 | 通过手机短信发送告警通知 |
| 钉钉 | 通过钉钉机器人发送告警通知 |
| 企业微信 | 通过企业微信机器人发送告警通知 |
| 飞书 | 通过飞书机器人发送告警通知 |
| Webhook | 通过HTTP回调发送告警通知 |

## 配置说明

### 钉钉配置

1. 获取钉钉机器人Webhook地址：
   - 在钉钉群中添加自定义机器人
   - 设置安全设置（关键词或IP地址）
   - 复制Webhook地址

2. 填写配置信息：
   - Webhook地址：粘贴从钉钉获取的URL
   - 安全设置：如使用加签方式，需填写签名密钥

### 企业微信配置

1. 获取企业微信机器人Webhook地址：
   - 在企业微信群中添加机器人
   - 复制机器人的Webhook地址

2. 填写配置信息：
   - Webhook地址：粘贴从企业微信获取的URL

## 模板变量

所有通知应用均支持以下模板变量：

- `{{ alertname }}` - 告警名称
- `{{ severity }}` - 告警级别
- `{{ startTime }}` - 告警开始时间
- `{{ summary }}` - 告警摘要
- `{{ description }}` - 告警详细描述

## 注意事项

1. 部分通知渠道（如短信）对内容长度有限制，请避免过长的模板内容
2. 建议在正式使用前进行通知测试，确保配置正确
3. 不同的通知应用对格式化支持不同，请根据实际情况调整模板

## 使用示例

### HTML格式模板示例

```html
<div style="font-family: Arial, sans-serif; padding: 20px; border: 1px solid #e6e6e6; border-radius: 5px;">
  <h2 style="color: #333;">告警通知: {{alertname}}</h2>
  <p><strong>时间：</strong>{{time}}</p>
  <p><strong>级别：</strong>{{severity}}</p>
  <p><strong>描述：</strong>{{description}}</p>
  <p><strong>资源：</strong>{{resource}}</p>
  <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e6e6e6;">
    <a href="{{dashboard_url}}" style="display: inline-block; padding: 10px 20px; background-color: #1890ff; color: white; text-decoration: none; border-radius: 4px;">查看详情</a>
  </div>
</div>
```

### 纯文本格式模板示例

```
【告警通知】{{alertname}}
--------------------------------------------------------
发生时间: {{time}}
严重程度: {{severity}}
状态: {{status}}
资源: {{resource}}
实例: {{instance}}
--------------------------------------------------------
描述: {{description}}
摘要: {{summary}}
--------------------------------------------------------
详情链接: {{dashboard_url}}
```

## 最佳实践

1. **模板简洁明了**
   - 突出重要信息，避免冗余内容
   - 使用适当的格式和颜色区分信息优先级
   - 包含必要的操作链接，便于快速响应

2. **通知分级**
   - 合理设置通知级别，避免过多的紧急通知导致告警疲劳
   - 对不同级别的通知使用不同的推送渠道和提醒方式

3. **减少噪音**
   - 设置合理的通知条件和阈值
   - 对相似问题进行聚合，避免重复通知
   - 实现智能静默和抑制机制，减少非工作时间的不必要通知

## 故障排查

1. **通知未发送**
   - 检查通知模板配置是否正确
   - 验证推送渠道的连接状态
   - 查看系统日志中的错误信息

2. **变量替换失败**
   - 确认变量名称拼写正确，区分大小写
   - 检查数据源中是否包含所需的字段
   - 验证模板语法是否符合规范

3. **格式显示异常**
   - 对于HTML格式，检查标签是否闭合
   - 确认接收端是否支持所使用的格式
   - 测试不同设备和客户端上的显示效果

## 更多资源

- [通知模板语法详解](https://example.com/notification-template-syntax)
- [变量占位符完整列表](https://example.com/variable-placeholders)
- [通知系统API文档](https://example.com/notification-api-docs) 