# 金山协作应用集成说明

## 简介

金山协作应用是一种快速与金山办公套件集成的方式，让您的系统可以无缝对接金山文档、WPS等办公软件。

## 配置步骤

1. **创建应用**
   - 登录金山开放平台
   - 选择"创建应用"
   - 填写应用基本信息

2. **获取API凭证**
   - 应用创建成功后，获取AppID和AppSecret
   - 保存这些凭证，后续API调用时需要使用

3. **配置回调URL**
   - 在应用设置中配置授权回调URL
   - 确保URL是HTTPS协议
   - 测试回调URL可正常访问

## API接口说明

### 文档操作

```javascript
// 获取文档列表示例
async function getDocumentList() {
  const response = await fetch('/api/kingsoft/documents', {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer ' + accessToken,
      'Content-Type': 'application/json'
    }
  });
  return await response.json();
}

// 创建新文档示例
async function createDocument(title, content) {
  const response = await fetch('/api/kingsoft/documents', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + accessToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      title: title,
      content: content
    })
  });
  return await response.json();
}
```

### 协作功能

金山协作应用支持实时多人协作编辑功能，可以通过以下API进行操作：

```javascript
// 获取协作用户列表
async function getCollaborators(documentId) {
  const response = await fetch(`/api/kingsoft/documents/${documentId}/collaborators`, {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer ' + accessToken,
      'Content-Type': 'application/json'
    }
  });
  return await response.json();
}

// 邀请协作用户
async function inviteCollaborator(documentId, email, permission) {
  const response = await fetch(`/api/kingsoft/documents/${documentId}/collaborators`, {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + accessToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: email,
      permission: permission // 'edit', 'comment', 'view'
    })
  });
  return await response.json();
}
```

## 常见问题

1. **授权失败**
   - 检查AppID和AppSecret是否正确
   - 确认回调URL是否配置正确
   - 检查用户是否取消了授权操作

2. **API调用失败**
   - 确认accessToken是否过期
   - 检查API请求参数是否正确
   - 查看错误响应的详细信息

3. **文档同步问题**
   - 检查网络连接
   - 确认文档ID是否正确
   - 检查用户对该文档是否有足够的权限

## 支持与联系

如需技术支持，请联系：<EMAIL>

更多API文档和示例，请访问：[金山开放平台开发者中心](https://open.wps.cn/) 