# 邮件应用模板

邮件应用模板允许您配置发送给用户的电子邮件通知。这些模板使用HTML格式，支持响应式设计，并针对各种邮件客户端进行了优化。

## 模板变量

在邮件模板中，您可以使用以下变量来动态填充内容：

| 变量名 | 描述 |
|-------|------|
| {{.Title}} | 故障或告警的标题 |
| {{.IncidentSeverity}} | 故障的严重程度（Critical/Warning/Info/Ok） |
| {{.ChannelName}} | 关联的协作空间名称 |
| {{.StartTime}} | 故障开始时间 |
| {{.Creator.PersonName}} | 故障创建者姓名 |
| {{.Responders}} | 故障处理人员列表 |
| {{.Progress}} | 故障处理进度 |
| {{.Description}} | 故障描述 |
| {{.Labels.body_text}} | 告警正文内容 |
| {{.Labels.resource}} | 告警资源信息 |
| {{.DetailUrl}} | 故障详情页面URL |

## 日期格式化

您可以使用以下语法格式化日期：

```
{{date "2006-01-02 15:04:05" .StartTime}}
```

## CSS样式

邮件模板支持内嵌CSS样式，并包含针对亮色和暗色模式的适配：

```css
/* 亮色模式样式 */
:root {
  --bg-color: rgb(255, 255, 255);
  --text-color: rgb(55, 65, 81);
  /* 其他变量... */
}

/* 暗色模式样式 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: rgb(31, 41, 55);
    --text-color: rgb(209, 213, 219);
    /* 其他变量... */
  }
}
```

## 严重程度样式

模板包含四种严重程度的预定义样式：

- Critical: 红色 (#C80000)
- Warning: 橙色 (#FA7D00)
- Info: 紫色 (#6C53B1)
- Ok: 绿色 (rgb(132 204 22))

您可以使用 `bg-{严重程度}` 和 `text-{严重程度}` 类来应用这些样式。

## 示例

下面是一个基本的邮件模板示例：

```html
<div style="width:100%;height:0.375rem;margin-bottom:2rem" class="bg-{{.IncidentSeverity}}"></div>
<div style="display:flex;align-items:center;margin-bottom:1.5rem">
  <div style="display:flex;"><span style="font-size:1.25rem;font-weight:600">您有故障待处理</span></div>
</div>
<div class="incident-section" style="padding:2rem;margin-top:1rem;border-radius:0.5rem">
  <div>
    <div class="label-row">
      <div class="label-key">故障标题</div>
      <div class="label-value">{{.Title}}</div>
    </div>
    <div class="label-row">
      <div class="label-key">严重程度</div>
      <div class="label-value text-{{.IncidentSeverity}}">{{.IncidentSeverity}}</div>
    </div>
    <!-- 其他字段... -->
  </div>
</div>
```

## 注意事项

1. 邮件HTML需要使用内联样式以确保在各邮件客户端中正确渲染
2. 避免使用复杂的JavaScript或高级CSS功能
3. 测试您的模板在不同邮件客户端中的显示效果
4. 图片应使用绝对URL路径 