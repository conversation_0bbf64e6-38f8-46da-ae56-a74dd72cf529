# 金山机器人集成指南

## 简介

金山机器人是基于金山办公产品生态系统的自动化工具，可以帮助团队实现工作流自动化、智能通知和数据处理等功能。通过金山机器人，您可以让系统自动响应特定事件，执行预定义的操作，提高团队协作效率。

## 配置步骤

1. **创建机器人**
   - 登录金山办公管理控制台
   - 导航至"机器人管理"
   - 点击"新建机器人"
   - 填写机器人名称、描述和图标

2. **设置机器人权限**
   - 选择机器人可访问的团队空间
   - 配置机器人的权限范围（文档读写、消息发送等）
   - 保存权限设置

3. **获取机器人令牌**
   - 创建完成后，系统将生成机器人的API令牌
   - 保存此令牌，用于后续API调用
   - 注意：令牌泄露可能导致安全风险，请妥善保管

## 消息发送

金山机器人可以向金山办公套件内的用户或群组发送消息：

```javascript
// 发送文本消息
async function sendTextMessage(chatId, text) {
  const response = await fetch('https://api.kingsoft.com/robot/sendMessage', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + robotToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      chat_id: chatId,
      text: text,
      parse_mode: 'plain'
    })
  });
  return await response.json();
}

// 发送富文本消息
async function sendRichTextMessage(chatId, title, content, buttons) {
  const response = await fetch('https://api.kingsoft.com/robot/sendRichMessage', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + robotToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      chat_id: chatId,
      title: title,
      content: content,
      buttons: buttons || []
    })
  });
  return await response.json();
}
```

## 文档自动化

金山机器人可以对文档执行自动化操作：

```javascript
// 创建文档
async function createDocument(title, template) {
  const response = await fetch('https://api.kingsoft.com/robot/documents/create', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + robotToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      title: title,
      template_id: template || null
    })
  });
  return await response.json();
}

// 更新文档内容
async function updateDocument(documentId, content) {
  const response = await fetch(`https://api.kingsoft.com/robot/documents/${documentId}/update`, {
    method: 'PUT',
    headers: {
      'Authorization': 'Bearer ' + robotToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      content: content
    })
  });
  return await response.json();
}
```

## 事件监听

金山机器人可以监听特定事件并自动执行操作：

```javascript
// 配置事件监听（通过管理控制台配置）
/*
事件类型:
- document_created: 文档创建
- document_updated: 文档更新
- comment_added: 添加评论
- mention: 用户被提及
*/
```

## 最佳实践

1. **错误处理**
   - 始终检查API响应，并妥善处理可能的错误
   - 实现重试机制以应对临时网络问题
   - 记录详细日志以便于问题诊断

2. **限速处理**
   - 遵循API限速规则，避免过于频繁的请求
   - 实现请求队列机制，确保不超过API调用限制
   - 对大批量操作进行分批处理

3. **安全性**
   - 不要在客户端代码中暴露机器人令牌
   - 定期更换机器人令牌以提高安全性
   - 限制机器人权限范围，遵循最小权限原则

## 常见问题

1. **消息发送失败**
   - 检查机器人令牌是否有效
   - 确认目标用户或群组ID是否正确
   - 验证机器人是否有权限向该目标发送消息

2. **文档操作失败**
   - 检查机器人是否有足够的权限
   - 确认文档ID是否正确
   - 检查请求格式是否符合API要求

3. **事件监听无响应**
   - 检查事件配置是否正确
   - 确认回调URL是否可访问
   - 检查机器人是否有权限访问相关资源

## 资源链接

- [金山机器人API文档](https://developers.kingsoft.com/robot/api)
- [示例代码库](https://github.com/kingsoft/robot-examples)
- [常见问题解答](https://support.kingsoft.com/robot/faq) 