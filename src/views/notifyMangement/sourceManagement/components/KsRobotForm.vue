<template>
  <div class="ks-robot-form">
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="120px">
      <el-form-item label="通知渠道名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入通知渠道名称"></el-input>
      </el-form-item>
      
      <el-form-item label="WebHook" prop="robotWebhook">
        <el-input v-model="formData.robotWebhook" placeholder="请输入金山协作机器人 WebHook 地址"></el-input>
      </el-form-item>
      
      <el-form-item label="机器人关键词" prop="keywords">
        <el-select
          v-model="formData.keywords"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请选择或输入关键词">
          <el-option
            v-for="item in keywordOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
        <div class="form-tip">关键词用于触发机器人通知，至少需要一个</div>
      </el-form-item>
      
      <el-form-item label="通知格式" prop="notifyFormat">
        <el-radio-group v-model="formData.notifyFormat">
          <el-radio label="text">文本消息</el-radio>
          <el-radio label="markdown">Markdown</el-radio>
          <el-radio label="interactive">交互卡片</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="消息模板" prop="messageTemplate">
        <el-input
          v-model="formData.messageTemplate"
          type="textarea"
          :rows="4"
          placeholder="请输入消息模板">
        </el-input>
        <div class="form-tip">
          支持变量: {title}, {content}, {level}, {time}<br>
          {{ formData.notifyFormat === 'markdown' ? 'Markdown语法可用' : formData.notifyFormat === 'interactive' ? '交互卡片JSON格式' : '纯文本格式' }}
        </div>
      </el-form-item>
      
      <el-form-item label="@提醒" prop="atAll">
        <el-switch v-model="formData.atAll"></el-switch>
        <span class="status-text">{{ formData.atAll ? '@所有人' : '不@所有人' }}</span>
      </el-form-item>
      
      <el-form-item label="状态">
        <el-switch v-model="formData.status" :active-value="'enabled'" :inactive-value="'disabled'"></el-switch>
        <span class="status-text">{{ formData.status === 'enabled' ? '启用' : '禁用' }}</span>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  channelData: {
    type: Object,
    default: () => ({})
  },
  channelType: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['submit', 'cancel']);

// 表单引用
const formRef = ref(null);

// 关键词选项
const keywordOptions = ['警告', '告警', '故障', '异常', '错误', '宕机', '超时'];

// 表单数据
const formData = reactive({
  id: props.channelData.id || null,
  name: props.channelData.name || '',
  typeId: props.channelData.typeId || props.channelType?.id,
  type: props.channelData.type || props.channelType?.name,
  status: props.channelData.status || 'enabled',
  robotWebhook: props.channelData.robotWebhook || '',
  keywords: props.channelData.keywords || ['告警'],
  notifyFormat: props.channelData.notifyFormat || 'markdown',
  messageTemplate: props.channelData.messageTemplate || '# 告警通知: {title}\n\n**级别**: {level}\n\n**时间**: {time}\n\n**详情**: {content}',
  atAll: props.channelData.atAll || false
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入通知渠道名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  robotWebhook: [
    { required: true, message: '请输入机器人 WebHook 地址', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  keywords: [
    { required: true, message: '请输入关键词', trigger: 'change' },
    { type: 'array', min: 1, message: '至少输入一个关键词', trigger: 'change' }
  ],
  messageTemplate: [
    { required: true, message: '请输入消息模板', trigger: 'blur' }
  ]
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      emit('submit', { ...formData });
    } else {
      console.error('表单验证失败:', fields);
      ElMessage.error('请完善表单信息');
    }
  });
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.ks-robot-form {
  padding: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.status-text {
  margin-left: 10px;
  color: #666;
}
</style> 