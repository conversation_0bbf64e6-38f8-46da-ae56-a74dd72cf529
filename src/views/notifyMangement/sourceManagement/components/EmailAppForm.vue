<template>
  <div class="email-app-form">
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="120px">
      <el-form-item label="通知渠道名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入通知渠道名称"></el-input>
      </el-form-item>
      
      <el-form-item label="SMTP服务器" prop="smtpServer">
        <el-input v-model="formData.smtpServer" placeholder="例如: smtp.example.com"></el-input>
      </el-form-item>
      
      <el-form-item label="SMTP端口" prop="smtpPort">
        <el-input-number v-model="formData.smtpPort" :min="1" :max="65535" placeholder="例如: 25, 465, 587"></el-input-number>
      </el-form-item>
      
      <el-form-item label="安全连接" prop="secure">
        <el-radio-group v-model="formData.secure">
          <el-radio :label="true">是 (SSL/TLS)</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="发件人邮箱" prop="senderEmail">
        <el-input v-model="formData.senderEmail" placeholder="请输入发件人邮箱地址"></el-input>
      </el-form-item>
      
      <el-form-item label="发件人名称" prop="senderName">
        <el-input v-model="formData.senderName" placeholder="请输入发件人名称"></el-input>
      </el-form-item>
      
      <el-form-item label="认证方式" prop="authType">
        <el-radio-group v-model="formData.authType">
          <el-radio label="password">用户名密码</el-radio>
          <el-radio label="oauth2">OAuth2</el-radio>
          <el-radio label="none">无认证</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <template v-if="formData.authType === 'password'">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input v-model="formData.password" type="password" placeholder="请输入密码" show-password></el-input>
        </el-form-item>
      </template>
      
      <template v-if="formData.authType === 'oauth2'">
        <el-form-item label="客户端ID" prop="clientId">
          <el-input v-model="formData.clientId" placeholder="请输入OAuth2客户端ID"></el-input>
        </el-form-item>
        
        <el-form-item label="客户端密钥" prop="clientSecret">
          <el-input v-model="formData.clientSecret" type="password" placeholder="请输入OAuth2客户端密钥" show-password></el-input>
        </el-form-item>
        
        <el-form-item label="刷新令牌" prop="refreshToken">
          <el-input v-model="formData.refreshToken" type="password" placeholder="请输入OAuth2刷新令牌" show-password></el-input>
        </el-form-item>
      </template>
      
      <el-form-item label="收件人" prop="recipients">
        <el-select
          v-model="formData.recipients"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入收件人邮箱地址，回车确认">
        </el-select>
      </el-form-item>
      
      <el-form-item label="抄送(CC)" prop="cc">
        <el-select
          v-model="formData.cc"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入抄送邮箱地址，回车确认">
        </el-select>
      </el-form-item>
      
      <el-form-item label="邮件主题" prop="subject">
        <el-input v-model="formData.subject" placeholder="请输入邮件主题模板"></el-input>
        <div class="form-tip">支持变量: {title}, {level}, {time}</div>
      </el-form-item>
      
      <el-form-item label="邮件格式" prop="format">
        <el-radio-group v-model="formData.format">
          <el-radio label="html">HTML</el-radio>
          <el-radio label="text">纯文本</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="邮件内容" prop="template">
        <el-input
          v-model="formData.template"
          type="textarea"
          :rows="5"
          placeholder="请输入邮件内容模板">
        </el-input>
        <div class="form-tip">
          支持变量: {title}, {content}, {level}, {time} 
          {{ formData.format === 'html' ? 'HTML格式支持富文本标签' : '纯文本格式' }}
        </div>
      </el-form-item>
      
      <el-form-item label="状态">
        <el-switch v-model="formData.status" :active-value="'enabled'" :inactive-value="'disabled'"></el-switch>
        <span class="status-text">{{ formData.status === 'enabled' ? '启用' : '禁用' }}</span>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  channelData: {
    type: Object,
    default: () => ({})
  },
  channelType: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['submit', 'cancel']);

// 表单引用
const formRef = ref(null);

// 默认模板
const defaultTemplates = {
  html: `<div style="font-family: Arial, sans-serif; padding: 20px; color: #333;">
  <h2 style="color: #E74C3C;">{title}</h2>
  <p><strong>级别:</strong> {level}</p>
  <p><strong>时间:</strong> {time}</p>
  <div style="margin-top: 20px; padding: 15px; background-color: #F8F9FA; border-left: 4px solid #3498DB;">
    {content}
  </div>
</div>`,
  text: `告警标题: {title}\n告警级别: {level}\n告警时间: {time}\n\n告警详情:\n{content}`
};

// 表单数据
const formData = reactive({
  id: props.channelData.id || null,
  name: props.channelData.name || '',
  typeId: props.channelData.typeId || props.channelType?.id,
  type: props.channelData.type || props.channelType?.name,
  status: props.channelData.status || 'enabled',
  smtpServer: props.channelData.smtpServer || '',
  smtpPort: props.channelData.smtpPort || 465,
  secure: props.channelData.secure !== undefined ? props.channelData.secure : true,
  senderEmail: props.channelData.senderEmail || '',
  senderName: props.channelData.senderName || '告警通知系统',
  authType: props.channelData.authType || 'password',
  username: props.channelData.username || '',
  password: props.channelData.password || '',
  clientId: props.channelData.clientId || '',
  clientSecret: props.channelData.clientSecret || '',
  refreshToken: props.channelData.refreshToken || '',
  recipients: props.channelData.recipients || [],
  cc: props.channelData.cc || [],
  subject: props.channelData.subject || '[告警] {title} - {level}级别',
  format: props.channelData.format || 'html',
  template: props.channelData.template || defaultTemplates.html
});

// 监听邮件格式变化，自动切换模板
watch(() => formData.format, (newFormat) => {
  if (!formData.template || formData.template === defaultTemplates[newFormat === 'html' ? 'text' : 'html']) {
    formData.template = defaultTemplates[newFormat];
  }
});

// 定义表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入通知渠道名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  smtpServer: [
    { required: true, message: '请输入SMTP服务器地址', trigger: 'blur' }
  ],
  smtpPort: [
    { required: true, message: '请输入SMTP端口', trigger: 'blur' },
    { type: 'number', message: '端口必须为数字', trigger: 'blur' }
  ],
  senderEmail: [
    { required: true, message: '请输入发件人邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  senderName: [
    { required: true, message: '请输入发件人名称', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  clientId: [
    { required: true, message: '请输入客户端ID', trigger: 'blur' }
  ],
  clientSecret: [
    { required: true, message: '请输入客户端密钥', trigger: 'blur' }
  ],
  refreshToken: [
    { required: true, message: '请输入刷新令牌', trigger: 'blur' }
  ],
  recipients: [
    { required: true, message: '请至少添加一个收件人', trigger: 'change' },
    { type: 'array', min: 1, message: '至少需要一个收件人', trigger: 'change' }
  ],
  subject: [
    { required: true, message: '请输入邮件主题', trigger: 'blur' }
  ],
  template: [
    { required: true, message: '请输入邮件内容模板', trigger: 'blur' }
  ]
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  // 根据认证方式动态验证字段
  if (formData.authType === 'password') {
    await formRef.value.validateField(['username', 'password']);
  } else if (formData.authType === 'oauth2') {
    await formRef.value.validateField(['clientId', 'clientSecret', 'refreshToken']);
  }
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      emit('submit', { ...formData });
    } else {
      console.error('表单验证失败:', fields);
      ElMessage.error('请完善表单信息');
    }
  });
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.email-app-form {
  padding: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.status-text {
  margin-left: 10px;
  color: #666;
}
</style> 