<template>
  <div class="ks-app-form">
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="120px">
      <el-form-item label="通知渠道名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入通知渠道名称"></el-input>
      </el-form-item>
      
      <el-form-item label="应用 ID" prop="appId">
        <el-input v-model="formData.appId" placeholder="请输入金山协作应用 ID"></el-input>
      </el-form-item>
      
      <el-form-item label="应用密钥" prop="appSecret">
        <el-input v-model="formData.appSecret" placeholder="请输入金山协作应用密钥" show-password></el-input>
      </el-form-item>
      
      <el-form-item label="回调地址" prop="callbackUrl">
        <el-input v-model="formData.callbackUrl" placeholder="请输入回调地址"></el-input>
      </el-form-item>
      
      <el-form-item label="目标用户组" prop="targetUsers">
        <el-select
          v-model="formData.targetUsers"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请选择或输入目标用户组">
          <el-option
            v-for="item in userGroups"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="消息模板" prop="messageTemplate">
        <el-input
          v-model="formData.messageTemplate"
          type="textarea"
          :rows="4"
          placeholder="请输入消息模板">
        </el-input>
        <div class="form-tip">支持变量: {title}, {content}, {level}, {time}</div>
      </el-form-item>
      
      <el-form-item label="状态">
        <el-switch v-model="formData.status" :active-value="'enabled'" :inactive-value="'disabled'"></el-switch>
        <span class="status-text">{{ formData.status === 'enabled' ? '启用' : '禁用' }}</span>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  channelData: {
    type: Object,
    default: () => ({})
  },
  channelType: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['submit', 'cancel']);

// 表单引用
const formRef = ref(null);

// 模拟用户组数据
const userGroups = reactive([
  { label: '开发团队', value: 'dev_team' },
  { label: '测试团队', value: 'test_team' },
  { label: '运维团队', value: 'ops_team' },
  { label: '产品团队', value: 'product_team' },
  { label: '管理层', value: 'management' }
]);

// 表单数据
const formData = reactive({
  id: props.channelData.id || null,
  name: props.channelData.name || '',
  typeId: props.channelData.typeId || props.channelType?.id,
  type: props.channelData.type || props.channelType?.name,
  status: props.channelData.status || 'enabled',
  appId: props.channelData.appId || '',
  appSecret: props.channelData.appSecret || '',
  callbackUrl: props.channelData.callbackUrl || '',
  targetUsers: props.channelData.targetUsers || [],
  messageTemplate: props.channelData.messageTemplate || '通知: {title}\n内容: {content}\n级别: {level}\n时间: {time}'
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入通知渠道名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  appId: [
    { required: true, message: '请输入应用 ID', trigger: 'blur' }
  ],
  appSecret: [
    { required: true, message: '请输入应用密钥', trigger: 'blur' }
  ],
  callbackUrl: [
    { required: true, message: '请输入回调地址', trigger: 'blur' }
  ],
  targetUsers: [
    { required: true, message: '请选择目标用户组', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个用户组', trigger: 'change' }
  ],
  messageTemplate: [
    { required: true, message: '请输入消息模板', trigger: 'blur' }
  ]
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      emit('submit', { ...formData });
    } else {
      console.error('表单验证失败:', fields);
      ElMessage.error('请完善表单信息');
    }
  });
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.ks-app-form {
  padding: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.status-text {
  margin-left: 10px;
  color: #666;
}
</style> 