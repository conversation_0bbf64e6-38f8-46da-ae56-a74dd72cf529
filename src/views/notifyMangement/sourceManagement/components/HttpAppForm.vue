<template>
  <div class="http-app-form">
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="120px">
      <el-form-item label="通知渠道名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入通知渠道名称"></el-input>
      </el-form-item>
      
      <el-form-item label="HTTP URL" prop="httpUrl">
        <el-input v-model="formData.httpUrl" placeholder="请输入HTTP回调URL"></el-input>
      </el-form-item>
      
      <el-form-item label="请求方法" prop="httpMethod">
        <el-select v-model="formData.httpMethod" placeholder="请选择请求方法">
          <el-option label="POST" value="POST"></el-option>
          <el-option label="GET" value="GET"></el-option>
          <el-option label="PUT" value="PUT"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="内容类型" prop="contentType">
        <el-select v-model="formData.contentType" placeholder="请选择内容类型">
          <el-option label="application/json" value="application/json"></el-option>
          <el-option label="application/x-www-form-urlencoded" value="application/x-www-form-urlencoded"></el-option>
          <el-option label="text/plain" value="text/plain"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="自定义请求头" prop="headers">
        <el-card class="header-card">
          <div v-for="(header, index) in formData.headers" :key="index" class="header-item">
            <el-input v-model="header.key" placeholder="Header名称" class="header-key"></el-input>
            <el-input v-model="header.value" placeholder="Header值" class="header-value"></el-input>
            <el-button type="danger" circle size="small" @click="removeHeader(index)">
              <i class="el-icon-delete"></i>
            </el-button>
          </div>
          <el-button type="primary" plain size="small" @click="addHeader">添加请求头</el-button>
        </el-card>
      </el-form-item>
      
      <el-form-item label="请求体模板" prop="bodyTemplate" v-if="formData.httpMethod !== 'GET'">
        <el-input
          v-model="formData.bodyTemplate"
          type="textarea"
          :rows="4"
          placeholder="请输入请求体模板，JSON格式">
        </el-input>
        <div class="form-tip">
          支持变量: {title}, {content}, {level}, {time}，请按照选择的内容类型正确格式化
        </div>
      </el-form-item>
      
      <el-form-item label="验证响应" prop="validateResponse">
        <el-switch v-model="formData.validateResponse"></el-switch>
        <span class="status-text">{{ formData.validateResponse ? '开启响应验证' : '关闭响应验证' }}</span>
      </el-form-item>
      
      <el-form-item label="成功响应值" prop="successResponse" v-if="formData.validateResponse">
        <el-input v-model="formData.successResponse" placeholder="例如: {&quot;success&quot;:true}"></el-input>
        <div class="form-tip">期望的成功响应值，用于验证回调是否成功</div>
      </el-form-item>
      
      <el-form-item label="状态">
        <el-switch v-model="formData.status" :active-value="'enabled'" :inactive-value="'disabled'"></el-switch>
        <span class="status-text">{{ formData.status === 'enabled' ? '启用' : '禁用' }}</span>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  channelData: {
    type: Object,
    default: () => ({})
  },
  channelType: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['submit', 'cancel']);

// 表单引用
const formRef = ref(null);

// 默认请求体模板
const defaultBodyTemplate = {
  'application/json': '{\n  "title": "{title}",\n  "content": "{content}",\n  "level": "{level}",\n  "time": "{time}"\n}',
  'application/x-www-form-urlencoded': 'title={title}&content={content}&level={level}&time={time}',
  'text/plain': '告警标题: {title}\n告警内容: {content}\n告警级别: {level}\n告警时间: {time}'
};

// 表单数据
const formData = reactive({
  id: props.channelData.id || null,
  name: props.channelData.name || '',
  typeId: props.channelData.typeId || props.channelType?.id,
  type: props.channelData.type || props.channelType?.name,
  status: props.channelData.status || 'enabled',
  httpUrl: props.channelData.httpUrl || '',
  httpMethod: props.channelData.httpMethod || 'POST',
  contentType: props.channelData.contentType || 'application/json',
  headers: props.channelData.headers || [{ key: 'Content-Type', value: 'application/json' }],
  bodyTemplate: props.channelData.bodyTemplate || defaultBodyTemplate['application/json'],
  validateResponse: props.channelData.validateResponse || false,
  successResponse: props.channelData.successResponse || '{"success":true}'
});

// 添加请求头
const addHeader = () => {
  formData.headers.push({ key: '', value: '' });
};

// 移除请求头
const removeHeader = (index) => {
  formData.headers.splice(index, 1);
};

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入通知渠道名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  httpUrl: [
    { required: true, message: '请输入HTTP回调URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  httpMethod: [
    { required: true, message: '请选择请求方法', trigger: 'change' }
  ],
  contentType: [
    { required: true, message: '请选择内容类型', trigger: 'change' }
  ],
  bodyTemplate: [
    { required: true, message: '请输入请求体模板', trigger: 'blur' }
  ],
  successResponse: [
    { required: true, message: '请输入成功响应值', trigger: 'blur' }
  ]
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      emit('submit', { ...formData });
    } else {
      console.error('表单验证失败:', fields);
      ElMessage.error('请完善表单信息');
    }
  });
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.http-app-form {
  padding: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.status-text {
  margin-left: 10px;
  color: #666;
}

.header-card {
  margin-bottom: 10px;
}

.header-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.header-key {
  flex: 1;
}

.header-value {
  flex: 2;
}
</style> 