<template>
  <div class="notify-channel-container">
    <!-- 左侧通知渠道类型导航 -->
    <div class="left-panel">
      <div class="left-header">
        <h3 class="panel-title">通知渠道类型</h3>
      </div>
      <div class="channel-types" v-loading="typeLoading">
        <div 
          v-for="item in channelTypes" 
          :key="item.id" 
          class="channel-type-item" 
          :class="{ active: activeType === item.id }"
          @click="selectChannelType(item)">
          <div class="type-item-content">
            <div class="icon-container">
              <!-- 使用不同类型的图标 -->
              <img v-if="item.name === '金山协作应用'" src="./components/logo/wps.png" class="logo-icon" />
              <img v-else-if="item.name === '金山协作机器人'" src="./components/logo/robot.svg" class="logo-icon" />
              <el-icon v-else-if="item.name === 'HTTP应用'" class="custom-el-icon" :style="{ color: item.bgColor }"><Connection /></el-icon>
              <el-icon v-else-if="item.name === '邮件应用'" class="custom-el-icon" :style="{ color: item.bgColor }"><Message /></el-icon>
            </div>
            <div class="channel-type-name">{{ item.name }}</div>
          </div>
          <div class="channel-type-actions">
            <el-button 
              type="success" 
              size="small" 
              circle 
              :icon="Plus"
              @click.stop="addNewChannel(item)"
              title="添加该类型通知渠道">
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="right-panel">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input 
          v-model="searchTypeModel" 
          placeholder="搜索渠道类型" 
          class="search-input">
        </el-input>
        <el-input 
          v-model="searchNameModel" 
          placeholder="搜索渠道名称" 
          class="search-input">
        </el-input>
        <el-select v-model="statusFilter" placeholder="状态" class="filter-select">
          <el-option label="全部" value=""></el-option>
          <el-option label="启用" value="enabled"></el-option>
          <el-option label="禁用" value="disabled"></el-option>
        </el-select>
        <el-select v-model="typeFilter" placeholder="类型" class="filter-select">
          <el-option label="全部" value=""></el-option>
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>

      <!-- 内容主体区域 -->
      <div class="content-area">
        <!-- 通知渠道列表 -->
        <el-table 
          :data="filteredChannels" 
          style="width: 100%" 
          class="channel-table"
          v-loading="tableLoading"
          element-loading-text="加载通知渠道数据中">
          <el-table-column prop="name" label="通知渠道" min-width="180">
            <template #default="scope">
              <div class="channel-cell">
                <div class="channel-icon-small">
                  <!-- 表格中的图标 -->
                  <img v-if="scope.row.type === '金山协作应用'" src="./components/logo/wps.png" class="table-logo-icon" />
                  <img v-else-if="scope.row.type === '金山协作机器人'" src="./components/logo/robot.svg" class="table-logo-icon" />
                  <el-icon v-else-if="scope.row.type === 'HTTP应用'" class="custom-table-el-icon" :style="{ color: getTypeBgColor(scope.row.type) }"><Connection /></el-icon>
                  <el-icon v-else-if="scope.row.type === '邮件应用'" class="custom-table-el-icon" :style="{ color: getTypeBgColor(scope.row.type) }"><Message /></el-icon>
                </div>
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" min-width="180"></el-table-column>
          <el-table-column prop="lastNotifyTime" label="最新发送时间" min-width="180"></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                @change="toggleStatus(scope.row)"
                :active-value="'enabled'"
                :inactive-value="'disabled'">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button type="primary" :icon="Edit" circle @click="editChannel(scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <div class="total-info">总计 {{ totalItems }} 项</div>
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-size="pageSize"
            layout="prev, pager, next"
            :total="totalItems">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 表单对话框 -->
    <el-dialog 
      v-model="formDialogVisible" 
      :title="formTitle" 
      width="600px"
      destroy-on-close>
      <component 
        :is="currentFormComponent" 
        :channel-data="currentChannel"
        :channel-type="selectedChannelType"
        @submit="handleFormSubmit"
        @cancel="formDialogVisible = false">
      </component>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, markRaw, defineAsyncComponent } from 'vue';
import { Edit, Plus, Message, Connection } from '@element-plus/icons-vue';
import { getChannelTypes, getChannels, toggleChannelStatus, createChannel, updateChannel } from '@/api/mock/notifyChannel';
import { ElMessage } from 'element-plus';

// 异步加载表单组件
const KsAppForm = defineAsyncComponent(() => import('./components/KsAppForm.vue'));
const KsRobotForm = defineAsyncComponent(() => import('./components/KsRobotForm.vue'));
const HttpAppForm = defineAsyncComponent(() => import('./components/HttpAppForm.vue'));
const EmailAppForm = defineAsyncComponent(() => import('./components/EmailAppForm.vue'));

// 组件名称定义
defineOptions({
  name: 'NotifyChannelManagement'
});

// 活动类型
const activeType = ref(null);

// 搜索和筛选
const searchTypeModel = ref('');
const searchNameModel = ref('');
const statusFilter = ref('');
const typeFilter = ref('');

// 通知渠道类型选项
const typeOptions = ref([]);

// 通知渠道类型数据
const channelTypes = ref([]);

// 通知渠道数据
const channels = ref([]);

// 加载状态
const typeLoading = ref(false);
const tableLoading = ref(false);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// 表单对话框
const formDialogVisible = ref(false);
const formTitle = ref('');
const currentFormComponent = ref(null);
const currentChannel = ref(null);
const selectedChannelType = ref(null);

// 过滤通知渠道
const filteredChannels = computed(() => {
  return channels.value.filter(item => {
    const matchType = !searchTypeModel.value || item.type.toLowerCase().includes(searchTypeModel.value.toLowerCase());
    const matchName = !searchNameModel.value || item.name.toLowerCase().includes(searchNameModel.value.toLowerCase());
    const matchStatus = !statusFilter.value || item.status === statusFilter.value;
    const matchTypeFilter = !typeFilter.value || item.typeId.toString() === typeFilter.value;
    const matchActiveType = !activeType.value || item.typeId === activeType.value;
    return matchType && matchName && matchStatus && matchTypeFilter && matchActiveType;
  });
});

// 获取类型背景色
const getTypeBgColor = (type) => {
  const typeBgColorMap = {
    '金山协作应用': '#4285f4',
    '金山协作机器人': '#34a853',
    'HTTP应用': '#ea4335',
    '邮件应用': '#fbbc05'
  };
  return typeBgColorMap[type] || '#909399';
};

// 选择通知渠道类型
const selectChannelType = (item) => {
  activeType.value = activeType.value === item.id ? null : item.id;
  
  // 重新加载符合当前筛选条件的数据
  loadChannels();
};

// 切换状态
const toggleStatus = async (row) => {
  try {
    const { success } = await toggleChannelStatus(row.id, row.status);
    if (success) {
      ElMessage.success(`已${row.status === 'enabled' ? '启用' : '禁用'}通知渠道：${row.name}`);
    } else {
      ElMessage.error('操作失败，请重试');
    }
  } catch (error) {
    console.error('切换通知渠道状态失败:', error);
    ElMessage.error('切换状态失败');
  }
};

// 获取表单组件
const getFormComponent = (type) => {
  const formMap = {
    '金山协作应用': KsAppForm,
    '金山协作机器人': KsRobotForm,
    'HTTP应用': HttpAppForm,
    '邮件应用': EmailAppForm
  };
  return formMap[type] || null;
};

// 添加新通知渠道
const addNewChannel = (typeItem) => {
  if (!typeItem) return;
  
  selectedChannelType.value = typeItem;
  formTitle.value = `添加${typeItem.name}通知渠道`;
  currentChannel.value = { typeId: typeItem.id, type: typeItem.name, status: 'enabled' };
  currentFormComponent.value = markRaw(getFormComponent(typeItem.name));
  formDialogVisible.value = true;
};

// 编辑通知渠道
const editChannel = (row) => {
  const typeItem = channelTypes.value.find(t => t.id === row.typeId);
  if (!typeItem) return;
  
  selectedChannelType.value = typeItem;
  formTitle.value = `编辑${row.type}通知渠道`;
  currentChannel.value = { ...row };
  currentFormComponent.value = markRaw(getFormComponent(row.type));
  formDialogVisible.value = true;
};

// 处理表单提交
const handleFormSubmit = async (formData) => {
  try {
    tableLoading.value = true;
    
    const isNew = !formData.id;
    let result;
    
    if (isNew) {
      result = await createChannel(formData);
    } else {
      result = await updateChannel(formData.id, formData);
    }
    
    if (result.success) {
      ElMessage.success(`${isNew ? '添加' : '更新'}通知渠道成功`);
      formDialogVisible.value = false;
      loadChannels();
    } else {
      ElMessage.error(result.message || `${isNew ? '添加' : '更新'}通知渠道失败`);
    }
  } catch (error) {
    console.error('保存通知渠道失败:', error);
    ElMessage.error('保存通知渠道失败');
  } finally {
    tableLoading.value = false;
  }
};

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  loadChannels();
};

// 加载通知渠道类型
const loadChannelTypes = async () => {
  typeLoading.value = true;
  try {
    const data = await getChannelTypes();
    channelTypes.value = data;
    
    // 从通知渠道类型中提取类型选项
    typeOptions.value = data.map(item => ({
      value: item.id.toString(),
      label: item.name
    }));
  } catch (error) {
    console.error('加载通知渠道类型失败:', error);
    ElMessage.error('加载通知渠道类型失败');
  } finally {
    typeLoading.value = false;
  }
};

// 加载通知渠道数据
const loadChannels = async () => {
  tableLoading.value = true;
  try {
    const { data, total } = await getChannels({
      page: currentPage.value,
      pageSize: pageSize.value,
      typeId: activeType.value,
      status: statusFilter.value,
      typeKeyword: searchTypeModel.value,
      nameKeyword: searchNameModel.value
    });
    
    channels.value = data;
    totalItems.value = total;
  } catch (error) {
    console.error('加载通知渠道数据失败:', error);
    ElMessage.error('加载通知渠道数据失败');
  } finally {
    tableLoading.value = false;
  }
};

// 加载数据
onMounted(async () => {
  await loadChannelTypes();
  await loadChannels();
});

// 监听过滤器变化，自动刷新数据
watch([searchNameModel, searchTypeModel, statusFilter, typeFilter], () => {
  loadChannels();
}, { debounce: 300 });
</script>

<style lang="scss" scoped>
.notify-channel-container {
  display: flex;
  height: calc(100vh - 60px);
  background-color: #fff;
  color: #333;
  overflow: hidden;
}

.left-panel {
  width: 480px;
  max-width: 22%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  flex-shrink: 0;
  padding: 0;
  background-color: #fff;
  color: #333;
  border-right: 1px solid #eaeaea;
}

.left-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #f9f9f9;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #333;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow-y: auto;
  min-width: 0; /* 防止子元素溢出 */
}

.search-area {
  display: flex;
  gap: 10px;
  margin: 10px;
  flex-wrap: wrap;
}

.search-input {
  width: 200px;
}

.filter-select {
  width: 120px;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 10px 10px;
  min-height: 0; /* 避免内容溢出 */
}

.channel-types {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 10px;
  overflow-y: auto;
  justify-content: flex-start;
  align-content: flex-start;
}

.channel-type-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s;
  background-color: #f5f7fa;
  height: 105px;
  position: relative;
  border: 1px solid #eaeaea;
  
  &:hover, &.active {
    background-color: #ecf5ff;
    border-color: #d9ecff;
  }
  
  &.active {
    border-left: 3px solid #409eff;
  }
}

.type-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.icon-container {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  flex-shrink: 0;
  border-radius: 50%;
  overflow: hidden;
  background-color: transparent;
}

.logo-icon {
  width: 100%;
  height: 100%;
}

.custom-el-icon {
  font-size: 32px;
}

.channel-type-name {
  font-size: 14px;
  text-align: center;
  color: #333;
  font-weight: 500;
}

.channel-type-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.channel-type-item:hover .channel-type-actions {
  opacity: 1;
}

.channel-table {
  margin-bottom: 10px;
  flex: 1;
}

.channel-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.channel-icon-small {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  background-color: transparent;
}

.table-logo-icon {
  width: 100%;
  height: 100%;
}

.custom-table-el-icon {
  font-size: 16px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.total-info {
  color: #606266;
}

/* 修改滚动条样式 */
:deep(::-webkit-scrollbar) {
  width: 4px;
  height: 4px;
}

:deep(::-webkit-scrollbar-thumb) {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

:deep(::-webkit-scrollbar-track) {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}
</style>
