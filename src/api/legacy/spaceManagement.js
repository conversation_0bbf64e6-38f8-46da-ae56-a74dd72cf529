import request from '@/utils/request'
import { apiCall } from '@/utils/apiManager'
import {
  getSpace<PERSON>ist as mockGetSpaceList,
  createSpace as mockCreateSpace,
  updateSpace as mockUpdateSpace,
  deleteSpace as mockDeleteSpace,
  getTeamList as mockGetTeamList
} from '../mock/spaceManagement'

// 真实API调用
function realGetSpaceList(params) {
  return request({
    url: '/event-ui/api/spaces',
    method: 'get',
    params
  })
}

// 获取工作空间列表 - 支持Mock切换
export function getSpaceList(params) {
  return apiCall(realGetSpaceList, mockGetSpaceList, params)
}

// 真实API调用函数
function realCreateSpace(data) {
  return request({
    url: '/event-ui/api/spaces',
    method: 'post',
    data
  })
}

function realUpdateSpace(id, data) {
  return request({
    url: `/event-ui/api/spaces/${id}`,
    method: 'put',
    data
  })
}

function realDeleteSpace(id) {
  return request({
    url: `/event-ui/api/spaces/${id}`,
    method: 'delete'
  })
}

function realGetTeamList() {
  return request({
    url: '/event-ui/api/teams',
    method: 'get'
  })
}

// 导出API函数 - 支持Mock切换
export function createSpace(data) {
  return apiCall(realCreateSpace, mockCreateSpace, data)
}

export function updateSpace(id, data) {
  return apiCall(realUpdateSpace, mockUpdateSpace, id, data)
}

export function deleteSpace(id) {
  return apiCall(realDeleteSpace, mockDeleteSpace, id)
}

export function getTeamList() {
  return apiCall(realGetTeamList, mockGetTeamList)
}