# ZeroDuty API 目录结构说明

## 📁 目录结构

```
src/api/
├── services/           # 按服务分类的真实API
│   ├── oncall/        # OnCall服务 (端口2002)
│   │   ├── index.js   # 值班管理主要API
│   │   ├── integration.js  # 集成管理API
│   │   └── alertRoute.js   # 告警路由API
│   ├── system/        # System服务 (端口2001)
│   │   └── index.js   # 系统管理API
│   └── message/       # Message服务 (端口2003)
│       └── index.js   # 消息通知API
├── adapters/          # 适配器文件
│   ├── integration.js # 集成管理适配器
│   └── alertRoute.js  # 告警路由适配器
├── legacy/            # 遗留API文件
│   ├── techApi.js     # 权限系统API
│   ├── spaceManagement.js
│   ├── templateManagement.js
│   └── noiseReduction.js
├── mock/              # Mock数据 (保持不变)
│   ├── index.js       # Mock数据统一导出
│   ├── integration.js
│   ├── spaceManagement.js
│   └── ...
├── index.js           # 统一导出文件
└── README.md          # 本文档
```

## 🎯 设计原则

### 1. 按服务分类
- **OnCall服务**: 值班管理、集成管理、告警路由
- **System服务**: 用户管理、权限控制、系统配置
- **Message服务**: 消息发送、通知渠道、模板管理

### 2. 清晰的职责分离
- **services/**: 真实API接口定义
- **adapters/**: 数据适配和业务逻辑封装
- **legacy/**: 遗留代码，逐步迁移
- **mock/**: Mock数据，开发和测试使用

### 3. 统一的导出方式
- 每个服务目录都有 `index.js` 作为主要导出文件
- 根目录的 `index.js` 提供统一的API访问入口

## 🚀 使用方式

### 方式一：按服务导入
```javascript
import { oncallApi, systemApi, messageApi } from '@/api'

// 使用OnCall服务API
const schedules = await oncallApi.getOncallSchedules({ spaceId: '123' })

// 使用System服务API
const users = await systemApi.getUserList({ page: 1 })

// 使用Message服务API
const result = await messageApi.sendMessage({ type: 'email', content: '...' })
```

### 方式二：直接导入具体函数
```javascript
import { 
  getOncallSchedules, 
  getIntegrationList,
  getUserList,
  sendMessage 
} from '@/api'

const schedules = await getOncallSchedules({ spaceId: '123' })
const integrations = await getIntegrationList({ page: 1 })
```

### 方式三：使用适配器
```javascript
import { integrationAdapter, alertRouteAdapter } from '@/api'

// 使用集成管理适配器 (混合Mock和真实API)
const integrations = await integrationAdapter.getIntegrations({ page: 1 })

// 使用告警路由适配器
const routes = await alertRouteAdapter.getRoutingRules('integration-123')
```

### 方式四：从具体服务导入
```javascript
import { getOncallSchedules } from '@/api/services/oncall'
import { getUserList } from '@/api/services/system'
import { sendMessage } from '@/api/services/message'
```

## 🔧 开发指南

### 添加新的API接口

1. **确定服务类型**: 新API属于哪个服务 (oncall/system/message)
2. **添加到对应服务文件**: 在 `services/{service}/index.js` 中添加
3. **更新统一导出**: 在 `src/api/index.js` 中添加便捷导出 (可选)
4. **添加适配器**: 如果需要数据适配，在 `adapters/` 中创建

### 迁移遗留API

1. **分析API功能**: 确定API属于哪个服务
2. **移动到对应服务**: 从 `legacy/` 移动到 `services/{service}/`
3. **更新导入路径**: 修改所有使用该API的文件
4. **测试功能**: 确保迁移后功能正常

## 📊 服务端口映射

| 服务 | 端口 | 功能 | API前缀 |
|------|------|------|---------|
| OnCall | 2002 | 值班管理、集成管理、告警路由 | `/oncall-api` |
| System | 2001 | 用户管理、权限控制、系统配置 | `/system-api` |
| Message | 2003 | 消息发送、通知渠道、模板管理 | `/message-api` |

## 🛠️ 工具函数

### 服务健康检查
```javascript
import { checkAllServicesHealth } from '@/api'

const healthStatus = await checkAllServicesHealth()
console.log('服务健康状态:', healthStatus)
// 输出: { oncall: true, system: true, message: false }
```

### API使用指南
```javascript
import { printApiGuide } from '@/api'

printApiGuide() // 在控制台打印API使用指南
```

## 🔄 迁移计划

### 已完成
- ✅ 创建新的目录结构
- ✅ 移动OnCall服务相关API
- ✅ 移动System和Message服务API
- ✅ 创建适配器文件
- ✅ 更新导入路径

### 待完成
- ⏳ 逐步迁移遗留API到对应服务
- ⏳ 完善各服务的API文档
- ⏳ 添加更多适配器和工具函数
- ⏳ 优化错误处理和重试机制

