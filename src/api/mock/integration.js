/**
 * 集成管理模块的mock数据接口
 */
import { generatePushUrl } from '@/utils/webhook';

// 模拟集成类型数据
const mockIntegrationTypes = [
  {
    id: 1,
    name: '阿里云监控 CM 事件',
    icon: 'tcicon-aliyun',
    bgColor: '#ff6a00',
    description: '接收阿里云云监控事件'
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    icon: 'tcicon-grafana',
    bgColor: '#f7941d',
    description: '接收Grafana告警事件'
  },
  {
    id: 3,
    name: 'Zabbix',
    icon: 'tcicon-zabbix',
    bgColor: '#d40000',
    description: '接收Zabbix告警事件'
  },
  {
    id: 4,
    name: 'Prometheus',
    icon: 'tcicon-prometheus',
    bgColor: '#e74c3c',
    description: '接收Prometheus告警事件'
  },
  {
    id: 5,
    name: '邮件 Email',
    icon: 'tcicon-email',
    bgColor: '#3498db',
    description: '通过邮件接收告警事件'
  },
  {
    id: 7,
    name: '标准告警事件',
    icon: 'tcicon-alert-circle',
    bgColor: '#4a69bd',
    description: '接收标准格式的告警事件'
  }
];

// 模拟标签增强规则数据
const mockTagRules = {
  // 按集成ID组织数据
  1: [
    {
      id: 1,
      kind: 'extraction',
      if: [
        {
          key: 'severity',
          oper: 'IN',
          vals: ['Critical']
        },
        {
          key: 'title',
          oper: 'IN',
          vals: ['*']
        }
      ],
      settings: {
        source_field: 'title',
        pattern: '/(Web)/',
        override: true,
        result_label: 'webTitle'
      }
    },
    {
      id: 2,
      kind: 'composition',
      if: [
        {
          key: 'severity',
          oper: 'IN',
          vals: ['*']
        }
      ],
      settings: {
        template: 'https://example.com/runbook/{{.Labels.service}}/{{.Labels.check}}',
        override: true,
        result_label: 'runbook_url'
      }
    },
    {
      id: 3,
      kind: 'drop',
      if: [
        {
          key: 'severity',
          oper: 'IN',
          vals: ['Info']
        }
      ],
      settings: {
        drop_labels: ['cpu']
      }
    }
  ],
  2: [
    {
      id: 4,
      kind: 'extraction',
      if: [
        {
          key: 'title',
          oper: 'CONTAINS',
          vals: ['error']
        }
      ],
      settings: {
        source_field: 'description',
        pattern: '/Error code: (\\w+)/',
        override: true,
        result_label: 'errorCode'
      }
    }
  ],
  3: [],
  4: [
    {
      id: 5,
      kind: 'extraction',
      if: [
        {
          key: 'title',
          oper: 'IN',
          vals: ['*']
        }
      ],
      settings: {
        source_field: 'title',
        pattern: '/(\\w+)服务/',
        override: true,
        result_label: 'serviceName'
      }
    }
  ],
  5: []
};

// 模拟降噪规则数据
const mockNoiseRules = {
  // 按集成ID组织数据
  1: [
    {
      id: 1,
      name: '忽略测试告警',
      enabled: true,
      conditions: [
        {
          field: 'title',
          operator: 'CONTAINS',
          value: 'test'
        },
        {
          field: 'severity',
          operator: 'IN',
          value: ['Info']
        }
      ],
      action: 'drop'
    },
    {
      id: 2,
      name: '降级CPU告警',
      enabled: true,
      conditions: [
        {
          field: 'title',
          operator: 'CONTAINS',
          value: 'CPU'
        },
        {
          field: 'cpuUsage',
          operator: 'LT',
          value: '90'
        }
      ],
      action: 'downgrade',
      actionSettings: {
        targetSeverity: 'Info'
      }
    }
  ],
  2: [
    {
      id: 3,
      name: '合并相似告警',
      enabled: true,
      conditions: [
        {
          field: 'instance',
          operator: 'REGEX',
          value: '.*'
        }
      ],
      action: 'group',
      actionSettings: {
        groupBy: ['instance', 'alertname'],
        windowSeconds: 300
      }
    }
  ],
  3: [],
  4: [],
  5: []
};

// 模拟告警处理规则数据
const mockAlertProcessingRules = {
  // 按集成ID组织数据
  1: [
    {
      id: 1,
      kind: 'title_reset',
      if: [
        {
          key: 'severity',
          oper: 'IN',
          vals: ['Info']
        },
        {
          key: 'description',
          oper: 'IN',
          vals: ['测试']
        }
      ],
      settings: {
        title: '测试告警'
      }
    },
    {
      id: 2,
      kind: 'alert_drop',
      if: [
        {
          key: 'title',
          oper: 'IN',
          vals: ['test']
        }
      ],
      settings: null
    },
    {
      id: 3,
      kind: 'description_reset',
      if: null,
      settings: {
        description: '测试更新描述'
      }
    }
  ],
  2: [
    {
      id: 4,
      kind: 'severity_reset',
      if: [
        {
          key: 'title',
          oper: 'CONTAINS',
          vals: ['CPU']
        }
      ],
      settings: {
        severity: 'Warning'
      }
    }
  ],
  3: [],
  4: [],
  5: []
};

// 模拟路由规则数据
const mockRoutingRules = {
  // 按集成ID组织数据
  1: {
    integration_id: 1,
    cases: [
      {
        if: [
          {
            key: 'severity',
            oper: 'IN',
            vals: ['Critical']
          }
        ],
        channel_ids: [1, 2],
        fallthrough: true
      },
      {
        if: [
          {
            key: 'title',
            oper: 'IN',
            vals: ['test*']
          },
          {
            key: 'description',
            oper: 'IN',
            vals: ['*测试']
          }
        ],
        channel_ids: [3],
        fallthrough: true
      }
    ],
    default: {
      channel_ids: [1]
    },
    status: 'enabled',
    version: 1,
    updated_by: 1,
    creator_id: 1,
    created_at: **********,
    updated_at: **********
  },
  2: {
    integration_id: 2,
    cases: [
      {
        if: [
          {
            key: 'severity',
            oper: 'IN',
            vals: ['Warning', 'Critical']
          }
        ],
        channel_ids: [2],
        fallthrough: false
      }
    ],
    default: {
      channel_ids: [1, 3]
    },
    status: 'enabled',
    version: 1,
    updated_by: 1,
    creator_id: 1,
    created_at: **********,
    updated_at: **********
  },
  3: {
    integration_id: 3,
    cases: [],
    default: {
      channel_ids: [1]
    },
    status: 'enabled',
    version: 1,
    updated_by: 1,
    creator_id: 1,
    created_at: **********,
    updated_at: **********
  },
  4: {
    integration_id: 4,
    cases: [
      {
        if: [
          {
            key: 'title',
            oper: 'CONTAINS',
            vals: ['error']
          }
        ],
        channel_ids: [2],
        fallthrough: true
      }
    ],
    default: {
      channel_ids: [1]
    },
    status: 'enabled',
    version: 1,
    updated_by: 1,
    creator_id: 1,
    created_at: **********,
    updated_at: **********
  },
  5: {
    integration_id: 5,
    cases: [],
    default: {
      channel_ids: [1]
    },
    status: 'disabled',
    version: 1,
    updated_by: 1,
    creator_id: 1,
    created_at: **********,
    updated_at: **********
  }
};

// 模拟告警示例数据
const mockAlertExamples = {
  1: [
    {
      id: 1,
      title: "Webhook Test(All the fields in the notification are examples)",
      alert_severity: "Info",
      labels: {
        alertname: "webhook测试(各通知字段均为示例)",
        alertState: "ALERT",
        check: "webhook测试(各通知字段均为示例)",
        curValue: "80",
        dimensions: "{userId=11111111111,instanceId=testInstanceId}",
        expression: "$Value > 75",
        instanceId: "testInstanceId",
        lastTime: "5 days",
        metric: "CPUUtilization",
        metricName: "(ECS)CPU使用率",
        metricProject: "acs_ecs_dashboard",
        namespace: "acs_ecs_dashboard",
        preTriggerLevel: "INFO",
        rawMetricName: "CPUUtilization",
        regionId: "cn-hangzhou",
        regionName: "cn-hangzhou",
        resource: "testInstanceId",
        ruleId: "testRuleId",
        triggerLevel: "INFO",
        userId: "testUserId"
      },
      annotations: {
        message: "CPU使用率过高，当前值: 80%"
      },
      startsAt: "2023-04-10T11:46:38Z",
      endsAt: "2023-04-10T11:46:38Z",
      generatorURL: "http://localhost:9090/graph?g0.expr=up%7Bjob%3D%22prometheus%22%7D+%3D%3D+1&g0.tab=1"
    },
    {
      id: 2,
      title: "Web Service Error",
      alert_severity: "Critical",
      labels: {
        alertname: "WebServiceError",
        alertState: "ALERT",
        check: "Web服务错误检查",
        curValue: "500",
        dimensions: "{serviceId=web-frontend,instanceId=web-1}",
        expression: "$StatusCode >= 500",
        instanceId: "web-1",
        lastTime: "10 minutes",
        metric: "HttpStatusCode",
        metricName: "HTTP状态码",
        metricProject: "web_monitoring",
        namespace: "web_services",
        preTriggerLevel: "WARNING",
        rawMetricName: "HttpStatusCode",
        regionId: "cn-beijing",
        regionName: "cn-beijing",
        resource: "web-frontend",
        ruleId: "web-error-rule",
        triggerLevel: "CRITICAL",
        userId: "admin"
      },
      annotations: {
        message: "Web服务返回错误状态码: 500"
      },
      startsAt: "2023-04-15T09:23:51Z",
      endsAt: "2023-04-15T09:23:51Z",
      generatorURL: "http://localhost:9090/graph?g0.expr=http_status_code+%3E%3D+500&g0.tab=1"
    }
  ],
  2: [
    {
      id: 3,
      title: "High CPU Usage",
      alert_severity: "Warning",
      labels: {
        alertname: "HighCpuUsage",
        alertState: "ALERT",
        check: "CPU使用率检查",
        curValue: "85",
        dimensions: "{nodeId=node-1,clusterId=cluster-a}",
        expression: "$Value > 80",
        instanceId: "node-1",
        lastTime: "15 minutes",
        metric: "CPUUtilization",
        metricName: "CPU使用率",
        metricProject: "kubernetes_cluster",
        namespace: "kube_system",
        preTriggerLevel: "INFO",
        rawMetricName: "CPUUtilization",
        regionId: "cn-shanghai",
        regionName: "cn-shanghai",
        resource: "node-1",
        ruleId: "cpu-warning-rule",
        triggerLevel: "WARNING",
        userId: "system"
      },
      annotations: {
        message: "节点CPU使用率过高，当前值: 85%，请检查是否需要扩容"
      },
      startsAt: "2023-04-14T16:42:10Z",
      endsAt: "2023-04-14T16:42:10Z",
      generatorURL: "http://localhost:9090/graph?g0.expr=node_cpu_usage+%3E+80&g0.tab=1"
    },
    {
      id: 4,
      title: "Database connection error",
      alert_severity: "Critical",
      labels: {
        alertname: "DatabaseConnectionError",
        alertState: "ALERT",
        check: "数据库连接检查",
        curValue: "0",
        dimensions: "{dbId=mysql-prod,instanceId=db-master}",
        expression: "$Connections == 0",
        instanceId: "db-master",
        lastTime: "5 minutes",
        metric: "DatabaseConnections",
        metricName: "数据库连接数",
        metricProject: "database_monitoring",
        namespace: "database_services",
        preTriggerLevel: "WARNING",
        rawMetricName: "DatabaseConnections",
        regionId: "cn-beijing",
        regionName: "cn-beijing",
        resource: "mysql-prod",
        ruleId: "db-connection-rule",
        triggerLevel: "CRITICAL",
        userId: "admin"
      },
      annotations: {
        message: "数据库连接失败，当前连接数: 0，请立即检查数据库状态"
      },
      startsAt: "2023-04-15T08:17:33Z",
      endsAt: "2023-04-15T08:17:33Z",
      generatorURL: "http://localhost:9090/graph?g0.expr=database_connections+%3D%3D+0&g0.tab=1"
    }
  ],
  3: [
    {
      id: 5,
      title: "ECS实例CPU使用率过高",
      alert_severity: "Warning",
      labels: {
        alertname: "EcsCpuHigh",
        alertState: "ALERT",
        check: "ECS CPU监控",
        curValue: "92",
        dimensions: "{instanceId=i-bp67acfmxazb4ph,userId=1234567890}",
        expression: "$Value > 90",
        instanceId: "i-bp67acfmxazb4ph",
        lastTime: "30 minutes",
        metric: "cpu_total",
        metricName: "CPU使用率",
        metricProject: "acs_ecs_dashboard",
        namespace: "acs_ecs_dashboard",
        preTriggerLevel: "INFO",
        rawMetricName: "cpu_total",
        regionId: "cn-hangzhou",
        regionName: "杭州",
        resource: "i-bp67acfmxazb4ph",
        ruleId: "ecs_cpu_rule",
        triggerLevel: "WARNING",
        userId: "1234567890"
      },
      annotations: {
        message: "ECS实例CPU使用率达到92%，超过警戒阈值90%"
      },
      startsAt: "2023-04-14T23:05:11Z",
      endsAt: "2023-04-14T23:05:11Z",
      generatorURL: "https://cloudmonitor.console.aliyun.com/"
    }
  ],
  4: [
    {
      id: 6,
      title: "邮件服务异常告警",
      alert_severity: "Critical",
      labels: {
        alertname: "EmailServiceError",
        alertState: "ALERT",
        check: "邮件服务检查",
        curValue: "Error",
        dimensions: "{serviceId=email-service,instanceId=email-1}",
        expression: "$Status == 'Error'",
        instanceId: "email-1",
        lastTime: "10 minutes",
        metric: "ServiceStatus",
        metricName: "服务状态",
        metricProject: "email_monitoring",
        namespace: "email_services",
        preTriggerLevel: "WARNING",
        rawMetricName: "ServiceStatus",
        regionId: "cn-beijing",
        regionName: "cn-beijing",
        resource: "email-service",
        ruleId: "email-service-rule",
        triggerLevel: "CRITICAL",
        userId: "admin"
      },
      annotations: {
        message: "邮件服务出现异常，当前状态: Error，请立即检查服务状态"
      },
      startsAt: "2023-04-13T18:42:33Z",
      endsAt: "2023-04-13T18:42:33Z",
      generatorURL: "http://monitoring.example.com/"
    }
  ],
  5: [
    {
      id: 7,
      title: "Grafana Alert: High Memory Usage",
      alert_severity: "Warning",
      labels: {
        alertname: "HighMemoryUsage",
        alertState: "ALERT",
        check: "内存使用率检查",
        curValue: "87",
        dimensions: "{nodeId=grafana-1,clusterId=monitoring}",
        expression: "$Value > 85",
        instanceId: "grafana-1",
        lastTime: "20 minutes",
        metric: "MemoryUtilization",
        metricName: "内存使用率",
        metricProject: "grafana_monitoring",
        namespace: "monitoring_system",
        preTriggerLevel: "INFO",
        rawMetricName: "MemoryUtilization",
        regionId: "cn-shanghai",
        regionName: "cn-shanghai",
        resource: "grafana-1",
        ruleId: "memory-warning-rule",
        triggerLevel: "WARNING",
        userId: "system"
      },
      annotations: {
        message: "Grafana服务器内存使用率过高，当前值: 87%，请检查是否存在内存泄漏"
      },
      startsAt: "2023-04-10T11:23:56Z",
      endsAt: "2023-04-10T11:23:56Z",
      generatorURL: "http://grafana.example.com/alerting/"
    }
  ]
};

// 模拟集成数据
let mockIntegrations = [
  {
    id: 1,
    name: '生产环境Prometheus',
    typeId: 4,
    type: 'Prometheus',
    status: 'enabled',
    lastEventTime: '2023-04-15 14:30:22',
    webhook: generatePushUrl('Prometheus', 1),
    tagRules: mockTagRules[1],
    noiseRules: mockNoiseRules[1],
    alertProcessingRules: mockAlertProcessingRules[1],
    alertExamples: mockAlertExamples[1],
    routingRules: mockRoutingRules[1]
  },
  {
    id: 2,
    name: '测试环境Prometheus',
    typeId: 4,
    type: 'Prometheus',
    status: 'enabled',
    lastEventTime: '2023-04-15 09:12:47',
    webhook: generatePushUrl('Prometheus', 2),
    tagRules: mockTagRules[2],
    noiseRules: mockNoiseRules[2],
    alertProcessingRules: mockAlertProcessingRules[2],
    alertExamples: mockAlertExamples[2],
    routingRules: mockRoutingRules[2]
  },
  {
    id: 3,
    name: '阿里云ECS监控',
    typeId: 1,
    type: '阿里云监控 CM 事件',
    status: 'enabled',
    lastEventTime: '2023-04-14 23:05:11',
    webhook: generatePushUrl('阿里云监控 CM 事件', 3),
    tagRules: mockTagRules[3],
    noiseRules: mockNoiseRules[3],
    alertProcessingRules: mockAlertProcessingRules[3],
    alertExamples: mockAlertExamples[3],
    routingRules: mockRoutingRules[3]
  },
  {
    id: 4,
    name: '运维团队邮件告警',
    typeId: 5,
    type: '邮件 Email',
    status: 'enabled',
    lastEventTime: '2023-04-13 18:42:33',
    webhook: generatePushUrl('邮件 Email', 4),
    tagRules: mockTagRules[4],
    noiseRules: mockNoiseRules[4],
    alertProcessingRules: mockAlertProcessingRules[4],
    alertExamples: mockAlertExamples[4],
    routingRules: mockRoutingRules[4]
  },
  {
    id: 5,
    name: '开发环境Grafana',
    typeId: 2,
    type: 'Grafana',
    status: 'disabled',
    lastEventTime: '2023-04-10 11:23:56',
    webhook: generatePushUrl('Grafana', 5),
    tagRules: mockTagRules[5],
    noiseRules: mockNoiseRules[5],
    alertProcessingRules: mockAlertProcessingRules[5],
    alertExamples: mockAlertExamples[5],
    routingRules: mockRoutingRules[5]
  }
];

/**
 * 获取集成类型列表
 * @returns {Promise<Array>} 集成类型列表
 */
export const getIntegrationTypes = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockIntegrationTypes);
    }, 300);
  });
};

/**
 * 获取集成列表（支持分页和筛选）
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页条数
 * @param {number|null} params.typeId 集成类型ID
 * @param {string} params.status 状态筛选
 * @param {string} params.typeKeyword 类型关键字
 * @param {string} params.nameKeyword 名称关键字
 * @returns {Promise<Object>} 分页数据
 */
export const getIntegrations = (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockIntegrations];

      // 应用筛选
      if (params) {
        if (params.typeId) {
          filteredData = filteredData.filter(item => item.typeId === params.typeId);
        }

        if (params.status) {
          filteredData = filteredData.filter(item => item.status === params.status);
        }

        if (params.typeKeyword) {
          const keyword = params.typeKeyword.toLowerCase();
          filteredData = filteredData.filter(item => item.type.toLowerCase().includes(keyword));
        }

        if (params.nameKeyword) {
          const keyword = params.nameKeyword.toLowerCase();
          filteredData = filteredData.filter(item => item.name.toLowerCase().includes(keyword));
        }
      }

      // 计算总数
      const total = filteredData.length;

      // 分页
      const page = params?.page || 1;
      const pageSize = params?.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      const paginatedData = filteredData.slice(start, end);

      resolve({
        success: true,
        data: paginatedData,
        total: total
      });
    }, 500);
  });
};

/**
 * 切换集成状态
 * @param {number} id 集成ID
 * @param {string} status 新状态
 * @returns {Promise<Object>} 更新结果
 */
export const toggleIntegrationStatus = (id, status) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockIntegrations.findIndex(item => item.id === id);
      if (index !== -1) {
        mockIntegrations[index].status = status;
        resolve({
          success: true,
          data: mockIntegrations[index]
        });
      } else {
        resolve({
          success: false,
          message: '未找到该集成'
        });
      }
    }, 300);
  });
};

/**
 * 获取单个集成详情
 * @param {number|string} id 集成ID
 * @returns {Promise<Object>} 集成详情
 */
export const getIntegrationDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 将id转换为数字，因为路由参数可能是字符串
      const numId = parseInt(id, 10);

      // 查找集成数据
      const integration = mockIntegrations.find(item => item.id === numId);

      if (integration) {
        // 添加一些模拟图片
        const mockImages = integration.id % 2 === 0 ? [
          {
            id: 1,
            name: '示例配置图.png',
            url: 'https://via.placeholder.com/300x200?text=Config+Example'
          }
        ] : [];

        resolve({
          ...integration,
          images: mockImages
        });
      } else {
        // 如果没有找到对应数据，创建一个模拟数据而不是直接报错
        console.warn(`未找到ID为${id}的集成，返回模拟数据`);

        // 获取类型ID，默认为Prometheus(4)
        const typeId = parseInt(localStorage.getItem('lastViewedTypeId') || '4', 10);

        // 查找对应的类型名称
        const typeInfo = mockIntegrationTypes.find(t => t.id === typeId) || mockIntegrationTypes[3];

        // 创建模拟数据
        const mockIntegration = {
          id: numId,
          name: `${typeInfo.name}集成-${numId}`,
          typeId: typeInfo.id,
          type: typeInfo.name,
          status: 'enabled',
          lastEventTime: '暂未收到告警事件',
          webhook: generatePushUrl(typeInfo.name, numId)
        };

        // 将模拟数据添加到数据集中以供后续使用
        mockIntegrations.push(mockIntegration);

        resolve(mockIntegration);
      }
    }, 300);
  });
};

// 创建集成
export const createIntegration = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = Math.max(...mockIntegrations.map(item => item.id), 0) + 1;
      const newIntegration = {
        ...data,
        id: newId,
        lastEventTime: '暂未收到告警事件'
      };

      mockIntegrations.unshift(newIntegration);

      resolve({
        success: true,
        data: newIntegration
      });
    }, 500);
  });
};

// 更新集成
export const updateIntegration = (id, data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockIntegrations.findIndex(item => item.id === id);
      if (index !== -1) {
        mockIntegrations[index] = {
          ...mockIntegrations[index],
          ...data,
          id // 确保ID不变
        };

        resolve({
          success: true,
          data: mockIntegrations[index]
        });
      } else {
        resolve({
          success: false,
          message: '未找到该集成'
        });
      }
    }, 300);
  });
};

/**
 * 获取标签增强规则
 * @param {number|string} integrationId 集成ID
 * @returns {Promise<Array>} 标签增强规则列表
 */
export const getTagRules = (integrationId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(integrationId, 10);
      const integration = mockIntegrations.find(item => item.id === numId);

      if (integration && integration.tagRules) {
        resolve({
          success: true,
          data: integration.tagRules
        });
      } else {
        resolve({
          success: true,
          data: []
        });
      }
    }, 300);
  });
};

/**
 * 更新标签增强规则
 * @param {number|string} integrationId 集成ID
 * @param {Array} rules 规则列表
 * @returns {Promise<Object>} 更新结果
 */
export const updateTagRules = (integrationId, rules) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(integrationId, 10);
      const index = mockIntegrations.findIndex(item => item.id === numId);

      if (index !== -1) {
        mockIntegrations[index].tagRules = rules;

        resolve({
          success: true,
          data: mockIntegrations[index].tagRules
        });
      } else {
        resolve({
          success: false,
          message: '未找到该集成'
        });
      }
    }, 500);
  });
};

/**
 * 获取降噪规则
 * @param {number|string} integrationId 集成ID
 * @returns {Promise<Array>} 降噪规则列表
 */
export const getNoiseRules = (integrationId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(integrationId, 10);
      const integration = mockIntegrations.find(item => item.id === numId);

      if (integration && integration.noiseRules) {
        resolve({
          success: true,
          data: integration.noiseRules
        });
      } else {
        resolve({
          success: true,
          data: []
        });
      }
    }, 300);
  });
};

/**
 * 更新降噪规则
 * @param {number|string} integrationId 集成ID
 * @param {Array} rules 规则列表
 * @returns {Promise<Object>} 更新结果
 */
export const updateNoiseRules = (integrationId, rules) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(integrationId, 10);
      const index = mockIntegrations.findIndex(item => item.id === numId);

      if (index !== -1) {
        mockIntegrations[index].noiseRules = rules;

        resolve({
          success: true,
          data: mockIntegrations[index].noiseRules
        });
      } else {
        resolve({
          success: false,
          message: '未找到该集成'
        });
      }
    }, 500);
  });
};

/**
 * 获取告警处理规则
 * @param {number|string} integrationId 集成ID
 * @returns {Promise<Array>} 告警处理规则列表
 */
export const getAlertProcessingRules = (integrationId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(integrationId, 10);
      const integration = mockIntegrations.find(item => item.id === numId);

      if (integration && integration.alertProcessingRules) {
        resolve({
          success: true,
          data: integration.alertProcessingRules
        });
      } else {
        resolve({
          success: true,
          data: []
        });
      }
    }, 300);
  });
};

/**
 * 更新告警处理规则
 * @param {number|string} integrationId 集成ID
 * @param {Array} rules 规则列表
 * @returns {Promise<Object>} 更新结果
 */
export const updateAlertProcessingRules = (integrationId, rules) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(integrationId, 10);
      const index = mockIntegrations.findIndex(item => item.id === numId);

      if (index !== -1) {
        mockIntegrations[index].alertProcessingRules = rules;

        resolve({
          success: true,
          data: mockIntegrations[index].alertProcessingRules
        });
      } else {
        resolve({
          success: false,
          message: '未找到该集成'
        });
      }
    }, 500);
  });
};

/**
 * 获取告警示例
 * @param {number|string} integrationId 集成ID
 * @returns {Promise<Array>} 告警示例列表
 */
export const getAlertExamples = (integrationId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(integrationId, 10);
      const integration = mockIntegrations.find(item => item.id === numId);

      if (integration && integration.alertExamples) {
        resolve({
          success: true,
          data: integration.alertExamples
        });
      } else {
        resolve({
          success: true,
          data: []
        });
      }
    }, 300);
  });
};

/**
 * 获取路由规则
 * @param {number|string} integrationId 集成ID
 * @returns {Promise<Object>} 路由规则
 */
export const getRoutingRules = (integrationId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(integrationId, 10);
      const integration = mockIntegrations.find(item => item.id === numId);

      if (integration && integration.routingRules) {
        resolve({
          success: true,
          data: integration.routingRules
        });
      } else {
        // 如果没有找到路由规则，返回默认的空规则
        resolve({
          success: true,
          data: {
            integration_id: numId,
            cases: [],
            default: {
              channel_ids: []
            },
            status: 'enabled',
            version: 1,
            updated_by: 1,
            creator_id: 1,
            created_at: Math.floor(Date.now() / 1000),
            updated_at: Math.floor(Date.now() / 1000)
          }
        });
      }
    }, 300);
  });
};

/**
 * 更新路由规则
 * @param {number|string} integrationId 集成ID
 * @param {Object} rules 路由规则
 * @returns {Promise<Object>} 更新结果
 */
export const updateRoutingRules = (integrationId, rules) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(integrationId, 10);
      const index = mockIntegrations.findIndex(item => item.id === numId);

      if (index !== -1) {
        // 更新时间戳
        rules.updated_at = Math.floor(Date.now() / 1000);

        mockIntegrations[index].routingRules = rules;

        resolve({
          success: true,
          data: mockIntegrations[index].routingRules
        });
      } else {
        resolve({
          success: false,
          message: '未找到该集成'
        });
      }
    }, 500);
  });
};

/**
 * 获取协作空间列表
 * @returns {Promise<Array>} 协作空间列表
 */
export const getChannels = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const channels = [
        { id: 1, name: '研发空间', type: '研发团队' },
        { id: 2, name: '测试空间', type: '测试团队' },
        { id: 3, name: '运维空间', type: '运维团队' },
        { id: 4, name: '产品空间', type: '产品团队' }
      ];

      resolve({
        success: true,
        data: channels
      });
    }, 300);
  });
};