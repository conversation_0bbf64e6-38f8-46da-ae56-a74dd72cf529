// 空间指标模拟数据
export const spaceMetricsData = {
  // 按空间ID组织数据
  1: {
    mtta: {
      value: 15,
      unit: '分钟',
      trend: 'down', // 'up', 'down', 'stable'
      change: 5 // 变化百分比
    },
    mttr: {
      value: 45,
      unit: '分钟',
      trend: 'down',
      change: 10
    },
    incidentCount: {
      value: 8,
      trend: 'up',
      change: 2
    },
    noiseReduction: {
      value: 75,
      unit: '%',
      trend: 'up',
      change: 5
    },
    // 该空间的消息列表
    messages: ['3148A4', '86DD00', 'B520A7', 'A2BC78']
  },
  2: {
    mtta: {
      value: 22,
      unit: '分钟',
      trend: 'up',
      change: 8
    },
    mttr: {
      value: 60,
      unit: '分钟',
      trend: 'stable',
      change: 0
    },
    incidentCount: {
      value: 5,
      trend: 'down',
      change: 3
    },
    noiseReduction: {
      value: 62,
      unit: '%',
      trend: 'up',
      change: 7
    },
    // 该空间的消息列表
    messages: ['86DDE6', 'D45E9F']
  },
  3: {
    mtta: {
      value: 8,
      unit: '分钟',
      trend: 'down',
      change: 12
    },
    mttr: {
      value: 30,
      unit: '分钟',
      trend: 'down',
      change: 15
    },
    incidentCount: {
      value: 3,
      trend: 'stable',
      change: 0
    },
    noiseReduction: {
      value: 80,
      unit: '%',
      trend: 'up',
      change: 10
    },
    // 该空间的消息列表
    messages: ['97F2C8']
  },
  4: {
    mtta: {
      value: 0,
      unit: '分钟',
      trend: 'stable',
      change: 0
    },
    mttr: {
      value: 0,
      unit: '分钟',
      trend: 'stable',
      change: 0
    },
    incidentCount: {
      value: 0,
      trend: 'stable',
      change: 0
    },
    noiseReduction: {
      value: 0,
      unit: '%',
      trend: 'stable',
      change: 0
    },
    // 该空间的消息列表
    messages: []
  },
  5: {
    mtta: {
      value: 12,
      unit: '分钟',
      trend: 'down',
      change: 8
    },
    mttr: {
      value: 35,
      unit: '分钟',
      trend: 'down',
      change: 12
    },
    incidentCount: {
      value: 6,
      trend: 'down',
      change: 2
    },
    noiseReduction: {
      value: 70,
      unit: '%',
      trend: 'up',
      change: 5
    },
    // 该空间的消息列表
    messages: ['3148A4', 'B520A7', '97F2C8']
  },
  7: {
    mtta: {
      value: 18,
      unit: '分钟',
      trend: 'up',
      change: 3
    },
    mttr: {
      value: 50,
      unit: '分钟',
      trend: 'up',
      change: 5
    },
    incidentCount: {
      value: 4,
      trend: 'down',
      change: 1
    },
    noiseReduction: {
      value: 65,
      unit: '%',
      trend: 'stable',
      change: 0
    },
    // 该空间的消息列表
    messages: ['86DDE6', 'D45E9F']
  }
};

// 获取空间指标数据
export const getSpaceMetrics = (spaceId) => {
  return Promise.resolve({
    code: 200,
    data: spaceMetricsData[spaceId] || {
      mtta: { value: 0, unit: '分钟', trend: 'stable', change: 0 },
      mttr: { value: 0, unit: '分钟', trend: 'stable', change: 0 },
      incidentCount: { value: 0, trend: 'stable', change: 0 },
      noiseReduction: { value: 0, unit: '%', trend: 'stable', change: 0 },
      messages: []
    }
  });
};

// 获取空间消息列表
export const getSpaceMessages = (spaceId) => {
  const metrics = spaceMetricsData[spaceId];
  if (!metrics) {
    return Promise.resolve({
      code: 200,
      data: []
    });
  }
  
  // 导入消息列表数据
  const { messageListData } = require('./messageList');
  
  // 过滤出该空间的消息
  const messages = messageListData.filter(msg => 
    metrics.messages.includes(msg.id)
  );
  
  return Promise.resolve({
    code: 200,
    data: messages
  });
};
