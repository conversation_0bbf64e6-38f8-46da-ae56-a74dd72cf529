// 告警详情模拟数据
export const alertDetailsData = [
  {
    id: 'B520A7',
    source: 'Webhook Test',
    platform: 'testinstanceid',
    status: 'processing',
    statusText: '处理中',
    timeInfo: '13天3小时',
    description: 'webhook测试(各通知字段均为示例) / testInstanceId',
    impact: '暂无描述',
    parameters: [
      { label: 'curValue（监控项的当前值）', value: '80' },
      { label: 'preTriggerLevel（上一次报警级别）', value: 'INFO' },
      { label: 'triggerLevel（本次报警级别）', value: 'INFO' },
      { label: 'alertState（报警状态）', value: 'ALERT' },
      { label: 'lastTime（上次触发时间）', value: '5 days' },
      { label: 'alertName（报警名称）', value: 'webhook测试(各通知字段均为示例)' },
      { label: 'check（检查项）', value: 'webhook测试(各通知字段均为示例)' },
      { label: 'dimensions（告警对象）', value: '{userId=11111111111,instanceId=testinstanceId}' },
      { label: 'expression（报警条件）', value: '$Value > 75' },
      { label: 'instanceId', value: 'testinstanceId' },
      { label: 'metric（指标名称）', value: 'CPUUtilization' },
      { label: 'metricName（监控项名称）', value: '(ECS)CPU使用率' },
      { label: 'metricProject（云服务名称）', value: 'acs_ecs_dashboard' },
      { label: 'namespace（云服务命名空间）', value: 'acs_ecs_dashboard' },
      { label: 'rawMetricName（指标名称）', value: 'CPUUtilization' },
      { label: 'regionId（Region ID）', value: 'cn-hangzhou' }
    ]
  },
  {
    id: '3148A4',
    source: 'TestAlert',
    platform: 'Grafana',
    status: 'pending',
    statusText: '待处理',
    timeInfo: '559天27分',
    description: '这是一个测试告警，来自Grafana监控系统',
    impact: '可能影响系统性能和响应时间',
    parameters: [
      { label: 'curValue（监控项的当前值）', value: '95' },
      { label: 'preTriggerLevel（上一次报警级别）', value: 'WARNING' },
      { label: 'triggerLevel（本次报警级别）', value: 'CRITICAL' },
      { label: 'alertState（报警状态）', value: 'ALERT' },
      { label: 'lastTime（上次触发时间）', value: '2 days' }
    ]
  }
];

// 告警活动时间线数据
export const alertActivitiesData = {
  'B520A7': [
    {
      user: 'zhangyupeng2',
      content: '更新了故障影响',
      timestamp: '2025-04-23 16:16:01',
      type: '',
      color: '#409EFF'
    },
    {
      user: 'zhangyupeng2',
      content: '更新了故障描述',
      timestamp: '2025-04-23 16:15:51',
      type: '',
      color: '#409EFF'
    },
    {
      user: 'zhangyupeng2',
      content: '更新了故障影响',
      timestamp: '2025-04-23 16:15:38',
      type: '',
      color: '#409EFF'
    },
    {
      user: 'zhangyupeng2',
      content: '更新了故障描述',
      timestamp: '2025-04-23 16:15:37',
      type: '',
      color: '#409EFF'
    },
    {
      user: 'zhangyupeng2',
      content: '暂缓处理2小时，已自保自动升级。警2025-04-23 17:24:41时系统将新分派。',
      timestamp: '2025-04-23 15:24:41',
      type: 'warning',
      color: '#E6A23C'
    }
  ],
  '3148A4': [
    {
      user: 'zhangyupeng2',
      content: '已识别问题原因',
      timestamp: '2025-04-24 10:30:22',
      type: '',
      color: '#409EFF'
    },
    {
      user: 'wangxiaoming',
      content: '正在排查系统性能问题',
      timestamp: '2025-04-24 09:45:15',
      type: '',
      color: '#409EFF'
    },
    {
      user: 'system',
      content: '告警自动升级为Critical级别',
      timestamp: '2025-04-24 08:15:30',
      type: 'warning',
      color: '#E6A23C'
    }
  ]
};

// 告警历史变更数据
export const alertHistoryData = {
  'B520A7': [
    {
      time: '2025-04-23 16:16:01',
      user: 'zhangyupeng2',
      content: '更新了故障影响'
    },
    {
      time: '2025-04-23 16:15:51',
      user: 'zhangyupeng2',
      content: '更新了故障描述'
    }
  ],
  '3148A4': [
    {
      time: '2025-04-24 10:30:22',
      user: 'zhangyupeng2',
      content: '将状态从"待处理"更改为"处理中"'
    },
    {
      time: '2025-04-24 09:45:15',
      user: 'wangxiaoming',
      content: '添加了问题备注'
    }
  ]
};

// 关联告警数据
export const relatedAlertsData = {
  'B520A7': [
    { id: '3148A4', source: 'TestAlert', time: '4月23日 14:46:31', platform: 'Grafana' },
    { id: '86DDE6', source: 'TestAlert', time: '4月10日 15:02:56', platform: 'Grafana' }
  ],
  '3148A4': [
    { id: 'B520A7', source: 'Webhook Test', time: '4月10日 11:46:38', platform: 'testinstanceid' }
  ]
}; 