/**
 * 通知渠道模块的mock数据接口
 */
import { generatePushUrl } from '@/utils/webhook';

// 模拟通知渠道类型数据
const mockChannelTypes = [
  {
    id: 1,
    name: '金山协作应用',
    icon: '', // 使用SVG图片，不需要图标类名
    bgColor: '#4285f4',
    description: '通过金山协作应用发送通知'
  },
  {
    id: 2,
    name: '金山协作机器人',
    icon: '', // 使用SVG图片，不需要图标类名
    bgColor: '#34a853',
    description: '通过金山协作机器人发送通知'
  },
  {
    id: 3,
    name: 'HTTP应用',
    icon: '', // 使用Element Plus图标
    bgColor: '#ea4335',
    description: '通过HTTP接口发送通知'
  },
  {
    id: 4,
    name: '邮件应用',
    icon: '', // 使用Element Plus图标
    bgColor: '#fbbc05',
    description: '通过邮件发送通知'
  }
];

// 模拟通知渠道数据
let mockChannels = [
  {
    id: 1,
    name: '运维团队通知',
    typeId: 1,
    type: '金山协作应用',
    status: 'enabled',
    lastNotifyTime: '2023-05-15 14:30:22',
    webhook: generatePushUrl('金山协作应用', 1)
  },
  {
    id: 2,
    name: '开发团队通知',
    typeId: 1,
    type: '金山协作应用',
    status: 'enabled',
    lastNotifyTime: '2023-05-14 09:12:47',
    webhook: generatePushUrl('金山协作应用', 2)
  },
  {
    id: 3,
    name: '警报机器人',
    typeId: 2,
    type: '金山协作机器人',
    status: 'enabled',
    lastNotifyTime: '2023-05-13 23:05:11',
    webhook: generatePushUrl('金山协作机器人', 3)
  },
  {
    id: 4,
    name: '邮件告警通知',
    typeId: 4,
    type: '邮件应用',
    status: 'enabled',
    lastNotifyTime: '2023-05-12 18:42:33',
    webhook: generatePushUrl('邮件应用', 4)
  },
  {
    id: 5,
    name: 'API回调服务',
    typeId: 3,
    type: 'HTTP应用',
    status: 'disabled',
    lastNotifyTime: '2023-05-10 11:23:56',
    webhook: generatePushUrl('HTTP应用', 5)
  }
];

/**
 * 获取通知渠道类型列表
 * @returns {Promise<Array>} 通知渠道类型列表
 */
export const getChannelTypes = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockChannelTypes);
    }, 300);
  });
};

/**
 * 获取通知渠道列表（支持分页和筛选）
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页条数
 * @param {number|null} params.typeId 通知渠道类型ID
 * @param {string} params.status 状态筛选
 * @param {string} params.typeKeyword 类型关键字
 * @param {string} params.nameKeyword 名称关键字
 * @returns {Promise<Object>} 分页数据
 */
export const getChannels = (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockChannels];
      
      // 应用筛选
      if (params) {
        if (params.typeId) {
          filteredData = filteredData.filter(item => item.typeId === params.typeId);
        }
        
        if (params.status) {
          filteredData = filteredData.filter(item => item.status === params.status);
        }
        
        if (params.typeKeyword) {
          const keyword = params.typeKeyword.toLowerCase();
          filteredData = filteredData.filter(item => item.type.toLowerCase().includes(keyword));
        }
        
        if (params.nameKeyword) {
          const keyword = params.nameKeyword.toLowerCase();
          filteredData = filteredData.filter(item => item.name.toLowerCase().includes(keyword));
        }
      }
      
      // 计算总数
      const total = filteredData.length;
      
      // 分页
      const page = params?.page || 1;
      const pageSize = params?.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      const paginatedData = filteredData.slice(start, end);
      
      resolve({
        success: true,
        data: paginatedData,
        total: total
      });
    }, 500);
  });
};

/**
 * 切换通知渠道状态
 * @param {number} id 通知渠道ID
 * @param {string} status 新状态
 * @returns {Promise<Object>} 更新结果
 */
export const toggleChannelStatus = (id, status) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockChannels.findIndex(item => item.id === id);
      if (index !== -1) {
        mockChannels[index].status = status;
        resolve({
          success: true,
          data: mockChannels[index]
        });
      } else {
        resolve({
          success: false,
          message: '未找到该通知渠道'
        });
      }
    }, 300);
  });
};

/**
 * 获取单个通知渠道详情
 * @param {number|string} id 通知渠道ID
 * @returns {Promise<Object>} 通知渠道详情
 */
export const getChannelDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 将id转换为数字，因为路由参数可能是字符串
      const numId = parseInt(id, 10);
      
      // 查找通知渠道数据
      const channel = mockChannels.find(item => item.id === numId);
      
      if (channel) {
        resolve(channel);
      } else {
        // 如果没有找到对应数据，创建一个模拟数据
        console.warn(`未找到ID为${id}的通知渠道，返回模拟数据`);
        
        // 获取类型ID，默认为金山协作应用(1)
        const typeId = parseInt(localStorage.getItem('lastViewedTypeId') || '1', 10);
        
        // 查找对应的类型名称
        const typeInfo = mockChannelTypes.find(t => t.id === typeId) || mockChannelTypes[0];
        
        // 创建模拟数据
        const mockChannel = {
          id: numId,
          name: `${typeInfo.name}-${numId}`,
          typeId: typeInfo.id,
          type: typeInfo.name,
          status: 'enabled',
          lastNotifyTime: '暂未发送通知',
          webhook: generatePushUrl(typeInfo.name, numId)
        };
        
        // 将模拟数据添加到数据集中以供后续使用
        mockChannels.push(mockChannel);
        
        resolve(mockChannel);
      }
    }, 300);
  });
};

// 创建通知渠道
export const createChannel = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = Math.max(...mockChannels.map(item => item.id), 0) + 1;
      const newChannel = {
        ...data,
        id: newId,
        lastNotifyTime: '暂未发送通知'
      };
      
      mockChannels.unshift(newChannel);
      
      resolve({
        success: true,
        data: newChannel
      });
    }, 500);
  });
};

// 更新通知渠道
export const updateChannel = (id, data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockChannels.findIndex(item => item.id === id);
      if (index !== -1) {
        mockChannels[index] = { 
          ...mockChannels[index], 
          ...data,
          id // 确保ID不变
        };
        
        resolve({
          success: true,
          data: mockChannels[index]
        });
      } else {
        resolve({
          success: false,
          message: '未找到该通知渠道'
        });
      }
    }, 300);
  });
}; 