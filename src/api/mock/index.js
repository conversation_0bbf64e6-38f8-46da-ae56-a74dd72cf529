// 导入所有模拟API
import * as integration<PERSON>pi from './integration';
import * as spaceManagement<PERSON>pi from './spaceManagement';
import * as templateManagementApi from './templateManagement';
import * as docsApi from './docs';
import * as alertDetail<PERSON>pi from './alertDetail';
import * as messageList<PERSON>pi from './messageList';
import * as notify<PERSON><PERSON>nel<PERSON><PERSON> from './notifyChannel';
import * as spaceMetricsApi from './spaceMetrics';
import * as spaceDispatchStrategyApi from './spaceDispatchStrategy';
import * as noiseReductionApi from './noiseReduction';

// 导出所有API
export {
  integrationApi,
  spaceManagementApi,
  templateManagementApi,
  docsApi,
  alertDetailApi,
  messageListApi,
  notifyChannelApi,
  spaceMetricsApi,
  spaceDispatchStrategyApi,
  noiseReductionApi
};

// 默认导出所有API
export default {
  ...integrationApi,
  ...spaceManagementApi,
  ...templateManagementApi,
  ...docsApi,
  ...alertDetailApi,
  ...messageListApi,
  ...notifyChannelApi,
  ...spaceMetricsApi,
  ...spaceDispatchStrategyApi,
  ...noiseReductionApi
};
