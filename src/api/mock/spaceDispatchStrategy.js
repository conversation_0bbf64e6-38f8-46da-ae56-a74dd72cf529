// 分派策略模拟数据
const mockDispatchStrategies = {
  // 按空间ID组织数据
  1: [
    {
      channel_id: 1,
      rule_id: "rule-001",
      rule_name: "关键告警分派策略",
      template_id: "template-001",
      description: "处理关键级别告警的分派策略",
      status: "enabled",
      priority: 1,
      created_at: "2023-04-15 10:30:00",
      updated_at: "2023-04-15 10:30:00",
      updated_by: 1,
      layers: [
        {
          max_times: 3,
          notify_step: 1,
          escalate_window: 15,
          force_escalate: true,
          target: {
            team_ids: [1],
            person_ids: [101, 102],
            schedule_to_role_ids: {
              "schedule-001": [1, 2]
            },
            by: {
              follow_preference: true,
              critical: ["email", "sms"],
              warning: ["email"],
              info: ["email"]
            },
            webhooks: [
              {
                type: "feishu_app",
                settings: {}
              }
            ]
          }
        }
      ],
      aggr_window: 300,
      time_filters: [
        {
          start: "09:00",
          end: "18:00",
          repeat: [1, 2, 3, 4, 5],
          cal_id: "calendar-001",
          is_off: false
        }
      ],
      filters: [
        [
          {
            key: "severity",
            oper: "IN",
            vals: ["Critical"]
          }
        ]
      ]
    },
    {
      channel_id: 1,
      rule_id: "rule-002",
      rule_name: "警告级别分派策略",
      template_id: "template-002",
      description: "处理警告级别告警的分派策略",
      status: "enabled",
      priority: 2,
      created_at: "2023-04-15 11:30:00",
      updated_at: "2023-04-15 11:30:00",
      updated_by: 1,
      layers: [
        {
          max_times: 2,
          notify_step: 1,
          escalate_window: 30,
          force_escalate: false,
          target: {
            team_ids: [2],
            person_ids: [103],
            schedule_to_role_ids: {},
            by: {
              follow_preference: true,
              critical: ["email"],
              warning: ["email"],
              info: ["email"]
            },
            webhooks: []
          }
        }
      ],
      aggr_window: 600,
      time_filters: [],
      filters: [
        [
          {
            key: "severity",
            oper: "IN",
            vals: ["Warning"]
          }
        ]
      ]
    }
  ],
  2: [
    {
      channel_id: 2,
      rule_id: "rule-003",
      rule_name: "数据库告警分派策略",
      template_id: "template-003",
      description: "处理数据库相关告警的分派策略",
      status: "enabled",
      priority: 1,
      created_at: "2023-04-16 09:30:00",
      updated_at: "2023-04-16 09:30:00",
      updated_by: 2,
      layers: [
        {
          max_times: 3,
          notify_step: 1,
          escalate_window: 15,
          force_escalate: true,
          target: {
            team_ids: [3],
            person_ids: [201, 202],
            schedule_to_role_ids: {},
            by: {
              follow_preference: true,
              critical: ["email", "sms"],
              warning: ["email"],
              info: ["email"]
            },
            webhooks: []
          }
        }
      ],
      aggr_window: 300,
      time_filters: [],
      filters: [
        [
          {
            key: "title",
            oper: "IN",
            vals: ["*数据库*", "*Database*"]
          }
        ]
      ]
    }
  ],
  3: [],
  4: [],
  5: [
    {
      channel_id: 5,
      rule_id: "rule-004",
      rule_name: "网络告警分派策略",
      template_id: "template-004",
      description: "处理网络相关告警的分派策略",
      status: "enabled",
      priority: 1,
      created_at: "2023-04-17 14:30:00",
      updated_at: "2023-04-17 14:30:00",
      updated_by: 3,
      layers: [
        {
          max_times: 2,
          notify_step: 1,
          escalate_window: 20,
          force_escalate: true,
          target: {
            team_ids: [1],
            person_ids: [301],
            schedule_to_role_ids: {},
            by: {
              follow_preference: true,
              critical: ["email", "sms"],
              warning: ["email"],
              info: ["email"]
            },
            webhooks: []
          }
        }
      ],
      aggr_window: 300,
      time_filters: [],
      filters: [
        [
          {
            key: "title",
            oper: "IN",
            vals: ["*网络*", "*Network*"]
          }
        ]
      ]
    }
  ],
  7: []
};

// 模拟团队数据
const mockTeams = [
  { id: 1, name: 'SRE 团队' },
  { id: 2, name: '前端开发团队' },
  { id: 3, name: '后端开发团队' },
  { id: 4, name: '测试团队' }
];

// 模拟人员数据
const mockPersons = [
  { id: 101, name: '张三', team_id: 1 },
  { id: 102, name: '李四', team_id: 1 },
  { id: 103, name: '王五', team_id: 2 },
  { id: 201, name: '赵六', team_id: 3 },
  { id: 202, name: '钱七', team_id: 3 },
  { id: 301, name: '孙八', team_id: 1 }
];

// 模拟排班数据
const mockSchedules = [
  { id: 'schedule-001', name: '工作日白班', team_id: 1 },
  { id: 'schedule-002', name: '工作日夜班', team_id: 1 },
  { id: 'schedule-003', name: '周末值班', team_id: 2 }
];

// 模拟角色数据
const mockRoles = [
  { id: 1, name: '主要负责人' },
  { id: 2, name: '备份负责人' },
  { id: 3, name: '管理员' }
];

// 模拟模板数据
const mockTemplates = [
  { id: 'template-001', name: '标准告警模板' },
  { id: 'template-002', name: '简洁告警模板' },
  { id: 'template-003', name: '详细告警模板' },
  { id: 'template-004', name: '自定义告警模板' }
];

/**
 * 获取空间分派策略列表
 * @param {number|string} spaceId 空间ID
 * @returns {Promise<Object>} 分派策略列表
 */
export const getSpaceDispatchStrategies = (spaceId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(spaceId, 10);
      const strategies = mockDispatchStrategies[numId] || [];
      
      resolve({
        code: 200,
        data: {
          items: strategies
        }
      });
    }, 300);
  });
};

/**
 * 获取分派策略详情
 * @param {number|string} spaceId 空间ID
 * @param {string} ruleId 策略ID
 * @returns {Promise<Object>} 分派策略详情
 */
export const getDispatchStrategyDetail = (spaceId, ruleId) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const numId = parseInt(spaceId, 10);
      const strategies = mockDispatchStrategies[numId] || [];
      const strategy = strategies.find(s => s.rule_id === ruleId);
      
      if (strategy) {
        resolve({
          code: 200,
          data: strategy
        });
      } else {
        reject(new Error('未找到该分派策略'));
      }
    }, 300);
  });
};

/**
 * 创建分派策略
 * @param {number|string} spaceId 空间ID
 * @param {Object} data 策略数据
 * @returns {Promise<Object>} 创建结果
 */
export const createDispatchStrategy = (spaceId, data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const numId = parseInt(spaceId, 10);
      
      // 生成新的策略ID
      const newRuleId = `rule-${Date.now().toString().substr(-6)}`;
      
      // 创建新策略
      const newStrategy = {
        ...data,
        rule_id: newRuleId,
        created_at: new Date().toISOString().replace('T', ' ').substr(0, 19),
        updated_at: new Date().toISOString().replace('T', ' ').substr(0, 19),
        updated_by: 1
      };
      
      // 添加到模拟数据中
      if (!mockDispatchStrategies[numId]) {
        mockDispatchStrategies[numId] = [];
      }
      
      mockDispatchStrategies[numId].push(newStrategy);
      
      resolve({
        code: 200,
        data: newStrategy
      });
    }, 500);
  });
};

/**
 * 更新分派策略
 * @param {number|string} spaceId 空间ID
 * @param {string} ruleId 策略ID
 * @param {Object} data 策略数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateDispatchStrategy = (spaceId, ruleId, data) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const numId = parseInt(spaceId, 10);
      const strategies = mockDispatchStrategies[numId] || [];
      const index = strategies.findIndex(s => s.rule_id === ruleId);
      
      if (index !== -1) {
        // 更新策略
        mockDispatchStrategies[numId][index] = {
          ...strategies[index],
          ...data,
          updated_at: new Date().toISOString().replace('T', ' ').substr(0, 19),
          updated_by: 1
        };
        
        resolve({
          code: 200,
          data: mockDispatchStrategies[numId][index]
        });
      } else {
        reject(new Error('未找到该分派策略'));
      }
    }, 500);
  });
};

/**
 * 启用/禁用分派策略
 * @param {number|string} spaceId 空间ID
 * @param {string} ruleId 策略ID
 * @param {string} status 状态 'enabled' 或 'disabled'
 * @returns {Promise<Object>} 更新结果
 */
export const toggleDispatchStrategyStatus = (spaceId, ruleId, status) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const numId = parseInt(spaceId, 10);
      const strategies = mockDispatchStrategies[numId] || [];
      const index = strategies.findIndex(s => s.rule_id === ruleId);
      
      if (index !== -1) {
        // 更新状态
        mockDispatchStrategies[numId][index].status = status;
        mockDispatchStrategies[numId][index].updated_at = new Date().toISOString().replace('T', ' ').substr(0, 19);
        
        resolve({
          code: 200,
          data: mockDispatchStrategies[numId][index]
        });
      } else {
        reject(new Error('未找到该分派策略'));
      }
    }, 300);
  });
};

/**
 * 获取团队列表
 * @returns {Promise<Object>} 团队列表
 */
export const getTeams = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: mockTeams
      });
    }, 300);
  });
};

/**
 * 获取人员列表
 * @param {number|null} teamId 团队ID，可选
 * @returns {Promise<Object>} 人员列表
 */
export const getPersons = (teamId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let persons = [...mockPersons];
      
      if (teamId) {
        persons = persons.filter(p => p.team_id === parseInt(teamId, 10));
      }
      
      resolve({
        code: 200,
        data: persons
      });
    }, 300);
  });
};

/**
 * 获取排班列表
 * @param {number|null} teamId 团队ID，可选
 * @returns {Promise<Object>} 排班列表
 */
export const getSchedules = (teamId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let schedules = [...mockSchedules];
      
      if (teamId) {
        schedules = schedules.filter(s => s.team_id === parseInt(teamId, 10));
      }
      
      resolve({
        code: 200,
        data: schedules
      });
    }, 300);
  });
};

/**
 * 获取角色列表
 * @returns {Promise<Object>} 角色列表
 */
export const getRoles = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: mockRoles
      });
    }, 300);
  });
};

/**
 * 获取模板列表
 * @returns {Promise<Object>} 模板列表
 */
export const getTemplates = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: mockTemplates
      });
    }, 300);
  });
};
