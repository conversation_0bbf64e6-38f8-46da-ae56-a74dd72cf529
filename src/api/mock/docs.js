import prometheusDoc from '../../views/integrationManagement/docs/prometheus.md?raw';
import aliyunDoc from '../../views/integrationManagement/docs/aliyun.md?raw';

// 模拟文档加载API
export const getIntegrationDoc = async (typeId) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // 根据类型返回对应文档
  const docMap = {
    1: aliyunDoc,   // 阿里云监控 CM 事件
    4: prometheusDoc // Prometheus
  };
  
  return {
    success: true,
    data: docMap[typeId] || '# 暂无文档\n该集成类型暂无详细文档。'
  };
};

// 获取文档目录
export const getDocList = async (typeId) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // 根据类型返回文档目录
  const docList = {
    1: [
      { title: '集成指南', id: 'guide', type: 'markdown' },
      { title: '配置示例', id: 'config', type: 'config' }
    ],
    4: [
      { title: '集成指南', id: 'guide', type: 'markdown' },
      { title: '配置示例', id: 'config', type: 'config' },
      { title: 'API参考', id: 'api', type: 'api' }
    ]
  };
  
  return {
    success: true,
    data: docList[typeId] || []
  };
}; 