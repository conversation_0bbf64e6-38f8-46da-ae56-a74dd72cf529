// 工作空间列表模拟数据
const spaceList = [
  {
    id: 1,
    name: '测试管理服务',
    teamId: 1,
    teamName: 'SRE 团队',
    description: '这是一个测试管理服务工作空间',
    visibility: 'private',
    createdAt: '2023-01-01 12:00:00',
    updatedAt: '2023-01-01 12:00:00',
    issueStats: {
      processing: 5,   // 处理中
      pending: 3,      // 待处理
      completed: 8     // 已处理
    },
    enabled: true,
    statusChangedAt: '2023-01-10 08:30:00'
  },
  {
    id: 2,
    name: 'IT系统',
    teamId: 1,
    teamName: 'SRE 团队',
    description: '用于管理IT系统的工作空间',
    visibility: 'public',
    createdAt: '2023-02-01 12:00:00',
    updatedAt: '2023-02-01 12:00:00',
    issueStats: {
      processing: 2,   // 处理中
      pending: 7,      // 待处理
      completed: 15    // 已处理
    },
    enabled: true,
    statusChangedAt: '2023-02-15 14:20:00'
  },
  {
    id: 3,
    name: '用户登录',
    teamId: 2,
    teamName: '前端开发团队',
    description: '用户登录相关功能的工作空间',
    visibility: 'public',
    createdAt: '2023-03-01 12:00:00',
    updatedAt: '2023-03-01 12:00:00',
    issueStats: {
      processing: 1,   // 处理中
      pending: 4,      // 待处理
      completed: 6     // 已处理
    },
    enabled: false,
    statusChangedAt: '2023-04-05 09:15:00'
  },
  {
    id: 4,
    name: 'SRE',
    teamId: 1,
    teamName: 'SRE 团队',
    description: 'SRE相关工作的工作空间',
    visibility: 'private',
    createdAt: '2023-04-01 12:00:00',
    updatedAt: '2023-04-01 12:00:00',
    issueStats: {
      processing: 0,   // 处理中
      pending: 0,      // 待处理
      completed: 12    // 已处理
    },
    enabled: true,
    statusChangedAt: '2023-04-10 16:45:00'
  },
  // 添加从 SpaceManagementPage.vue 转移过来的模板数据
  {
    id: 5,
    name: '应急响应协作空间',
    teamId: 1,
    teamName: 'SRE 团队',
    description: '用于处理紧急事件和故障的协作空间，包含应急预案和处理流程，便于团队快速响应和解决问题。',
    visibility: 'public',
    createdAt: '2023-05-01 12:00:00',
    updatedAt: '2023-05-01 12:00:00',
    issueStats: {
      processing: 3,   // 处理中
      pending: 5,      // 待处理
      completed: 9     // 已处理
    },
    enabled: true,
    statusChangedAt: '2023-05-20 11:30:00'
  },
  {
    id: 7,
    name: '运维管理协作空间',
    teamId: 1,
    teamName: 'SRE 团队',
    description: '用于日常运维工作的协作空间，包含服务器监控、性能优化和日常维护等任务的管理。',
    visibility: 'public',
    createdAt: '2023-07-01 12:00:00',
    updatedAt: '2023-07-01 12:00:00',
    issueStats: {
      processing: 1,   // 处理中
      pending: 0,      // 待处理
      completed: 22    // 已处理
    },
    enabled: false,
    statusChangedAt: '2023-08-15 10:25:00'
  }
]

// 团队列表模拟数据
const teamList = [
  {
    id: 1,
    name: 'SRE 团队'
  },
  {
    id: 2,
    name: '前端开发团队'
  },
  {
    id: 3,
    name: '后端开发团队'
  }
]

// 直接导出函数，方便组件直接导入使用
export const getSpaceList = () => {
  return Promise.resolve({
    code: 200,
    data: spaceList
  });
};

export const createSpace = (data) => {
  const newSpace = {
    ...data,
    id: spaceList.length + 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    issueStats: {
      processing: 0,
      pending: 0,
      completed: 0
    },
    enabled: true,
    statusChangedAt: new Date().toISOString()
  };
  
  // 根据 teamId 添加 teamName
  const team = teamList.find(team => team.id === parseInt(newSpace.teamId));
  if (team) {
    newSpace.teamName = team.name;
  }
  
  spaceList.push(newSpace);
  
  return Promise.resolve({
    code: 200,
    data: newSpace
  });
};

export const updateSpace = (id, data) => {
  const index = spaceList.findIndex(space => space.id === id);
  if (index !== -1) {
    // 根据 teamId 更新 teamName
    const team = teamList.find(team => team.id === parseInt(data.teamId));
    const teamName = team ? team.name : spaceList[index].teamName;
    
    spaceList[index] = {
      ...spaceList[index],
      ...data,
      teamName,
      updatedAt: new Date().toISOString()
    };
    
    return Promise.resolve({
      code: 200,
      data: spaceList[index]
    });
  }
  
  return Promise.reject(new Error('空间不存在'));
};

export const deleteSpace = (id) => {
  const index = spaceList.findIndex(space => space.id === id);
  if (index !== -1) {
    spaceList.splice(index, 1);
    
    return Promise.resolve({
      code: 200,
      data: 'success'
    });
  }
  
  return Promise.reject(new Error('空间不存在'));
};

export const getTeamList = () => {
  return Promise.resolve({
    code: 200,
    data: teamList
  });
};

// 添加切换空间启用状态的函数
export const toggleSpaceStatus = (id) => {
  const index = spaceList.findIndex(space => space.id === id);
  if (index !== -1) {
    spaceList[index].enabled = !spaceList[index].enabled;
    spaceList[index].statusChangedAt = new Date().toISOString();
    
    return Promise.resolve({
      code: 200,
      data: spaceList[index]
    });
  }
  
  return Promise.reject(new Error('空间不存在'));
};

// 保留原始导出格式，兼容其他可能的用法
export default [
  // 获取工作空间列表
  {
    url: '/event-ui/api/spaces',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: spaceList
      }
    }
  },
  
  // 创建工作空间
  {
    url: '/event-ui/api/spaces',
    method: 'post',
    response: ({ body }) => {
      return {
        code: 200,
        data: {
          ...body,
          id: spaceList.length + 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
    }
  },
  
  // 更新工作空间
  {
    url: '/event-ui/api/spaces/:id',
    method: 'put',
    response: ({ body }) => {
      return {
        code: 200,
        data: {
          ...body,
          updatedAt: new Date().toISOString()
        }
      }
    }
  },
  
  // 删除工作空间
  {
    url: '/event-ui/api/spaces/:id',
    method: 'delete',
    response: () => {
      return {
        code: 200,
        data: 'success'
      }
    }
  },
  
  // 获取团队列表
  {
    url: '/event-ui/api/teams',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: teamList
      }
    }
  }
] 