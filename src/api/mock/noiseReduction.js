// 模拟降噪配置相关的API数据

// 告警聚合配置
export const getAlarmAggregationConfig = (spaceId) => {
  return {
    code: 0,
    data: {
      space_id: spaceId,
      aggregation_type: 2, // 2-规则聚合，3-不聚合
      aggregation_window: {
        enabled: false,
        minutes: 60
      },
      storm_reminder: {
        enabled: false,
        threshold: 100
      },
      strict_aggregation: {
        enabled: false
      },
      dimensions: ['labels.check', 'labels.host']
    }
  }
}

// 更新告警聚合配置
export const updateAlarmAggregationConfig = (params) => {
  return {
    code: 0,
    data: {
      success: true
    }
  }
}

// 故障收敛配置
export const getAlarmSuppressionConfig = (spaceId) => {
  return {
    code: 0,
    data: {
      space_id: spaceId,
      enabled: true,
      time_window: 60, // 分钟
      occurrence_count: 4,
      suppression_time: 120 // 分钟
    }
  }
}

// 更新故障收敛配置
export const updateAlarmSuppressionConfig = (params) => {
  return {
    code: 0,
    data: {
      success: true
    }
  }
}

// 静默策略列表
export const getSilentStrategies = (spaceId) => {
  return {
    code: 0,
    data: {
      space_id: spaceId,
      strategies: [
        {
          id: 1,
          name: '测试环境告警静默',
          description: '测试环境的所有告警都不会触发通知',
          conditions: ['environment=test', 'severity!=critical']
        },
        {
          id: 2,
          name: '夜间低优先级告警静默',
          description: '夜间（22:00-08:00）的低优先级告警不会触发通知',
          conditions: ['time_range=22:00-08:00', 'priority=low']
        }
      ]
    }
  }
}

// 创建静默策略
export const createSilentStrategy = (params) => {
  return {
    code: 0,
    data: {
      id: Math.floor(Math.random() * 1000) + 3,
      ...params
    }
  }
}

// 更新静默策略
export const updateSilentStrategy = (params) => {
  return {
    code: 0,
    data: {
      success: true
    }
  }
}

// 删除静默策略
export const deleteSilentStrategy = (id) => {
  return {
    code: 0,
    data: {
      success: true
    }
  }
}
