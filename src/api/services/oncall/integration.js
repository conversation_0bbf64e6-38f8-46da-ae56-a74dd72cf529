/**
 * 集成管理API接口
 * 基于 zero_duty_api 文档的接口实现
 * 使用OnCall服务 (端口2002)
 */
import { oncallRequest } from '@/utils/multiRequest'

// ==================== 集成管理接口 ====================

/**
 * 获取集成类型列表
 * @returns {Promise<Array>} 集成类型列表
 */
export function getIntegrationTypes() {
  return oncallRequest.get('/api/v1/integration-types')
}

/**
 * 获取集成列表
 * @param {Object} data - 请求体参数
 * @param {string} data.name - 集成名称
 * @param {string} data.type - 集成类型
 * @param {number} data.page - 页码，默认1
 * @param {number} data.pageSize - 每页大小，默认20
 * @returns {Promise<Object>} 集成列表响应
 */
export function getIntegrationList(data = {}) {
  const requestBody = {
    page: 1,
    pageSize: 20,
    ...data
  }

  // 使用POST请求，将参数作为请求体发送
  return oncallRequest.post('/api/v1/integrations/list', requestBody)
}

/**
 * 创建集成
 * @param {Object} data - 集成数据
 * @param {string} data.id - 集成ID
 * @param {string} data.name - 集成名称
 * @param {string} data.kind - 集成类型 (public/private)
 * @param {string} data.type - 集成类型
 * @param {boolean} data.status - 集成状态
 * @returns {Promise<Object>} 创建结果
 */
export function createIntegration(data) {
  return oncallRequest.post('/api/v1/integrations', data)
}

/**
 * 获取集成详情
 * @param {string} id - 集成ID
 * @returns {Promise<Object>} 集成详情
 */
export function getIntegrationDetail(id) {
  return oncallRequest.get(`/api/v1/integrations/${id}`)
}

/**
 * 更新集成
 * @param {string} id - 集成ID
 * @param {Object} data - 更新数据
 * @param {string} data.id - 集成ID
 * @param {string} data.name - 集成名称
 * @param {string} data.type - 集成类型
 * @param {string} data.kind - 集成类型 (public/private)
 * @param {boolean} data.enabled - 是否启用
 * @returns {Promise<Object>} 更新结果
 */
export function updateIntegration(id, data) {
  return oncallRequest.put(`/api/v1/integrations/${id}`, data)
}

/**
 * 删除集成
 * @param {Array<string>} ids - 集成ID列表
 * @returns {Promise<Object>} 删除结果
 */
export function deleteIntegrations(ids) {
  return oncallRequest.delete('/api/v1/integrations', {
    data: { ids }
  })
}

/**
 * 获取集成密钥
 * @returns {Promise<Object>} 集成密钥
 */
export function getIntegrationKey() {
  return oncallRequest.get('/api/v1/integrations/key')
}

// ==================== 告警路由接口 ====================

/**
 * 获取告警路由列表
 * @param {Object} params - 查询参数
 * @param {string} params.integrationId - 集成ID
 * @param {number} params.status - 状态
 * @param {number} params.page - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认20
 * @returns {Promise<Object>} 告警路由列表
 */
export function getAlertRouteList(params = {}) {
  const requestBody = {
    page: 1,
    pageSize: 20,
    ...params
  }

  // 根据API文档，告警路由列表使用POST请求
  return oncallRequest.post('/api/v1/alert-routes/list', requestBody)
}

/**
 * 创建告警路由
 * @param {Object} data - 告警路由数据
 * @param {string} data.name - 路由名称
 * @param {string} data.integrationId - 集成ID
 * @param {Array} data.conditions - 条件列表
 * @param {Array} data.actions - 动作列表
 * @param {number} data.priority - 优先级
 * @param {number} data.status - 状态
 * @returns {Promise<Object>} 创建结果
 */
export function createAlertRoute(data) {
  return oncallRequest.post('/api/v1/alert-routes', data)
}

/**
 * 获取告警路由详情
 * @param {string} id - 告警路由ID
 * @returns {Promise<Object>} 告警路由详情
 */
export function getAlertRouteDetail(id) {
  return oncallRequest.get(`/api/v1/alert-routes/${id}`)
}

/**
 * 更新告警路由
 * @param {string} id - 告警路由ID
 * @param {Object} data - 更新数据
 * @param {string} data.name - 路由名称
 * @param {string} data.integrationId - 集成ID
 * @param {Array} data.conditions - 条件列表
 * @param {Array} data.actions - 动作列表
 * @param {number} data.priority - 优先级
 * @param {number} data.status - 状态
 * @returns {Promise<Object>} 更新结果
 */
export function updateAlertRoute(id, data) {
  return oncallRequest.put(`/api/v1/alert-routes/${id}`, data)
}

/**
 * 删除告警路由
 * @param {Array<string>} ids - 告警路由ID列表
 * @returns {Promise<Object>} 删除结果
 */
export function deleteAlertRoutes(ids) {
  return oncallRequest.delete('/api/v1/alert-routes', {
    data: { ids }
  })
}

// 默认导出所有API函数
export default {
  // 集成管理
  getIntegrationTypes,
  getIntegrationList,
  createIntegration,
  getIntegrationDetail,
  updateIntegration,
  deleteIntegrations,
  getIntegrationKey,

  // 告警路由
  getAlertRouteList,
  createAlertRoute,
  getAlertRouteDetail,
  updateAlertRoute,
  deleteAlertRoutes
}
