/**
 * OnCall服务 - 空间管理API接口
 * 空间管理相关的所有API调用
 * 端口: 2002
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
import { oncallRequest } from '@/utils/multiRequest'

// ==================== 空间管理接口 ====================

/**
 * 获取空间列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.keyword - 搜索关键词
 * @param {number} params.teamId - 团队ID
 * @param {string} params.visibility - 可见性 (public/private)
 * @param {boolean} params.enabled - 是否启用
 * @returns {Promise<Object>} 空间列表
 */
export function getSpaceList(params) {
  return oncallRequest({
    url: '/api/v1/spaces/list',
    method: 'post',
    data: params
  })
}

/**
 * 获取空间详情
 * @param {string|number} spaceId - 空间ID
 * @returns {Promise<Object>} 空间详情
 */
export function getSpaceDetail(spaceId) {
  return oncallRequest({
    url: `/api/v1/spaces/${spaceId}`,
    method: 'get'
  })
}

/**
 * 创建空间
 * @param {Object} data - 空间数据
 * @param {string} data.name - 空间名称
 * @param {number} data.teamId - 团队ID
 * @param {string} data.description - 空间描述
 * @param {string} data.visibility - 可见性 (public/private)
 * @param {boolean} data.enabled - 是否启用
 * @returns {Promise<Object>} 创建结果
 */
export function createSpace(data) {
  return oncallRequest({
    url: '/api/v1/spaces',
    method: 'post',
    data
  })
}

/**
 * 更新空间信息
 * @param {string|number} spaceId - 空间ID
 * @param {Object} data - 更新数据
 * @param {string} data.name - 空间名称
 * @param {string} data.description - 空间描述
 * @param {string} data.visibility - 可见性 (public/private)
 * @param {boolean} data.enabled - 是否启用
 * @returns {Promise<Object>} 更新结果
 */
export function updateSpace(spaceId, data) {
  return oncallRequest({
    url: `/api/v1/spaces/${spaceId}`,
    method: 'put',
    data
  })
}

/**
 * 删除空间
 * @param {string|number} spaceId - 空间ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteSpace(spaceId) {
  return oncallRequest({
    url: `/api/v1/spaces/${spaceId}`,
    method: 'delete'
  })
}

/**
 * 启用/禁用空间
 * @param {string|number} spaceId - 空间ID
 * @param {boolean} enabled - 是否启用
 * @returns {Promise<Object>} 操作结果
 */
export function toggleSpaceStatus(spaceId, enabled) {
  return oncallRequest({
    url: `/api/v1/spaces/${spaceId}`,
    method: 'put',
    data: { enabled }
  })
}


// ==================== 错误处理示例 ====================

/**
 * 带错误处理的空间数据获取示例
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} API响应
 */
export async function getSpaceDataWithErrorHandling(params) {
  try {
    const response = await getSpaceList(params)
    console.log('✅ 空间数据获取成功:', response)
    return response
  } catch (error) {
    console.error('❌ 空间数据获取失败:', error.message)
    throw error
  }
}