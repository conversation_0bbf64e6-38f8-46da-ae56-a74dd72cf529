/**
 * Message服务API接口
 * 消息通知相关的所有API调用
 * 端口: 2003
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
import { messageRequest } from '@/utils/multiRequest'

// ==================== 消息发送接口 ====================

/**
 * 发送消息
 * @param {Object} data - 消息数据
 * @param {string} data.type - 消息类型 (email, sms, webhook, etc.)
 * @param {Array} data.recipients - 接收者列表
 * @param {string} data.subject - 消息主题
 * @param {string} data.content - 消息内容
 * @param {Object} data.metadata - 元数据
 * @returns {Promise<Object>} 发送结果
 */
export function sendMessage(data) {
  return messageRequest({
    url: '/api/v1/message/send',
    method: 'post',
    data
  })
}

/**
 * 批量发送消息
 * @param {Array} messages - 消息列表
 * @returns {Promise<Object>} 批量发送结果
 */
export function sendBatchMessages(messages) {
  return messageRequest({
    url: '/api/v1/message/send/batch',
    method: 'post',
    data: { messages }
  })
}

/**
 * 发送告警通知
 * @param {Object} data - 告警数据
 * @param {string} data.alertId - 告警ID
 * @param {string} data.spaceId - 工作空间ID
 * @param {Array} data.channels - 通知渠道
 * @param {Object} data.alert - 告警详情
 * @returns {Promise<Object>} 发送结果
 */
export function sendAlertNotification(data) {
  return messageRequest({
    url: '/api/v1/message/alert/notify',
    method: 'post',
    data
  })
}

// ==================== 消息模板接口 ====================

/**
 * 获取消息模板列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.type - 模板类型
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise<Object>} 模板列表
 */
export function getMessageTemplates(params) {
  return messageRequest({
    url: '/api/v1/message/templates',
    method: 'post',
    data: params
  })
}

/**
 * 获取消息模板详情
 * @param {string} templateId - 模板ID
 * @returns {Promise<Object>} 模板详情
 */
export function getMessageTemplate(templateId) {
  return messageRequest({
    url: `/api/v1/message/templates/${templateId}`,
    method: 'get'
  })
}

/**
 * 创建消息模板
 * @param {Object} data - 模板数据
 * @param {string} data.name - 模板名称
 * @param {string} data.type - 模板类型
 * @param {string} data.subject - 主题模板
 * @param {string} data.content - 内容模板
 * @param {Object} data.variables - 变量定义
 * @returns {Promise<Object>} 创建结果
 */
export function createMessageTemplate(data) {
  return messageRequest({
    url: '/api/v1/message/templates',
    method: 'post',
    data
  })
}

/**
 * 更新消息模板
 * @param {string} templateId - 模板ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateMessageTemplate(templateId, data) {
  return messageRequest({
    url: `/api/v1/message/templates/${templateId}`,
    method: 'put',
    data
  })
}

/**
 * 删除消息模板
 * @param {string} templateId - 模板ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteMessageTemplate(templateId) {
  return messageRequest({
    url: `/api/v1/message/templates/${templateId}`,
    method: 'delete'
  })
}

// ==================== 通知渠道接口 ====================

/**
 * 获取通知渠道列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 渠道列表
 */
export function getNotificationChannels(params) {
  return messageRequest({
    url: '/api/v1/message/channels',
    method: 'post',
    data: params
  })
}

/**
 * 创建通知渠道
 * @param {Object} data - 渠道数据
 * @param {string} data.name - 渠道名称
 * @param {string} data.type - 渠道类型 (email, webhook, slack, etc.)
 * @param {Object} data.config - 渠道配置
 * @returns {Promise<Object>} 创建结果
 */
export function createNotificationChannel(data) {
  return messageRequest({
    url: '/api/v1/message/channels',
    method: 'post',
    data
  })
}

/**
 * 更新通知渠道
 * @param {string} channelId - 渠道ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateNotificationChannel(channelId, data) {
  return messageRequest({
    url: `/api/v1/message/channels/${channelId}`,
    method: 'put',
    data
  })
}

/**
 * 删除通知渠道
 * @param {string} channelId - 渠道ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteNotificationChannel(channelId) {
  return messageRequest({
    url: `/api/v1/message/channels/${channelId}`,
    method: 'delete'
  })
}

/**
 * 测试通知渠道
 * @param {string} channelId - 渠道ID
 * @param {Object} data - 测试数据
 * @returns {Promise<Object>} 测试结果
 */
export function testNotificationChannel(channelId, data) {
  return messageRequest({
    url: `/api/v1/message/channels/${channelId}/test`,
    method: 'post',
    data
  })
}

// ==================== 消息历史接口 ====================

/**
 * 获取消息发送历史
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @param {string} params.type - 消息类型
 * @param {string} params.status - 发送状态
 * @returns {Promise<Object>} 消息历史
 */
export function getMessageHistory(params) {
  return messageRequest({
    url: '/api/v1/message/history',
    method: 'post',
    data: params
  })
}

/**
 * 获取消息详情
 * @param {string} messageId - 消息ID
 * @returns {Promise<Object>} 消息详情
 */
export function getMessageDetail(messageId) {
  return messageRequest({
    url: `/api/v1/message/history/${messageId}`,
    method: 'get'
  })
}

/**
 * 重发消息
 * @param {string} messageId - 消息ID
 * @returns {Promise<Object>} 重发结果
 */
export function resendMessage(messageId) {
  return messageRequest({
    url: `/api/v1/message/history/${messageId}/resend`,
    method: 'post'
  })
}

// ==================== 消息统计接口 ====================

/**
 * 获取消息发送统计
 * @param {Object} params - 查询参数
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @param {string} params.groupBy - 分组方式 (day, hour, channel, type)
 * @returns {Promise<Object>} 统计数据
 */
export function getMessageStatistics(params) {
  return messageRequest({
    url: '/api/v1/message/statistics',
    method: 'post',
    data: params
  })
}

/**
 * 获取渠道发送统计
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 渠道统计
 */
export function getChannelStatistics(params) {
  return messageRequest({
    url: '/api/v1/message/statistics/channels',
    method: 'post',
    data: params
  })
}

// ==================== 消息订阅接口 ====================

/**
 * 获取用户订阅设置
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 订阅设置
 */
export function getUserSubscriptions(userId) {
  return messageRequest({
    url: `/api/v1/message/subscriptions/${userId}`,
    method: 'get'
  })
}

/**
 * 更新用户订阅设置
 * @param {string} userId - 用户ID
 * @param {Object} data - 订阅设置
 * @returns {Promise<Object>} 更新结果
 */
export function updateUserSubscriptions(userId, data) {
  return messageRequest({
    url: `/api/v1/message/subscriptions/${userId}`,
    method: 'put',
    data
  })
}

// ==================== 错误处理示例 ====================

/**
 * 带错误处理的API调用示例
 * @param {Object} data - 消息数据
 * @returns {Promise<Object>} API响应
 */
export async function sendMessageWithErrorHandling(data) {
  try {
    const response = await sendMessage(data)
    console.log('✅ Message发送成功:', response)
    return response
  } catch (error) {
    console.error('❌ Message发送失败:', error.message)
    
    // 可以在这里添加重试逻辑
    if (error.message.includes('网络')) {
      console.log('🔄 网络错误，尝试重发...')
      // 实现重试逻辑
    }
    
    throw error
  }
}
