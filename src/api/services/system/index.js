/**
 * System服务API接口
 * 系统管理相关的所有API调用
 * 端口: 2001
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
import { systemRequest } from '@/utils/multiRequest'

// ==================== 认证相关接口 ====================

// 重新导出认证API
export * from './auth'

// ==================== 团队管理接口 ====================

/**
 * 获取团队列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise<Object>} 团队列表
 */
export function getTeamList(params) {
  return systemRequest({
    url: '/team/list',
    method: 'post',
    data: params
  })
}

/**
 * 获取团队详情
 * @param {string|number} teamId - 团队ID
 * @returns {Promise<Object>} 团队详情
 */
export function getTeamDetail(teamId) {
  return systemRequest({
    url: `/team/${teamId}`,
    method: 'get'
  })
}

/**
 * 创建团队
 * @param {Object} data - 团队数据
 * @param {string} data.name - 团队名称
 * @param {string} data.trans - 团队显示名称
 * @param {Array<string>} data.userIds - 用户ID列表
 * @param {string} data.remark - 备注
 * @returns {Promise<Object>} 创建结果
 */
export function createTeam(data) {
  return systemRequest({
    url: '/team',
    method: 'post',
    data
  })
}

/**
 * 更新团队信息
 * @param {string|number} teamId - 团队ID
 * @param {Object} data - 更新数据
 * @param {string} data.name - 团队名称
 * @param {string} data.trans - 团队显示名称
 * @param {Array<string>} data.userIds - 用户ID列表
 * @param {string} data.remark - 备注
 * @returns {Promise<Object>} 更新结果
 */
export function updateTeam(teamId, data) {
  return systemRequest({
    url: `/team/${teamId}`,
    method: 'put',
    data
  })
}

/**
 * 删除团队
 * @param {string|number} teamId - 团队ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteTeam(teamId) {
  return systemRequest({
    url: `/team/${teamId}`,
    method: 'delete'
  })
}

// ==================== 错误处理示例 ====================

/**
 * 带错误处理的API调用示例
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} API响应
 */
export async function getSystemDataWithErrorHandling(params) {
  try {
    const response = await getTeamList(params)
    console.log('✅ System数据获取成功:', response)
    return response
  } catch (error) {
    console.error('❌ System数据获取失败:', error.message)
    throw error
  }
}
