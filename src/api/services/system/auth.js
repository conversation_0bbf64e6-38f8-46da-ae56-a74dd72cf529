/**
 * 认证相关API
 * 用户登录、token管理等
 */
import { systemRequest } from '@/utils/multiRequest'

/**
 * 通过Cookie登录获取Token
 * @param {string} ommsToken - 从cookie中获取的jwt_token
 * @returns {Promise<Object>} 登录响应
 */
export function cookieLogin(ommsToken) {
  return systemRequest({
    url: '/user/cookielogin',
    method: 'post',
    data: {
      ommsToken: ommsToken
    }
  })
}

/**
 * 刷新Token
 * @param {string} refreshToken - 刷新token
 * @returns {Promise<Object>} 新的token信息
 */
export function refreshToken(refreshToken) {
  return systemRequest({
    url: '/user/refresh',
    method: 'post',
    data: {
      refresh_token: refreshToken
    }
  })
}

/**
 * 退出登录
 * @returns {Promise<Object>} 退出响应
 */
export function logout() {
  return systemRequest({
    url: '/user/logout',
    method: 'post'
  })
}

/**
 * 获取用户信息
 * @returns {Promise<Object>} 用户信息
 */
export function getUserInfo() {
  return systemRequest({
    url: '/user/info',
    method: 'get'
  })
}

/**
 * 验证Token有效性
 * @returns {Promise<Object>} 验证结果
 */
export function validateToken() {
  return systemRequest({
    url: '/user/validate',
    method: 'get'
  })
}

export default {
  cookieLogin,
  refreshToken,
  logout,
  getUserInfo,
  validateToken
}
