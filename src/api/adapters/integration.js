/**
 * 集成管理API - 混合使用真实API和Mock数据
 */
import * as realApi from '../services/oncall/integration'
import * as mockApi from '../mock/integration'

// 集成类型继续使用Mock数据，其他功能使用真实API
export const getIntegrationTypes = mockApi.getIntegrationTypes
export const getIntegrationById = realApi.getIntegrationDetail
export const createIntegration = realApi.createIntegration
export const updateIntegration = realApi.updateIntegration
export const deleteIntegrations = realApi.deleteIntegrations
export const getIntegrationKey = realApi.getIntegrationKey

// 适配getIntegrations函数，处理参数映射和数据格式转换
export function getIntegrations(params) {
  // 将页面参数映射到API参数，作为请求体发送
  const requestBody = {
    page: params.page || 1,
    pageSize: params.pageSize || 100,
    name: params.nameKeyword,
    type: params.typeKeyword
  }

  return realApi.getIntegrationList(requestBody).then(response => {
    // 处理API响应数据格式
    if (response && response.data) {
      // API返回格式: { code: 200, msg: "...", data: { total: 1, data: [...] } }
      const apiData = response.data

      // 转换数据格式以匹配页面期望的格式
      const adaptedData = apiData.data.map(item => {
        console.log('🔍 原始集成数据:', item); // 调试信息
        return {
          id: item.id, // 确保使用API返回的原始ID
          name: item.name,
          typeId: getTypeIdByName(item.type), // 转换type到typeId
          type: item.type,
          status: item.status ? 'enabled' : 'disabled', // 转换boolean到字符串
          lastEventTime: item.lastEventTime || '暂未收到告警事件',
          webhook: item.webhook || '',
          statusLoading: false // 添加状态加载标识
        }
      })

      return {
        data: adaptedData,
        total: apiData.total
      }
    }

    // 如果数据格式不符合预期，返回空数据
    return {
      data: [],
      total: 0
    }
  })
}

// 根据类型名称获取类型ID（与mock数据保持一致）
function getTypeIdByName(typeName) {
  const typeMap = {
    '阿里云监控 CM 事件': 1,
    'Grafana': 2,
    'Zabbix': 3,
    'Prometheus': 4,
    'promethues-zabbix': 4, // API返回的类型名
    '邮件 Email': 5,
    '标准告警事件': 7
  }
  return typeMap[typeName] || 4
}

// 切换集成状态的特殊处理
export function toggleIntegrationStatus(id, status, currentRowData = null) {
  console.log('🔄 切换集成状态 - ID:', id, 'Status:', status); // 调试信息
  console.log('🔄 当前行数据:', currentRowData); // 调试信息

  // 如果有当前行数据，直接使用，避免调用可能返回mock数据的详情接口
  if (currentRowData) {
    const updateData = {
      id: id, // 使用传入的ID
      name: currentRowData.name,
      kind: 'public', // 根据API文档，默认为public
      type: currentRowData.type,
      status: status === 'enabled'
    }

    console.log('📤 直接更新数据:', updateData); // 调试信息
    return realApi.updateIntegration(id, updateData)
  }

  // 如果没有行数据，则调用详情接口（保留原逻辑作为备用）
  return realApi.getIntegrationDetail(id).then(response => {
    console.log('📋 获取集成详情响应:', response); // 调试信息

    // 处理API响应数据结构
    const current = response.data || response;
    console.log('📋 当前集成数据:', current); // 调试信息

    const updateData = {
      id: id, // 确保使用正确的ID
      name: current.name,
      kind: current.kind || 'public',
      type: current.type,
      status: status === 'enabled'
    }

    console.log('📤 更新数据:', updateData); // 调试信息
    return realApi.updateIntegration(id, updateData)
  })
}

// 默认导出
export default {
  getIntegrationTypes,
  getIntegrations,
  getIntegrationById,
  createIntegration,
  updateIntegration,
  toggleIntegrationStatus,
  deleteIntegrations,
  getIntegrationKey
}
