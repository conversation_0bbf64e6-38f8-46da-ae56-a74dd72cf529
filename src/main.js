import { createApp } from 'vue'
import router, { asyncRoutes, constantRoutes } from "./router";
import { setupStore } from './stores'
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import {install} from '@icon-park/vue-next/es/all';
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import Emitter from 'tiny-emitter'
import App from './App.vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import stateSave from "./directive/stateSave";
import authDirective from "./directive/authDirective";
// 导入Markdown相关库
import { marked } from 'marked';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';
// 导入Monaco Editor插件
import MonacoPlugin from './plugins/monaco';
import microAppActions from "./utils/microAppState.js";
import { useAuthStore } from './stores/modules/authStore';
// 导入开发环境认证工具
import { setupDevAuth } from './utils/devAuth';

// 在开发环境中设置测试用jwt_token
if (import.meta.env.MODE === 'development') {
  setupDevAuth();
}

// 配置marked
marked.setOptions({
  highlight: function(code, lang) {
    const language = hljs.getLanguage(lang) ? lang : 'plaintext';
    return hljs.highlight(code, { language }).value;
  },
  langPrefix: 'hljs language-'
});

let app = null;
let routerForQiankun = null;
let history = null;
function bootstrapVue3(container) {
  // 进行创建，挂载app的一系列操作，这里挂载的时候可以利用传入的container
  app = createApp(App);
  const emitter = new Emitter()
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  setupStore(app)
  app.use(router)
  app.use(ElementPlus)
  app.use(stateSave);
  app.use(authDirective);
  app.use(MonacoPlugin); // 使用Monaco Editor插件
  app.config.globalProperties.bus = emitter;
  install(app, 'tcicon')
  app.mount(container);

  // 初始化认证状态
  const authStore = useAuthStore();
  authStore.restoreAuthFromStorage();
}

const initMicroApp = (container) => {
  history = createWebHashHistory(window.__MICRO_APP_BASE_ROUTE__ ? `OMSMain/#${window.__MICRO_APP_BASE_ROUTE__}` : '/')
  routerForQiankun = createRouter({
    // 运行在主应用中时，添加路由命名空间 /vue
    history,
    routes: [...asyncRoutes, ...constantRoutes],
  });
  app = createApp(App);
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  setupStore(app)
  app.use(routerForQiankun);
  app.use(ElementPlus)
  app.use(stateSave);
  app.use(authDirective);
   app.use(MonacoPlugin); // 使用Monaco Editor插件
  app.config.globalProperties.bus = Emitter;
  microAppActions.setActions(window.microApp)
  install(app, 'tcicon')
  app.mount(container);

  // 初始化认证状态
  const authStore = useAuthStore();
  authStore.restoreAuthFromStorage();
}


window.unmount = () => {
  app.unmount();
  app = null;
}


window.__MICRO_APP_ENVIRONMENT__ ? initMicroApp('#app') : bootstrapVue3('#app');
