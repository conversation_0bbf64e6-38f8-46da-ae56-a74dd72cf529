/**
 * 认证路由守卫
 * 在进入路由前检查用户认证状态
 */
import { useAuthStore } from '@/stores/modules/authStore'

/**
 * 不需要认证的路由白名单
 */
const AUTH_WHITELIST = [
  '/404',
  '/403',
  '/500',
  '/remind'
]

/**
 * 检查路由是否在白名单中
 * @param {string} path - 路由路径
 * @returns {boolean} 是否在白名单中
 */
function isInWhitelist(path) {
  return AUTH_WHITELIST.some(whitePath => {
    if (whitePath === path) return true
    // 支持通配符匹配
    if (whitePath.endsWith('*')) {
      const prefix = whitePath.slice(0, -1)
      return path.startsWith(prefix)
    }
    return false
  })
}

/**
 * 认证守卫函数
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 路由跳转函数
 */
export async function authGuard(to, from, next) {
  const authStore = useAuthStore()

  console.log('🛡️ 路由守卫检查:', to.path)

  // 检查是否在白名单中
  if (isInWhitelist(to.path)) {
    console.log('✅ 路由在白名单中，直接通过')
    next()
    return
  }

  try {
    // 确保有有效的token
    const tokenResult = await authStore.ensureToken()

    if (tokenResult.success) {
      console.log('✅ Token有效，允许访问')
      next()
    } else {
      console.error('❌ 无法获取有效token:', tokenResult.error)
      // 如果无法获取token，仍然允许访问，但会在请求时处理认证失败
      next()
    }
  } catch (error) {
    console.error('❌ 认证守卫执行失败:', error.message)
    // 即使认证失败，也允许访问，让具体的API请求处理认证问题
    next()
  }
}



/**
 * 权限检查守卫（可选）
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 路由跳转函数
 */
export function permissionGuard(to, from, next) {
  const authStore = useAuthStore()
  
  // 检查路由是否需要特定权限
  const requiredRole = to.meta?.requireRole
  const requiredPermission = to.meta?.requirePermission
  
  if (requiredRole && authStore.userRole !== requiredRole) {
    console.log('❌ 用户角色不足，无法访问')
    next('/403')
    return
  }
  
  if (requiredPermission && !authStore.userInfo?.permissions?.includes(requiredPermission)) {
    console.log('❌ 用户权限不足，无法访问')
    next('/403')
    return
  }
  
  next()
}

export default authGuard
