import { defineStore } from 'pinia'
import { checkResponseCode } from "../../utils/utilTool";
import * as TechA<PERSON> from "../../api/techApi";
export const useTechStore = defineStore('techStore', {
  state:() => ({
    userInfo: {},
    authoritylist: [], // 项目相关权限
    techAuthoritylist: [], // 项目不相关权限
    projectEnvList:[], // 项目&环境列表
    nowProjectId:null,
    nowProjectAppName:"",
    nowProjectAppShortName:"", // 项目简称
    nowAppid:0
  }),
  getter: {

  },
  actions:{
    moduleGetUserinfo(){
      return new Promise((resolve, reject) => {
        let data= {
          "code": 0,
          "message": "",
          "data": {
              "userId": "luyongjie",
              "name": "mock-测试用户",
              "head": "https://mnoa.kingsoft.com/oa/head/current/202109/a61d9ebce7bc4d40a0a2b6747c69eedf.jpg"
          }
        }
        resolve(data.data);
        return
        // TechApi.getUserInfo()
        //   .then((response) => {
        //     let _data = checkResponseCode(response);
        //     this.userInfo = _data
        //     resolve(_data);
        //   })
        //   .catch((error) => {
        //     reject(error);
        //   });
      });
    },
    moduleGetPrivileges(){
      return new Promise((resolve, reject) => {
        //mock
        let data= {
          "code": 0,
          "message": "",
          "data": [
            {
              "appId": -99,
              "privileges": [
                {
                  "sysCode": "ARM",
                  "appId": 200000100,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "美术素材/*",
                  "res_name": "下载美术素材",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "ARM",
                  "appId": 200000100,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "美术插件/*",
                  "res_name": "下载美术插件",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "ARM",
                  "appId": 200000100,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "音频插件/*",
                  "res_name": "音频插件",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/mange/financeimport",
                  "res_name": "账单处理(前端)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "finance_admin",
                  "res_name": "所有项目数据(后端)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/mange/audit_logs",
                  "res_name": "审计日志",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/mange/finance_share",
                  "res_name": "财务处理(分摊配置)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/gop/serverlist",
                  "res_name": "机甲serverlist_cdn",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                }
              ],
              "shortName": "",
              "gameEngine": "",
              "engineVersion": "",
              "releaseRegion": "",
              "screenDirection": "",
              "gameType": "",
              "icon": "",
              "shareDepart": "",
              "sapId": "",
              "isGame": 0,
              "process": 0,
              "bigDataState": "",
              "processName": "",
              "emailGroup": "",
              "appStandbyOwner": "",
              "handlers": ""
            },
            {
              "appId": 200001100,
              "appName": "封神榜虚拟化资源池",
              "privileges": [
                {
                  "sysCode": "Tech",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "ProjectAdministration",
                  "res_name": "项目管理员",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "BIGLOG",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "logsearch",
                  "res_name": "日志查看",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": true
                },
                {
                  "sysCode": "tcbase",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "DocReplyAI",
                  "res_name": "智能问答",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "tcbase",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "DocTranslateAI",
                  "res_name": "智能翻译",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "BIGLOG",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "log3",
                  "res_name": "日志查询3.0",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": true
                },
                {
                  "sysCode": "BIGLOG",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "gpm_support",
                  "res_name": "gpm技术支持",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": true
                },
                {
                  "sysCode": "BIGLOG",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "jx3ClientReportManage",
                  "res_name": "缘起客户端日志上报配置",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": true
                }
              ],
              "shortName": "",
              "gameEngine": "",
              "engineVersion": "",
              "releaseRegion": "",
              "screenDirection": "",
              "gameType": "",
              "icon": "",
              "shareDepart": "",
              "sapId": "008",
              "isGame": 1,
              "appOwner": "zhangbo",
              "process": 0,
              "bigDataState": "",
              "processName": "",
              "emailGroup": "",
              "appStandbyOwner": "",
              "handlers": "[]"
            },
            {
              "appId": 200001088,
              "appName": "全球发行Rising Sun",
              "privileges": [
                {
                  "sysCode": "Tech",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "ProjectAdministration",
                  "res_name": "项目管理员",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "BIGLOG",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "logsearch",
                  "res_name": "日志查看",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": true
                },
                {
                  "sysCode": "tcbase",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "DocReplyAI",
                  "res_name": "智能问答",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "tcbase",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "DocTranslateAI",
                  "res_name": "智能翻译",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "BIGLOG",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "log3",
                  "res_name": "日志查询3.0",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": true
                },
                {
                  "sysCode": "BIGLOG",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "gpm_support",
                  "res_name": "gpm技术支持",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": true
                },
                {
                  "sysCode": "BIGLOG",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "jx3ClientReportManage",
                  "res_name": "缘起客户端日志上报配置",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": true
                }
              ],
              "shortName": "",
              "gameEngine": "NATIVE",
              "engineVersion": "",
              "releaseRegion": "DOMESTIC",
              "screenDirection": "HORIZONTAL",
              "gameType": "RPG",
              "icon": "",
              "shareDepart": "",
              "sapId": "248",
              "isGame": 1,
              "appOwner": "shenghui",
              "process": 0,
              "bigDataState": "",
              "processName": "",
              "emailGroup": "",
              "appStandbyOwner": "",
              "handlers": "[{\"handler\": [{\"id\": \"1\", \"duty\": \"GM、SDK接入对接，如接入技术中心的技术架构\", \"user_id\": \"\", \"role_name\": \"技术框架（服务端）\", \"user_name\": \"\", \"model_name\": \"游戏技术\"}, {\"id\": \"2\", \"duty\": \"\", \"user_id\": \"\", \"role_name\": \"技术框架（客户端）\", \"user_name\": \"\", \"model_name\": \"游戏技术\"}, {\"id\": \"3\", \"duty\": \"大数据平台开发对接\", \"user_id\": \"chenlijun1\", \"role_name\": \"GPM系统\", \"user_name\": \"陈立军（chenlijun1）\", \"model_name\": \"游戏技术\"}, {\"id\": \"4\", \"duty\": \"运维领导，根据项目情况，指派游戏运维对接人\", \"user_id\": \"yangcan\", \"role_name\": \"运维负责人\", \"user_name\": \"杨灿（yangcan）\", \"model_name\": \"运维支持\"}, {\"id\": \"5\", \"duty\": \"1.如是游戏项目，游戏服务器部署，游戏日常更新维护 2.如是系统项目，游戏服务器部署，系统日常更新维护\", \"user_id\": \"\", \"role_name\": \"项目运维\", \"user_name\": \"\", \"model_name\": \"运维支持\"}, {\"id\": \"6\", \"duty\": \"\", \"user_id\": \"\", \"role_name\": \"数据库运维\", \"user_name\": \"\", \"model_name\": \"运维支持\"}, {\"id\": \"7\", \"duty\": \"西瓜SDK运维/端游计费运维\", \"user_id\": \"zhoudepeng\", \"role_name\": \"系统运维\", \"user_name\": \"周德鹏（zhoudepeng）\", \"model_name\": \"运维支持\"}, {\"id\": \"8\", \"duty\": \"西瓜新功能开发\", \"user_id\": \"wuyihai\", \"role_name\": \"西瓜SDK开发\", \"user_name\": \"吴益海（wuyihai）\", \"model_name\": \"发行支持\"}, {\"id\": \"9\", \"duty\": \"西瓜新功能开发排期安排，项目对接\", \"user_id\": \"caiye\", \"role_name\": \"西瓜SDK开发\", \"user_name\": \"蔡叶（caiye）\", \"model_name\": \"发行支持\"}, {\"id\": \"10\", \"duty\": \"西瓜质检checklist跟进人\", \"user_id\": \"zhanzhitao\", \"role_name\": \"西瓜SDK开发\", \"user_name\": \"詹志涛（zhanzhitao）\", \"model_name\": \"发行支持\"}, {\"id\": \"11\", \"duty\": \"\", \"user_id\": \"liyan\", \"role_name\": \"西瓜SDK运营\", \"user_name\": \"黎芷妍（lizhiyan)\", \"model_name\": \"发行支持\"}, {\"id\": \"12\", \"duty\": \"上线前资产安全漏洞扫描\", \"user_id\": \"caichangchong\", \"role_name\": \"网络安全\", \"user_name\": \"蔡畅翀（caichangchong）\", \"model_name\": \"网络安全\"}, {\"id\": \"13\", \"duty\": \"上线前最终游戏安装包安全检测\", \"user_id\": \"huyongshen\", \"role_name\": \"渗透测试\", \"user_name\": \"胡永燊（huyongshen）\", \"model_name\": \"网络安全\"}, {\"id\": \"14\", \"duty\": \"\", \"user_id\": \"liyan\", \"role_name\": \"配置管理\", \"user_name\": \"黎芷妍（lizhiyan)\", \"model_name\": \"配置管理\"}, {\"id\": 9991, \"duty\": \"\", \"user_id\": \"wangsiyue\", \"role_name\": \"规划服务\", \"user_name\": \"王思樾（wangsiyue）\", \"model_name\": \"规划服务\"}, {\"id\": 9992, \"duty\": \"\", \"user_id\": \"huanghuiwu\", \"role_name\": \"规划服务\", \"user_name\": \"黄辉武（huanghuiwu）\", \"model_name\": \"规划服务\"}, {\"id\": 9993, \"duty\": \"\", \"user_id\": \"lizhiyan\", \"role_name\": \"规划服务\", \"user_name\": \"黎芷妍（lizhiyan）\", \"model_name\": \"规划服务\"}, {\"id\": 9994, \"duty\": \"\", \"user_id\": \"liyan\", \"role_name\": \"规划服务\", \"user_name\": \"李艳（liyan）\", \"model_name\": \"规划服务\"}, {\"id\": 9995, \"duty\": \"\", \"user_id\": \"wangdongyin\", \"role_name\": \"规划服务\", \"user_name\": \"王冬寅（wangdongyin）\", \"model_name\": \"规划服务\"}, {\"id\": 9996, \"duty\": \"\", \"user_id\": \"menglong\", \"role_name\": \"规划服务\", \"user_name\": \"蒙龙（menglong）\", \"model_name\": \"规划服务\"}, {\"id\": 9997, \"duty\": \"\", \"user_id\": \"w_zhangyalan\", \"role_name\": \"规划服务\", \"user_name\": \"张雅蓝（w_zhangyalan）\", \"model_name\": \"规划服务\"}], \"supports\": [{\"id\": \"15\", \"duty\": \"\", \"user_id\": \"\", \"role_name\": \"项目负责人（制作人）\", \"user_name\": \"\", \"model_name\": \"项目发行侧\"}, {\"id\": \"16\", \"duty\": \"跟进游戏版本计划需求\", \"user_id\": \"\", \"role_name\": \"游戏项目PM\", \"user_name\": \"\", \"model_name\": \"项目发行侧\"}, {\"id\": \"17\", \"duty\": \"主导游戏开发排期\", \"user_id\": \"\", \"role_name\": \"游戏主程\", \"user_name\": \"\", \"model_name\": \"项目发行侧\"}, {\"id\": \"18\", \"duty\": \"根据项目PM或主程分发的任务开发\", \"user_id\": \"\", \"role_name\": \"游戏开发\", \"user_name\": \"\", \"model_name\": \"项目发行侧\"}, {\"id\": \"19\", \"duty\": \"\", \"user_id\": \"\", \"role_name\": \"构建对接人\", \"user_name\": \"\", \"model_name\": \"项目发行侧\"}, {\"id\": \"20\", \"duty\": \"跟进发行计划和项目组的信息拉齐确认\", \"user_id\": \"\", \"role_name\": \"运营发行\", \"user_name\": \"\", \"model_name\": \"项目发行侧\"}, {\"id\": \"21\", \"duty\": \"指派西瓜测试对接人\", \"user_id\": \"xufei3\", \"role_name\": \"西瓜测试总负责人\", \"user_name\": \"徐飞（xufei3）\", \"model_name\": \"质量中心\"}, {\"id\": \"22\", \"duty\": \"西瓜新功能开发测试，游戏打包验收前的西瓜参数打包测试，游戏渠道包测试过程中反馈的西瓜bug\", \"user_id\": \"wujingjuan\", \"role_name\": \"西瓜测试\", \"user_name\": \"伍婧娟（wujingjuan）\", \"model_name\": \"质量中心\"}, {\"id\": \"23\", \"duty\": \"游戏打包后的游戏渠道包测试\", \"user_id\": \"chensushan\", \"role_name\": \"西瓜测试\", \"user_name\": \"陈素珊（chensushan）\", \"model_name\": \"质量中心\"}, {\"id\": \"24\", \"duty\": \"\", \"user_id\": \"\", \"role_name\": \"游戏测试\", \"user_name\": \"\", \"model_name\": \"质量中心\"}]}]"
            },
            {
              "appId": 200001022,
              "appName": "运维系统",
              "privileges": [
                {
                  "sysCode": "omms",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/sysmanage/auth/data",
                  "res_name": "数据权限管理",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/finance/finance",
                  "res_name": "汇总图表(前端)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/finance/finance_multi",
                  "res_name": "多类别汇总表(前端)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/finance/bill",
                  "res_name": "账单明细(前端)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "data_access",
                  "res_name": "项目数据权限(后端)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 0,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "sys_access",
                  "res_name": "系统访问权限",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/mange/financeimport",
                  "res_name": "账单处理(前端)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "finance_admin",
                  "res_name": "所有项目数据(后端)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/mange/audit_logs",
                  "res_name": "审计日志",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/mange/finance_share",
                  "res_name": "财务处理(分摊配置)",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                },
                {
                  "sysCode": "omms",
                  "appId": 200001022,
                  "appName": "",
                  "fatherAppName": "",
                  "fatherAppId": 0,
                  "resCode": "/gop/serverlist",
                  "res_name": "机甲serverlist_cdn",
                  "createdTime": "0001-01-01T00:00:00Z",
                  "isKoa": false,
                  "roleName": "",
                  "envOnly": false
                }
              ],
              "shortName": "TZ",
              "gameEngine": "",
              "engineVersion": "",
              "releaseRegion": "",
              "screenDirection": "",
              "gameType": "",
              "icon": "",
              "shareDepart": "",
              "sapId": "999",
              "isGame": 0,
              "appOwner": "chenwei10",
              "process": 0,
              "bigDataState": "",
              "processName": "",
              "emailGroup": "",
              "appStandbyOwner": "",
              "handlers": ""
            }
          ]
        }
        resolve(data.data)
        return;
        // TechApi.getPrivileges()
        //   .then((response) => {
        //     let _data = checkResponseCode(response);
        //     resolve(_data);
        //   })
        //   .catch((error) => {
        //     reject(error);
        //   });
      });
    },
    moduleGetProjectAndEnv(_data){
      return new Promise((resolve, reject) => {
        TechApi.getProjectAndEnv(_data)
          .then((response) => {
            let _data = checkResponseCode(response);
            resolve(_data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    }
  }
})
