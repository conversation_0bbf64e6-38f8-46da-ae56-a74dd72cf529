/**
 * 认证状态管理
 * 管理用户登录状态、token等
 */
import { defineStore } from 'pinia'
import { cookieLogin, getUserInfo, logout } from '@/api/services/system/auth'

export const useAuthStore = defineStore('authStore', {
  state: () => ({
    // 用户信息
    userInfo: null,
    // 访问token
    token: null,
    // token过期时间
    tokenExpire: null,
    // 是否已登录
    isLoggedIn: false,
    // 登录状态
    loginStatus: 'idle' // idle, loading, success, error
  }),

  getters: {
    /**
     * 检查token是否有效
     */
    isTokenValid: (state) => {
      if (!state.token || !state.tokenExpire) {
        return false
      }
      // 检查是否过期（提前5分钟判断过期）
      const now = Date.now()
      const expireTime = state.tokenExpire - 5 * 60 * 1000 // 提前5分钟
      return now < expireTime
    },

    /**
     * 获取用户ID
     */
    userId: (state) => {
      return state.userInfo?.userId || null
    },

    /**
     * 获取用户角色
     */
    userRole: (state) => {
      return state.userInfo?.roleId || null
    }
  },

  actions: {
    /**
     * 确保有有效的token
     * 先检查store中的token，如果没有或过期，则从cookie获取并调用登录API
     */
    async ensureToken() {
      try {
        // 如果已有有效token，直接返回
        if (this.token && this.isTokenValid) {
          console.log('✅ 使用现有有效token')
          return { success: true, token: this.token }
        }

        // 防止重复请求 - 如果当前状态是loading，则等待
        if (this.loginStatus === 'loading') {
          console.log('⏳ 正在获取token，请等待...')
          // 等待一段时间后返回
          await new Promise(resolve => setTimeout(resolve, 1000))
          return { success: false, error: '正在获取token中，请稍后再试' }
        }

        console.log('🔄 需要获取新token...')
        this.loginStatus = 'loading'

        // 从cookie中获取jwt_token
        // const jwtToken = this.getCookie('jwt_token')
        // if (!jwtToken) {
        //   this.loginStatus = 'error'
        //   throw new Error('未找到jwt_token')
        // }

        // console.log('🔑 从Cookie获取到jwt_token:', jwtToken.substring(0, 20) + '...')

        try {
          // 调用登录API获取系统token
          const response = await cookieLogin()

          if (response.code === 0 && response.data) {
            const { userId, token, expire } = response.data

            // 存储token信息到store
            this.token = token
            this.tokenExpire = expire
            this.isLoggedIn = true
            this.loginStatus = 'success'

            // 存储到localStorage
            localStorage.setItem('auth_token', token)
            localStorage.setItem('auth_expire', expire.toString())
            localStorage.setItem('auth_userId', userId)

            console.log('✅ 获取系统token成功:', {
              userId,
              token: token.substring(0, 20) + '...',
              expire: new Date(expire).toLocaleString()
            })

            // 获取用户详细信息
            await this.fetchUserInfo()

            return { success: true, token: token }
          } else {
            throw new Error(response.msg || '获取token失败')
          }
        } catch (error) {
          console.error('❌ API调用失败:', error.message)
          
          // 开发环境下，如果API调用失败，使用模拟数据
          if (import.meta.env.MODE === 'development') {
            console.log('🔧 开发环境: 使用模拟token数据')
            
            // 模拟成功响应
            const mockToken = 'dev_mock_token_' + Date.now()
            const mockExpire = Date.now() + 24 * 60 * 60 * 1000 // 24小时后过期
            
            this.token = mockToken
            this.tokenExpire = mockExpire
            this.isLoggedIn = true
            this.loginStatus = 'success'
            
            // 存储到localStorage
            localStorage.setItem('auth_token', mockToken)
            localStorage.setItem('auth_expire', mockExpire.toString())
            localStorage.setItem('auth_userId', 'dev_user')
            
            // 模拟用户信息
            this.userInfo = {
              userId: 'dev_user',
              name: '开发测试用户',
              roleId: 'admin',
              permissions: ['*']
            }
            
            localStorage.setItem('auth_userInfo', JSON.stringify(this.userInfo))
            
            console.log('✅ 开发环境: 已设置模拟token数据')
            return { success: true, token: mockToken }
          }
          
          this.loginStatus = 'error'
          return { success: false, error: error.message }
        }
      } catch (error) {
        console.error('❌ 获取token失败:', error.message)
        this.loginStatus = 'error'
        return { success: false, error: error.message }
      }
    },

    /**
     * 获取用户信息
     */
    async fetchUserInfo() {
      try {
        const response = await getUserInfo()
        if (response.code === 0 && response.data) {
          this.userInfo = response.data
          localStorage.setItem('auth_userInfo', JSON.stringify(response.data))
          console.log('✅ 获取用户信息成功:', response.data)
        }
      } catch (error) {
        console.error('❌ 获取用户信息失败:', error.message)
      }
    },

    /**
     * 退出登录
     */
    async logoutUser() {
      try {
        // 调用退出API
        await logout()
      } catch (error) {
        console.error('退出登录API调用失败:', error.message)
      } finally {
        // 无论API调用是否成功，都清除本地状态
        this.clearAuth()
        console.log('✅ 已退出登录')
      }
    },

    /**
     * 清除认证信息
     */
    clearAuth() {
      this.userInfo = null
      this.token = null
      this.tokenExpire = null
      this.isLoggedIn = false
      this.loginStatus = 'idle'
      
      // 清除localStorage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_expire')
      localStorage.removeItem('auth_userId')
      localStorage.removeItem('auth_userInfo')
    },

    /**
     * 从localStorage恢复认证状态
     */
    restoreAuthFromStorage() {
      try {
        const token = localStorage.getItem('auth_token')
        const expire = localStorage.getItem('auth_expire')
        const userInfo = localStorage.getItem('auth_userInfo')
        
        if (token && expire) {
          const expireTime = parseInt(expire)
          const now = Date.now()
          
          // 检查token是否过期
          if (now < expireTime) {
            this.token = token
            this.tokenExpire = expireTime
            this.isLoggedIn = true
            this.loginStatus = 'success'
            
            if (userInfo) {
              this.userInfo = JSON.parse(userInfo)
            }
            
            console.log('✅ 从本地存储恢复认证状态成功')
            return true
          } else {
            console.log('⚠️ 本地token已过期，清除认证信息')
            this.clearAuth()
          }
        }
      } catch (error) {
        console.error('❌ 恢复认证状态失败:', error.message)
        this.clearAuth()
      }
      return false
    },

    /**
     * 获取Cookie值
     * @param {string} name - Cookie名称
     * @returns {string|null} Cookie值
     */
    getCookie(name) {
      const value = `; ${document.cookie}`
      const parts = value.split(`; ${name}=`)
      if (parts.length === 2) {
        return parts.pop().split(';').shift()
      }
      return null
    },

    /**
     * 检查并刷新token
     */
    async checkAndRefreshToken() {
      if (!this.isTokenValid && this.isLoggedIn) {
        console.log('⚠️ Token即将过期，尝试重新获取')
        await this.ensureToken()
      }
    }
  }
})
