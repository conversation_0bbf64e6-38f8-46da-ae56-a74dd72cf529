/**
 * API管理工具
 * 根据配置决定使用Mock数据还是真实接口
 */
import * as Settings from '../settings';

/**
 * API调用包装器
 * @param {Function} realApiCall - 真实API调用函数
 * @param {Function} mockApiCall - Mock API调用函数
 * @param {Object} params - 请求参数
 * @returns {Promise} API响应
 */
export function apiCall(realApiCall, mockApiCall, ...params) {
  if (Settings.default.useMock) {
    console.log('🔧 使用Mock数据');
    return mockApiCall(...params);
  } else {
    console.log('🌐 使用真实API');
    return realApiCall(...params);
  }
}

/**
 * 环境信息
 */
export const envInfo = {
  current: Settings.default.env.current,
  isDev: Settings.default.env.isDev,
  isProd: Settings.default.env.isProd,
  useMock: Settings.default.useMock
};

/**
 * 打印当前API配置信息
 */
export function printApiConfig() {
  console.group('🔧 API配置信息');
  console.log('环境:', envInfo.current);
  console.log('使用Mock:', envInfo.useMock);
  console.log('API地址:', Settings.default.api.baseUrl || '使用代理');
  console.log('超时时间:', Settings.default.api.timeout + 'ms');
  console.groupEnd();
}

/**
 * 切换Mock模式（仅开发环境）
 * @param {boolean} useMock - 是否使用Mock
 */
export function toggleMock(useMock) {
  if (envInfo.isDev) {
    Settings.default.useMock = useMock;
    console.log(`🔄 已切换到${useMock ? 'Mock' : '真实API'}模式`);
    // 可以在这里添加页面刷新逻辑
    // window.location.reload();
  } else {
    console.warn('⚠️ 只能在开发环境切换Mock模式');
  }
}
