/**
 * create by sxf on 2019/3/7.
 * 功能: HTTP请求封装
 * 支持Mock数据和真实API切换
 */
import axios from "axios";
import * as Settings from "./../settings";

// 创建axios实例
const service = axios.create({
  baseURL: Settings.default.api.baseUrl, // 使用配置的API地址
  timeout: Settings.default.api.timeout, // 使用配置的超时时间
  withCredentials: Settings.default.api.withCredentials, // 使用配置的凭证设置
  headers: {
    "Content-Type": "application/json",
  },
});
let loadingInstance = null;
var CancelToken = axios.CancelToken;
let pending = []; //声明一个数组用于存储每个ajax请求的取消函数和ajax标识

// request拦截器
service.interceptors.request.use(
  (config) => {
    // 添加认证令牌
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = token
    }

    // 开发环境下打印请求信息
    if (Settings.default.env.isDev) {
      console.log('🌐 API请求:', config.method?.toUpperCase(), config.url)
    }

    config.cancelToken = new CancelToken((c) => {
      // 这里的ajax标识我是用请求地址&请求方式拼接的字符串，当然你可以选择其他的一些方式
      pending.push({ u: config.url + "&" + config.method, f: c });
    });
    return config;
  },
  (error) => {
    if (loadingInstance != null) {
      loadingInstance.close();
    }
    console.log(error); // for debug
    Promise.reject(error);
  }
);

// response 拦截器
service.interceptors.response.use(
  (response) => {
    // 开发环境下打印响应信息
    if (Settings.default.env.isDev) {
      console.log('✅ API响应:', response.status, response.config.url)
    }

    // 统一处理响应数据
    const responseData = response.data;

    // 处理认证相关的特殊code
    if (responseData.code === -210000 || responseData.code === 10008) {
      let _redirecturl;

      if (Settings.default.isDevelopMode) {
        // 开发使用
        _redirecturl =
          responseData.data.redirect +
          "?service=" +
          encodeURIComponent(
            Settings.default.developVerifyTicketUrl +
              "?from=" +
              encodeURIComponent(window.location.href)
          );
      } else {
        // 部署使用
        _redirecturl =
          responseData.data.redirect +
          "?service=" +
          encodeURIComponent(
            responseData.data.service +
              "?from=" +
              encodeURIComponent(window.location.href)
          );
      }
      var isChrome = window.navigator.userAgent.indexOf("Chrome");
      if (isChrome === -1) {
        window.location.href = "./remind.html";
      } else {
        window.location.href = _redirecturl;
      }
      return;
    }

    // 统一判断业务code，如果不是200则抛出错误
    if (responseData.code !== undefined && responseData.code !== 200) {
      const errorMessage = responseData.msg || responseData.message || '请求失败';
      if (Settings.default.env.isDev) {
        console.error('❌ 业务错误:', responseData.code, errorMessage)
      }
      // 抛出错误，让调用方处理
      throw new Error(errorMessage);
    }

    return responseData;
  },
  (error) => {
    // 开发环境下打印错误信息
    if (Settings.default.env.isDev) {
      console.error('❌ API请求错误:', error.response?.status, error.config?.url, error.message)
    }

    console.log("err" + error); // for debug
    //console.log(error.response.status);
    if (error && error.response) {
      console.log(error.response.status);
    }
    return { code: 500, msg: error, data: [] };
  }
);

export default service;
