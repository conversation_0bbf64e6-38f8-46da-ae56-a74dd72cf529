/**
 * webhook.js - 推送地址相关工具函数
 */

/**
 * 生成推送地址
 * @param {string} type 集成类型名称，如 'prometheus', 'grafana' 等
 * @param {number|string} id 集成ID或UUID
 * @param {boolean} useRealId 是否使用真实ID（默认false，用于兼容旧逻辑）
 * @returns {string} 推送地址
 */
export const generatePushUrl = (type, id, useRealId = false) => {
  // 格式化集成类型名称（转为小写，去除空格）
  const formattedType = type.toLowerCase().replace(/\s+/g, '-');

  if (useRealId && id && id !== 'new') {
    // 使用真实UUID生成推送地址
    return `https://omms-push.seasungames.com/api/v1/${id}`;
  } else {
    // 生成随机字符串作为令牌（兼容旧逻辑）
    const randomToken = Math.random().toString(36).substring(2, 15) +
                        Math.random().toString(36).substring(2, 15);

    // 当前时间戳（作为版本标识）
    const timestamp = Date.now();

    // 构建推送地址
    return `https://omms-push.seasungames.com/api/v1/${formattedType}/${id}/${randomToken}?t=${timestamp}`;
  }
};