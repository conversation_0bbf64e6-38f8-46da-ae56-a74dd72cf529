#app {
  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth ;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden ;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
        padding-top: 10px;
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .el-sub-menu__title * {
      vertical-align: middle !important;
    }

    .el-menu-item * {
      vertical-align: middle !important;
    }


    .el-menu {
      border: none;
      height: 100%;
      width: 100% ;
      background: transparent;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: $menuHover ;
      }
    }

    .is-active>.el-sub-menu__title {
      color: $subMenuActiveText ;
    }

    & .nest-menu .el-sub-menu>.el-submenu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $sideBarWidth ;
      background-color: $subMenuBg ;

      &:hover {
        background-color: $subMenuHover ;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px ;
    }


    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 ;
      position: relative;

      .el-tooltip {
        padding: 0 ;


      }
    }


    .el-sub-menu {
      overflow: hidden;

      &>.el-sub-menu__title {
        //padding: 0 ;



        .el-sub-menu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $sideBarWidth ;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth ;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    background: $menuBg !important;
  }

  .el-menu-item * {
    vertical-align: baseline !important;
  }

  .nest-menu .el-sub-menu>.el-sub-menu__title,
  .el-menu-item {

    &:hover {
      // you can use $subMenuHover
      background-color: $subMenuHover ;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
