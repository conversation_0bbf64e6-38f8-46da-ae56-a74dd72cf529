<template>
  <div v-if="showDevTools" class="dev-tools">
    <el-card class="dev-tools-card">
      <template #header>
        <div class="dev-tools-header">
          <span>🔧 开发工具</span>
          <el-button 
            type="text" 
            @click="toggleDevTools"
            class="toggle-btn"
          >
            {{ collapsed ? '展开' : '收起' }}
          </el-button>
        </div>
      </template>
      
      <div v-if="!collapsed" class="dev-tools-content">
        <!-- 环境信息 -->
        <div class="info-section">
          <h4>环境信息</h4>
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="当前环境">
              <el-tag :type="envTagType">{{ envInfo.current }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="Mock状态">
              <el-tag :type="envInfo.useMock ? 'warning' : 'success'">
                {{ envInfo.useMock ? '使用Mock' : '真实API' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="API地址">
              {{ apiBaseUrl || '使用代理' }}
            </el-descriptions-item>
            <el-descriptions-item label="超时时间">
              {{ apiTimeout }}ms
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- Mock切换 -->
        <div class="control-section">
          <h4>Mock控制</h4>
          <el-switch
            v-model="mockEnabled"
            @change="handleMockToggle"
            active-text="使用Mock"
            inactive-text="真实API"
            :disabled="!envInfo.isDev"
          />
          <el-text v-if="!envInfo.isDev" type="warning" size="small">
            只能在开发环境切换Mock模式
          </el-text>
        </div>

        <!-- 快捷操作 -->
        <div class="action-section">
          <h4>快捷操作</h4>
          <el-space>
            <el-button size="small" @click="printConfig">
              打印配置
            </el-button>
            <el-button size="small" @click="clearCache">
              清除缓存
            </el-button>
            <el-button size="small" @click="reloadPage">
              重新加载
            </el-button>
          </el-space>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { envInfo, printApiConfig, toggleMock } from '@/utils/apiManager'
import * as Settings from '@/settings'

// 响应式数据
const showDevTools = ref(false)
const collapsed = ref(false)
const mockEnabled = ref(envInfo.useMock)

// 计算属性
const envTagType = computed(() => {
  switch (envInfo.current) {
    case 'development': return 'primary'
    case 'production': return 'danger'
    default: return 'warning'
  }
})

const apiBaseUrl = computed(() => Settings.default.api.baseUrl)
const apiTimeout = computed(() => Settings.default.api.timeout)

// 方法
const toggleDevTools = () => {
  collapsed.value = !collapsed.value
}

const handleMockToggle = (value) => {
  toggleMock(value)
  mockEnabled.value = value
  // 可选：刷新页面以应用更改
  // setTimeout(() => window.location.reload(), 500)
}

const printConfig = () => {
  printApiConfig()
}

const clearCache = () => {
  localStorage.clear()
  sessionStorage.clear()
  console.log('🗑️ 缓存已清除')
}

const reloadPage = () => {
  window.location.reload()
}

// 生命周期
onMounted(() => {
  // 只在开发环境显示开发工具
  showDevTools.value = envInfo.isDev
  
  // 键盘快捷键：Ctrl+Shift+D 切换开发工具
  const handleKeydown = (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
      e.preventDefault()
      if (showDevTools.value) {
        collapsed.value = !collapsed.value
      }
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  // 组件卸载时移除事件监听
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})
</script>

<style scoped>
.dev-tools {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
}

.dev-tools-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dev-tools-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-btn {
  padding: 0;
}

.dev-tools-content {
  max-height: 500px;
  overflow-y: auto;
}

.info-section,
.control-section,
.action-section {
  margin-bottom: 16px;
}

.info-section h4,
.control-section h4,
.action-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}
</style>
