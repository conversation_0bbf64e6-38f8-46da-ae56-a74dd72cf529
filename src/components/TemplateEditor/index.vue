<template>
  <div class="template-editor-container" :style="{ height: height }">
    <div ref="editorContainer" class="monaco-editor-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as monaco from 'monaco-editor'

defineOptions({
  name: 'TemplateEditor'
})

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  language: {
    type: String,
    default: 'plaintext'
  },
  height: {
    type: String,
    default: '300px'
  },
  theme: {
    type: String,
    default: 'vs'
  },
  options: {
    type: Object,
    default: () => ({})
  },
  suggestions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'editor-mounted'])

const editorContainer = ref(null)
let editor = null

// 自定义语法高亮和自动补全
const configureTemplateLanguage = () => {
  // 注册一个新的语言
  monaco.languages.register({ id: 'template' })

  // 定义语法高亮规则
  monaco.languages.setMonarchTokensProvider('template', {
    tokenizer: {
      root: [
        [/{{/, { token: 'delimiter.curly', next: '@templateExpression' }],
        [/./, 'text']
      ],
      templateExpression: [
        [/if|else|end|range|not|in/, 'keyword'],
        [/\./, 'delimiter.dot'],
        [/[a-zA-Z_][a-zA-Z0-9_]*/, 'variable'],
        [/"[^"]*"/, 'string'],
        [/'[^']*'/, 'string'],
        [/}}/, { token: 'delimiter.curly', next: '@pop' }],
        [/./, 'expression']
      ]
    }
  })

  // 配置自动括号配对
  monaco.languages.setLanguageConfiguration('template', {
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '{{', close: '}}' },
      { open: '(', close: ')' },
      { open: '[', close: ']' },
      { open: '"', close: '"' },
      { open: "'", close: "'" }
    ],
    surroundingPairs: [
      { open: '{', close: '}' },
      { open: '(', close: ')' },
      { open: '[', close: ']' },
      { open: '"', close: '"' },
      { open: "'", close: "'" }
    ]
  })

  // 设置自动补全提供程序
  monaco.languages.registerCompletionItemProvider('template', {
    provideCompletionItems: (model, position) => {
      // 获取当前行的内容
      const textUntilPosition = model.getValueInRange({
        startLineNumber: position.lineNumber,
        startColumn: 1,
        endLineNumber: position.lineNumber,
        endColumn: position.column
      })

      // 严格检查是否在双花括号内
      const lastOpenBracket = textUntilPosition.lastIndexOf('{{')
      if (lastOpenBracket === -1) {
        return { suggestions: [] } // 如果没有找到 {{，不提供补全
      }

      const lastCloseBracket = textUntilPosition.lastIndexOf('}}')
      
      // 如果最后的关闭括号在最后的开放括号之后，说明不在花括号内
      if (lastCloseBracket > lastOpenBracket) {
        return { suggestions: [] }
      }
      
      // 检查是否在开始标记后面
      const textAfterOpenBracket = textUntilPosition.substring(lastOpenBracket)
      // 如果只有 {{ 没有其他内容，也不显示补全（需要用户输入至少一个字符）
      if (textAfterOpenBracket === '{{') {
        return { suggestions: [] }
      }

      // 获取花括号内的内容，用于上下文相关补全
      const bracketContent = textUntilPosition.substring(lastOpenBracket + 2).trim()
      
      // 基础模板标签建议
      const suggestions = [
        {
          label: 'if',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'if ${1:condition}}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          detail: '条件判断',
          documentation: '条件逻辑，格式: {{ if 条件 }}',
          sortText: '01-if' // 排序权重
        },
        {
          label: 'else',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'else}}',
          detail: '条件分支',
          documentation: '条件逻辑的else分支，格式: {{ else }}',
          sortText: '01-else' // 排序权重
        },
        {
          label: 'end',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'end}}',
          detail: '结束标签',
          documentation: '结束if或range块，格式: {{ end }}',
          sortText: '01-end' // 排序权重
        },
        {
          label: 'range',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'range ${1:k}, ${2:v} := ${3:collection}}}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          detail: '循环遍历',
          documentation: '遍历集合，格式: {{ range k, v := collection }}',
          sortText: '01-range' // 排序权重
        },
        {
          label: 'not',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'not (${1:condition})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          detail: '否定条件',
          documentation: '否定条件判断，格式: {{ not (条件) }}',
          sortText: '01-not' // 排序权重
        },
        {
          label: 'in',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'in ${1:item} "${2:value1}" "${3:value2}"',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          detail: '包含判断',
          documentation: '判断元素是否在集合中，格式: {{ in 元素 集合 }}',
          sortText: '01-in' // 排序权重
        }
      ]

      // 添加标签建议
      const labelSuggestions = [
        {
          label: '.Labels',
          kind: monaco.languages.CompletionItemKind.Property,
          insertText: '.Labels',
          detail: '标签对象',
          documentation: '包含所有可用标签的对象',
          sortText: '02-Labels' // 排序权重
        },
        {
          label: '.Labels.body_text',
          kind: monaco.languages.CompletionItemKind.Property,
          insertText: '.Labels.body_text',
          detail: '正文文本标签',
          documentation: '代表正文内容的标签',
          sortText: '02-body_text' // 排序权重
        },
        {
          label: '.Labels.resource',
          kind: monaco.languages.CompletionItemKind.Property,
          insertText: '.Labels.resource',
          detail: '资源标签',
          documentation: '代表资源名称的标签',
          sortText: '02-resource' // 排序权重
        },
        {
          label: '.Description',
          kind: monaco.languages.CompletionItemKind.Property,
          insertText: '.Description',
          detail: '描述信息',
          documentation: '告警的描述信息',
          sortText: '02-Description' // 排序权重
        },
        {
          label: '.Annotations',
          kind: monaco.languages.CompletionItemKind.Property,
          insertText: '.Annotations',
          detail: '注解对象',
          documentation: '包含告警注解的对象',
          sortText: '02-Annotations' // 排序权重
        },
        {
          label: '.Annotations.summary',
          kind: monaco.languages.CompletionItemKind.Property,
          insertText: '.Annotations.summary',
          detail: '摘要信息',
          documentation: '告警的摘要信息',
          sortText: '02-summary' // 排序权重
        },
        {
          label: '.StartsAt',
          kind: monaco.languages.CompletionItemKind.Property,
          insertText: '.StartsAt',
          detail: '开始时间',
          documentation: '告警的开始时间',
          sortText: '02-StartsAt' // 排序权重
        }
      ]

      // 添加函数建议
      const functionSuggestions = [
        {
          label: 'joinAlertLabels',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'joinAlertLabels . "${1:resource}" "${2:, }"',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          detail: '连接标签值',
          documentation: '使用分隔符连接多个标签值',
          sortText: '03-joinAlertLabels' // 排序权重
        },
        {
          label: 'colorSeverity',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'colorSeverity .IncidentSeverity',
          detail: '严重程度颜色',
          documentation: '根据严重程度返回对应的颜色',
          sortText: '03-colorSeverity' // 排序权重
        },
        {
          label: 'date',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'date "${1:2006-01-02 15:04:05}" .${2:StartsAt}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          detail: '日期格式化',
          documentation: '格式化日期时间',
          sortText: '03-date' // 排序权重
        }
      ]

      // 添加用户自定义的建议
      let customSuggestions = []
      if (props.suggestions && props.suggestions.length > 0) {
        customSuggestions = props.suggestions.map((suggestion, index) => ({
          label: suggestion.label || suggestion,
          kind: monaco.languages.CompletionItemKind.Variable,
          insertText: suggestion.insertText || suggestion.label || suggestion,
          detail: suggestion.detail || '自定义变量',
          documentation: suggestion.documentation || '用户自定义的变量',
          iconClasses: ['custom-suggestion-icon'],
          sortText: `04-${(index + 1).toString().padStart(2, '0')}-${suggestion.label || suggestion}` // 排序权重
        }))
      }

      // 合并所有建议并去重
      const allSuggestions = [...suggestions, ...labelSuggestions, ...functionSuggestions, ...customSuggestions]
      
      // 使用Map进行去重，以label为键
      const uniqueSuggestions = Array.from(
        new Map(allSuggestions.map(item => [item.label, item])).values()
      )
      
      // 智能排序：根据用户当前输入的内容进行相关性排序
      if (bracketContent) {
        uniqueSuggestions.forEach(suggestion => {
          const label = suggestion.label.toLowerCase()
          const content = bracketContent.toLowerCase()
          
          // 如果标签与输入完全匹配，给最高优先级
          if (label === content) {
            suggestion.sortText = '00-exact-' + suggestion.sortText
          } 
          // 如果标签以输入开头，给高优先级
          else if (label.startsWith(content)) {
            suggestion.sortText = '00-prefix-' + suggestion.sortText
          }
          // 如果标签包含输入，给中等优先级
          else if (label.includes(content)) {
            suggestion.sortText = '00-contains-' + suggestion.sortText
          }
        })
      }
      
      return {
        suggestions: uniqueSuggestions
      }
    },
    triggerCharacters: ['.'] // 只保留点号作为触发字符，移除{和$
  })
}

onMounted(() => {
  configureTemplateLanguage()

  // 创建编辑器
  const defaultOptions = {
    value: props.modelValue,
    language: props.language === 'template' ? 'template' : props.language,
    theme: props.theme,
    automaticLayout: true,
    fontSize: 14,
    lineHeight: 20,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    tabSize: 2,
    wordWrap: 'on',
    autoClosingBrackets: 'always', // 启用自动闭合括号
    autoClosingQuotes: 'always',   // 启用自动闭合引号
    quickSuggestions: {
      other: false,  // 禁用其他位置的快速建议
      comments: false, // 禁用注释中的快速建议
      strings: false   // 禁用字符串中的快速建议
    },
    suggestOnTriggerCharacters: true, // 只在触发字符时显示建议
    acceptSuggestionOnEnter: 'on',
    snippetSuggestions: 'inline'
  }

  editor = monaco.editor.create(editorContainer.value, {
    ...defaultOptions,
    ...props.options
  })

  // 监听内容变化
  editor.onDidChangeModelContent((event) => {
    const value = editor.getValue()
    emit('update:modelValue', value)
    emit('change', value)

    // 处理花括号自动配对
    handleBracketPairs(event)
  })

  // 处理花括号自动配对的函数
  const handleBracketPairs = (event) => {
    // 只处理单个字符的输入
    if (event.changes.length !== 1 || event.changes[0].text.length !== 1) {
      return
    }

    const change = event.changes[0]
    const model = editor.getModel()
    const position = {
      lineNumber: change.range.startLineNumber,
      column: change.range.startColumn + 1 // +1 因为已经插入了字符
    }

    // 如果输入的是第一个 {
    if (change.text === '{') {
      // 获取当前行内容
      const lineContent = model.getLineContent(position.lineNumber)
      const textBeforeCursor = lineContent.substring(0, position.column - 1)

      // 查找最后一个 { 的位置
      const lastBraceIndex = textBeforeCursor.lastIndexOf('{')
      
      // 如果前一个字符也是 {，说明用户输入了两个连续的 {{
      if (lastBraceIndex === position.column - 2) {
        // 自动添加 }}
        editor.executeEdits('auto-brackets', [
          {
            range: new monaco.Range(
              position.lineNumber,
              position.column,
              position.lineNumber,
              position.column
            ),
            text: '}}',
            forceMoveMarkers: true
          }
        ])
        
        // 将光标放置在 {{|}} 中间
        editor.setPosition({
          lineNumber: position.lineNumber,
          column: position.column
        })
      }
    }
  }

  // 添加键盘事件监听（留作扩展用）
  /* editor.onKeyDown((e) => {
    // 进一步的键盘事件处理可以在这里添加
  }) */

  emit('editor-mounted', editor)
})

// 监听modelValue的变化，更新编辑器内容
watch(() => props.modelValue, (newValue) => {
  if (editor && newValue !== editor.getValue()) {
    editor.setValue(newValue)
  }
}, { deep: true })

// 监听语言的变化
watch(() => props.language, (newValue) => {
  if (editor) {
    monaco.editor.setModelLanguage(editor.getModel(), 
      newValue === 'template' ? 'template' : newValue)
  }
})

// 监听主题的变化
watch(() => props.theme, (newValue) => {
  if (editor) {
    monaco.editor.setTheme(newValue)
  }
})

// 组件销毁时销毁编辑器
onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
  }
})
</script>

<style scoped>
.template-editor-container {
  position: relative;
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.monaco-editor-container {
  width: 100%;
  height: 100%;
}

.monaco-editor .margin-view-overlays .line-numbers {
  left: 0px;
  width: 22px;
}

/* 全局样式，需要添加到全局CSS中 */
:deep(.custom-suggestion-icon::before) {
  content: "⚡";
  margin-right: 4px;
  color: #1890ff;
}
</style>

<style>
/* Monaco编辑器补全菜单样式优化 */
.monaco-list-row {
  display: flex;
  align-items: center;
}

.monaco-list-row:hover {
  background-color: #f5f7fa !important;
}

.monaco-icon-label {
  display: flex !important;
  align-items: center !important;
}

.monaco-list .monaco-list-row.focused {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
}

.monaco-editor .suggest-widget {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #ebeef5;
}
</style> 