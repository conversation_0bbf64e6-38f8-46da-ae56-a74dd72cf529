import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import qiankun from 'vite-plugin-qiankun';

// 支持Markdown导入
import Markdown from 'vite-plugin-md'

const name = 'event-ui';
// https://vitejs.dev/config/
export default defineConfig({
  base:name,
  plugins: [
    vue({
      include: [/\.vue$/, /\.md$/], // 让Vue插件处理Markdown文件
    }),
    vueJsx(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
    qiankun(name, {
      useDevMode: true
    }),
    // 添加Markdown支持
    Markdown()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: "@import \"@/styles/main.scss\";"
      },
      less: {
        modifyVars: {},
        javascriptEnabled: true,
      },
    }
  },
  server: {
    port: 9008,
    proxy: {
      // 权限系统代理
      '/tech': {
        target: "https://tech.seasungame.com/",  //目标代理接口地址
        secure: false,
        changeOrigin: true,  //开启代理，在本地创建一个虚拟服务端
        ws: true
      },
      // OnCall服务代理 - 值班管理相关API (端口2002)
      '/oncall-api': {
        target: "http://localhost:2002",
        secure: false,
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/oncall-api/, ''),
        configure: (proxy) => {
          proxy.on('error', (err) => {
            console.log('OnCall服务代理错误:', err.message);
          });
          proxy.on('proxyReq', (_, req) => {
            console.log('OnCall服务代理请求:', req.method, req.url);
          });
        }
      },

      // System服务代理 - 系统管理相关API (端口2001)
      '/system-api': {
        target: "http://localhost:2001",
        secure: false,
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/system-api/, ''),
        configure: (proxy) => {
          proxy.on('error', (err) => {
            console.log('System服务代理错误:', err.message);
          });
          proxy.on('proxyReq', (_, req) => {
            console.log('System服务代理请求:', req.method, req.url);
          });
        }
      },

      // Message服务代理 - 消息通知相关API (端口2003)
      '/message-api': {
        target: "http://localhost:2003",
        secure: false,
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/message-api/, ''),
        configure: (proxy) => {
          proxy.on('error', (err) => {
            console.log('Message服务代理错误:', err.message);
          });
          proxy.on('proxyReq', (_, req) => {
            console.log('Message服务代理请求:', req.method, req.url);
          });
        }
      },
    }
  },
  // Monaco Editor配置
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          jsonWorker: ['monaco-editor/esm/vs/language/json/json.worker'],
          cssWorker: ['monaco-editor/esm/vs/language/css/css.worker'],
          htmlWorker: ['monaco-editor/esm/vs/language/html/html.worker'],
          tsWorker: ['monaco-editor/esm/vs/language/typescript/ts.worker'],
          editorWorker: ['monaco-editor/esm/vs/editor/editor.worker'],
        }
      }
    }
  }
})
