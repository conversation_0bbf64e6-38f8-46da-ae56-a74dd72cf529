# API接口配置指南

本项目采用分阶段的API配置方案，支持开发、联调、生产三个阶段的无缝切换。

## 🏗️ 架构概览

```
开发阶段 (useMock: true)
├── Mock数据 (src/api/mock/)
└── 本地开发

联调阶段 (useMock: false + 代理)
├── Vite代理 (vite.config.js)
├── 后端开发服务器
└── 真实API调用

生产环境 (useMock: false + 真实API)
├── 生产API服务器
└── 真实API调用
```

## 📋 配置步骤

### 1. 开发阶段配置

**特点**: 使用Mock数据，无需后端服务

```javascript
// src/settings.js
useMock: true  // 使用Mock数据
```

**优势**:
- 前端独立开发
- 快速原型验证
- 无网络依赖

### 2. 联调阶段配置

**特点**: 通过代理连接后端开发服务器

```javascript
// src/settings.js
useMock: false  // 切换到真实API

// vite.config.js - 配置代理
proxy: {
  '/api': {
    target: "http://localhost:8080", // 后端服务器地址
    changeOrigin: true
  }
}
```

**步骤**:
1. 设置 `useMock: false`
2. 配置 `vite.config.js` 中的代理地址
3. 确保后端服务器运行在指定端口

### 3. 生产环境配置

**特点**: 直接连接生产API服务器

```javascript
// src/settings.js
api: {
  baseUrl: 'https://your-production-api.com'
}
useMock: false
```

## 🔧 使用方法

### 快速切换Mock模式

```javascript
import { toggleMock } from '@/utils/apiManager'

// 切换到Mock模式
toggleMock(true)

// 切换到真实API
toggleMock(false)
```

### API调用示例

```javascript
// src/api/example.js
import { apiCall } from '@/utils/apiManager'
import { mockGetData } from './mock/example'

function realGetData(params) {
  return request({
    url: '/api/data',
    method: 'get',
    params
  })
}

export function getData(params) {
  return apiCall(realGetData, mockGetData, params)
}
```

## 🛠️ 开发工具

项目包含开发工具组件，提供可视化配置管理：

- **环境信息显示**
- **Mock模式切换**
- **配置信息打印**
- **缓存清理**

使用快捷键 `Ctrl+Shift+D` 切换开发工具面板。

## 📁 文件结构

```
src/
├── api/
│   ├── mock/           # Mock数据
│   ├── *.js           # API接口定义
├── utils/
│   ├── request.js     # HTTP请求封装
│   ├── apiManager.js  # API管理工具
├── settings.js        # 项目配置
└── components/
    └── DevTools.vue   # 开发工具组件
```

## 🔄 环境切换流程

### 开发 → 联调
1. 设置 `useMock: false`
2. 配置后端服务器代理地址
3. 启动后端服务
4. 重启前端开发服务器

### 联调 → 生产
1. 更新生产API地址
2. 构建生产版本
3. 部署到生产环境

## ⚠️ 注意事项

1. **代理配置**: 确保代理地址与后端服务器一致
2. **CORS问题**: 生产环境需要后端配置CORS
3. **环境变量**: 使用 `.env` 文件管理不同环境配置
4. **错误处理**: 确保Mock数据格式与真实API一致

## 🐛 常见问题

### Q: Mock切换后没有生效？
A: 检查浏览器缓存，尝试硬刷新或清除缓存

### Q: 代理配置不生效？
A: 确认后端服务器运行状态和端口号

### Q: 生产环境API调用失败？
A: 检查API地址、CORS配置和网络连接

## 📞 技术支持

如有问题，请联系开发团队或查看项目文档。
