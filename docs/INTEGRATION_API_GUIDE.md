# 集成管理API使用指南

## 📋 概述

集成管理模块采用混合模式：
- **左侧集成类型列表**：继续使用Mock数据（稳定的UI展示）
- **右侧集成管理功能**：使用真实API接口（基于 zero_duty_api 文档实现）

## 🔧 配置

### 环境变量配置

#### 开发环境 (.env.development)
```bash
# API配置 - 开发环境使用相对路径，通过Vite代理
# VITE_API_BASE_URL=  # 留空，使用相对路径避免CORS问题
VITE_API_TIMEOUT=30000
```

#### 生产环境 (.env.production)
```bash
# API配置
VITE_API_BASE_URL=system-rpc-svc.zeroduty-qa.svc.cluster.local:2002
VITE_API_TIMEOUT=10000
```

## 📁 文件结构

```
src/
├── api/
│   ├── integrationApi.js      # 真实API接口定义
│   ├── integrationAdapter.js  # 混合适配器（Mock + 真实API）
│   └── mock/
│       └── integration.js     # Mock数据（用于集成类型）
├── utils/
│   └── request.js             # 统一的HTTP请求工具
└── views/
    └── integrationManagement/
        └── index.vue          # 集成管理页面
```

## 🔄 混合模式说明

### 为什么使用混合模式？

1. **集成类型列表（Mock数据）**：
   - 集成类型相对稳定，不经常变化
   - Mock数据确保UI展示的一致性
   - 避免API服务器问题影响基础UI

2. **集成管理功能（真实API）**：
   - 集成的增删改查需要实时数据
   - 状态切换需要与后端同步
   - 支持真实的业务操作

### 混合模式实现

```javascript
// src/api/integrationAdapter.js
import * as realApi from './integrationApi'
import * as mockApi from './mock/integration'

// 集成类型使用Mock数据
export const getIntegrationTypes = mockApi.getIntegrationTypes

// 其他功能使用真实API
export const getIntegrations = (params) => realApi.getIntegrationList(params)
export const createIntegration = realApi.createIntegration
export const updateIntegration = realApi.updateIntegration
// ...
```

## 🌐 API接口

### 集成管理接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取集成类型列表 | GET | `/api/v1/integration-types` | 获取所有可用的集成类型（使用Mock数据） |
| 获取集成列表 | POST | `/api/v1/integrations/list` | 获取集成列表，参数通过请求体发送 |
| 创建集成 | POST | `/api/v1/integrations` | 创建新的集成 |
| 获取集成详情 | GET | `/api/v1/integrations/{id}` | 根据ID获取集成详情 |
| 更新集成 | PUT | `/api/v1/integrations/{id}` | 更新集成信息 |
| 删除集成 | DELETE | `/api/v1/integrations` | 批量删除集成，IDs通过请求体发送 |
| 获取集成密钥 | GET | `/api/v1/integrations/key` | 获取集成访问密钥 |

### 告警路由接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取告警路由列表 | GET | `/api/v1/alert-routes` | 获取告警路由列表 |
| 创建告警路由 | POST | `/api/v1/alert-routes` | 创建新的告警路由 |
| 获取告警路由详情 | GET | `/api/v1/alert-routes/{id}` | 获取告警路由详情 |
| 更新告警路由 | PUT | `/api/v1/alert-routes/{id}` | 更新告警路由 |
| 删除告警路由 | DELETE | `/api/v1/alert-routes` | 批量删除告警路由 |

## 💻 使用示例

### 在Vue组件中使用

```javascript
import {
  getIntegrationTypes,
  getIntegrations,
  createIntegration,
  toggleIntegrationStatus
} from '@/api/integrationAdapter'

export default {
  async mounted() {
    try {
      // 获取集成类型（使用Mock数据）
      const types = await getIntegrationTypes()
      console.log('集成类型:', types)

      // 获取集成列表（使用真实API，参数作为请求体发送）
      const integrations = await getIntegrations({
        page: 1,
        pageSize: 20,
        nameKeyword: 'prometheus',
        typeKeyword: 'monitoring'
      })
      console.log('集成列表:', integrations)

    } catch (error) {
      console.error('API调用失败:', error)
    }
  }
}
```

### POST请求体参数示例

获取集成列表使用POST请求，参数通过请求体发送：

```javascript
// POST /api/v1/integrations/list
// 请求头：
// Content-Type: application/json
// Accept: */*

// 请求体：
{
  "page": 1,
  "pageSize": 20,
  "name": "prometheus",     // 可选：按名称搜索
  "type": "monitoring"      // 可选：按类型搜索
}
```

### 原生fetch示例

```javascript
const myHeaders = new Headers();
myHeaders.append("Content-Type", "application/json");
myHeaders.append("Accept", "*/*");

const raw = JSON.stringify({
   "page": 1,
   "pageSize": 10,
   "name": "prometheus"
});

const requestOptions = {
   method: 'POST',
   headers: myHeaders,
   body: raw,
   redirect: 'follow'
};

// 开发环境使用相对路径，通过代理访问
fetch("/api/v1/integrations/list", requestOptions)
   .then(response => response.json())
   .then(result => console.log(result))
   .catch(error => console.log('error', error));
```

### axios配置示例

```javascript
// 使用POST请求发送body参数
import request from '@/utils/request'

const requestBody = {
  page: 1,
  pageSize: 10,
  name: "prometheus"
}

// 简单的POST请求
request.post('/api/v1/integrations/list', requestBody)
  .then(response => console.log(response))
  .catch(error => console.error(error))
```

### 直接使用API函数

```javascript
import * as integrationApi from '@/api/integrationApi'

// 创建集成
const newIntegration = await integrationApi.createIntegration({
  id: "019733a5-d252-735a-925a-4d8874766573",
  name: "Prometheus集成测试",
  kind: "public",
  type: "prometheus",
  status: true
})

// 更新集成
const updatedIntegration = await integrationApi.updateIntegration(
  "019733a5-d252-735a-925a-4d8874766573",
  {
    name: "Prometheus集成（更新）",
    type: "prometheus-zabbix",
    kind: "public",
    enabled: false
  }
)
```

## 🔍 调试

### 开发环境调试

在开发环境下，所有API请求都会在控制台打印日志：

```
🌐 API请求: GET /api/v1/integrations
✅ API响应: 200 /api/v1/integrations
```

如果请求失败，会显示错误信息：

```
❌ API请求错误: 500 /api/v1/integrations Network Error
```

### 网络问题排查

1. **CORS跨域问题**
   ```
   错误信息：Access to XMLHttpRequest at 'http://127.0.0.1:2002/api/v1/integrations'
   from origin 'http://localhost:9008' has been blocked by CORS policy
   ```

   **解决方案**：
   - 确保开发环境使用相对路径，通过Vite代理访问API
   - 检查 `.env.development` 文件中 `VITE_API_BASE_URL` 是否为空
   - 确认 `vite.config.js` 中的代理配置正确

2. **检查API服务器是否运行**
   ```bash
   curl http://localhost:2002/api/v1/integrations
   ```

3. **检查Vite代理配置**
   ```javascript
   // vite.config.js
   server: {
     proxy: {
       '/api': {
         target: "http://localhost:2002",
         changeOrigin: true,
         secure: false
       }
     }
   }
   ```

4. **检查网络连接**
   - 确保API服务器地址正确
   - 检查防火墙设置
   - 验证CORS配置

5. **检查认证**
   - 确保认证令牌有效
   - 检查请求头中的Authorization字段

## 🚀 部署

### 开发环境启动

```bash
# 启动API服务器（如果需要）
# 确保 http://localhost:2002 可访问

# 启动前端开发服务器
npm run serve
```

### 生产环境部署

```bash
# 构建生产版本
npm run build

# 确保生产环境API地址正确配置
# VITE_API_BASE_URL=system-rpc-svc.zeroduty-qa.svc.cluster.local:2002
```

## 📝 注意事项

1. **API地址配置**
   - 开发环境使用 `http://localhost:2002`
   - 生产环境使用 `system-rpc-svc.zeroduty-qa.svc.cluster.local:2002`

2. **错误处理**
   - 所有API调用都应该包含错误处理
   - 网络错误会自动重试（如果配置了重试机制）

3. **认证**
   - API请求会自动添加认证令牌
   - 令牌存储在localStorage中的'authToken'键

4. **数据格式**
   - 请求和响应都使用JSON格式
   - 日期时间使用ISO 8601格式

## 🔧 故障排除

### 常见问题

**Q: API请求返回404错误**
A: 检查API服务器是否运行，确认接口路径正确

**Q: 请求超时**
A: 检查网络连接，可以增加超时时间配置

**Q: 认证失败**
A: 检查localStorage中的authToken是否有效

**Q: CORS错误**
A: 确保API服务器配置了正确的CORS策略

这个简化的配置系统直接使用真实API，避免了复杂的适配器逻辑，更容易维护和调试。
