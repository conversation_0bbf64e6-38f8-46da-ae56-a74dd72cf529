# 配置文件生效顺序和优先级

## 🔄 加载顺序

```
1. .env 文件加载 (Vite启动时)
2. vite.config.js 执行 (构建配置)
3. src/settings.js 执行 (运行时配置)
4. 代码中直接使用 (最终调用)
```

## 📊 优先级关系

```
高优先级 ←→ 低优先级

代码直接使用 > src/settings.js > .env环境变量 > vite.config.js代理 > 默认值
```

## 🎯 具体场景分析

### 场景1: API Base URL 配置

#### 配置层级
```javascript
// 1. .env.development (最低优先级)
VITE_API_BASE_URL=http://localhost:3000

// 2. vite.config.js (代理配置)
proxy: {
  '/api': {
    target: "http://localhost:8080"
  }
}

// 3. src/settings.js (中等优先级)
api: {
  baseUrl: import.meta.env.VITE_API_BASE_URL || ''
}

// 4. 代码中直接使用 (最高优先级)
const service = axios.create({
  baseURL: Settings.default.api.baseUrl
})
```

#### 实际生效逻辑
```javascript
// src/settings.js 中的逻辑
baseUrl: import.meta.env.VITE_API_BASE_URL || (
  import.meta.env.MODE === 'production'
    ? 'https://your-production-api.com'  // 生产环境
    : ''  // 开发环境使用代理
)
```

### 场景2: 不同环境的配置生效

#### 开发环境 (development)
```
1. .env.development 加载
   VITE_API_BASE_URL= (空值)
   
2. src/settings.js 判断
   baseUrl: '' (空值，使用代理)
   
3. vite.config.js 代理生效
   /api -> http://localhost:8080
   
4. 最终请求路径
   /api/users -> http://localhost:8080/api/users
```

#### 生产环境 (production)
```
1. .env.production 加载
   VITE_API_BASE_URL=https://api.example.com
   
2. src/settings.js 判断
   baseUrl: 'https://api.example.com'
   
3. vite.config.js 代理不生效 (生产环境无代理)
   
4. 最终请求路径
   /api/users -> https://api.example.com/api/users
```

## 🔧 配置覆盖示例

### 示例1: 临时切换API地址

```javascript
// 方法1: 修改 .env.development
VITE_API_BASE_URL=http://test-server:8080

// 方法2: 修改 src/settings.js
api: {
  baseUrl: 'http://test-server:8080'  // 直接硬编码
}

// 方法3: 运行时动态修改
import * as Settings from '@/settings'
Settings.default.api.baseUrl = 'http://test-server:8080'
```

### 示例2: Mock模式切换

```javascript
// 优先级: 代码调用 > settings配置 > 环境变量

// 1. 环境变量 (.env)
VITE_USE_MOCK=false

// 2. settings配置
useMock: import.meta.env.VITE_USE_MOCK === 'true' || true

// 3. 代码调用 (最高优先级)
import { toggleMock } from '@/utils/apiManager'
toggleMock(false)  // 直接覆盖所有配置
```

## 📋 配置检查清单

### 开发环境检查
- [ ] `.env.development` 文件存在
- [ ] `vite.config.js` 代理配置正确
- [ ] `src/settings.js` 中 `useMock` 设置
- [ ] 后端服务器运行状态

### 生产环境检查
- [ ] `.env.production` 文件存在
- [ ] 生产API地址配置正确
- [ ] CORS配置正确
- [ ] 网络连接正常

## 🐛 常见问题排查

### Q1: 配置修改后不生效？
**排查步骤:**
1. 检查文件保存状态
2. 重启开发服务器 (`npm run serve`)
3. 清除浏览器缓存
4. 检查控制台错误信息

### Q2: 代理配置不工作？
**排查步骤:**
1. 确认后端服务器运行
2. 检查代理目标地址
3. 查看网络面板请求状态
4. 检查 `baseURL` 是否为空

### Q3: 环境变量不生效？
**排查步骤:**
1. 确认 `.env` 文件命名正确
2. 检查变量名前缀 `VITE_`
3. 重启开发服务器
4. 使用 `console.log(import.meta.env)` 调试

## 🎯 最佳实践

### 1. 环境变量命名
```bash
# ✅ 正确
VITE_API_BASE_URL=
VITE_USE_MOCK=true

# ❌ 错误 (缺少VITE_前缀)
API_BASE_URL=
USE_MOCK=true
```

### 2. 配置优先级设计
```javascript
// ✅ 推荐: 环境变量优先
baseUrl: import.meta.env.VITE_API_BASE_URL || defaultValue

// ❌ 不推荐: 硬编码优先
baseUrl: defaultValue || import.meta.env.VITE_API_BASE_URL
```

### 3. 开发调试
```javascript
// 添加调试信息
console.log('当前环境:', import.meta.env.MODE)
console.log('API地址:', Settings.default.api.baseUrl)
console.log('使用Mock:', Settings.default.useMock)
```

## 📝 配置模板

### .env.development
```bash
# 开发环境配置
VITE_API_BASE_URL=
VITE_USE_MOCK=true
VITE_API_TIMEOUT=30000
```

### .env.production
```bash
# 生产环境配置
VITE_API_BASE_URL=https://your-production-api.com
VITE_USE_MOCK=false
VITE_API_TIMEOUT=10000
```

这样的配置层级确保了灵活性和可维护性，同时提供了清晰的覆盖机制。
