# 多后端API服务配置指南

本文档介绍如何在ZeroDuty项目中使用多个后端API服务。

## 🏗️ 架构概览

```
前端应用 (Vue3 + Vite)
├── OnCall服务 (端口2002) - 值班管理
├── System服务 (端口2001) - 系统管理  
└── Message服务 (端口2003) - 消息通知
```

## 📋 服务说明

### OnCall服务 (端口2002)
- **功能**: 值班管理、轮换规则、覆盖设置
- **代理路径**: `/oncall-api`
- **API文件**: `src/api/oncallApi.js`

### System服务 (端口2001)  
- **功能**: 用户管理、权限控制、系统配置
- **代理路径**: `/system-api`
- **API文件**: `src/api/systemApi.js`

### Message服务 (端口2003)
- **功能**: 消息发送、通知渠道、模板管理
- **代理路径**: `/message-api`
- **API文件**: `src/api/messageApi.js`

## 🔧 开发环境配置

### 1. 启动后端服务
```bash
# 启动OnCall服务 (端口2002)
cd oncall-service && npm start

# 启动System服务 (端口2001)
cd system-service && npm start

# 启动Message服务 (端口2003)
cd message-service && npm start
```

### 2. 前端代理配置
Vite会自动将请求代理到对应的后端服务：
- `/oncall-api/*` → `http://localhost:2002/*`
- `/system-api/*` → `http://localhost:2001/*`
- `/message-api/*` → `http://localhost:2003/*`

## 💻 使用方法

### 方法一：直接使用服务实例

```javascript
import { oncallRequest, systemRequest, messageRequest } from '@/utils/multiRequest'

// OnCall服务调用
const schedules = await oncallRequest({
  url: '/api/v1/oncall/schedules',
  method: 'post',
  data: { spaceId: '123' }
})

// System服务调用
const users = await systemRequest({
  url: '/api/v1/system/users',
  method: 'post',
  data: { page: 1, pageSize: 20 }
})

// Message服务调用
const result = await messageRequest({
  url: '/api/v1/message/send',
  method: 'post',
  data: { type: 'email', content: 'Hello' }
})
```

### 方法二：使用封装的API函数

```javascript
import { getOncallSchedules } from '@/api/oncallApi'
import { getUserList } from '@/api/systemApi'
import { sendMessage } from '@/api/messageApi'

// 获取值班计划
const schedules = await getOncallSchedules({ spaceId: '123' })

// 获取用户列表
const users = await getUserList({ page: 1, pageSize: 20 })

// 发送消息
const result = await sendMessage({
  type: 'email',
  recipients: ['<EMAIL>'],
  subject: '告警通知',
  content: '系统检测到异常'
})
```

### 方法三：动态选择服务

```javascript
import { serviceApiCall, SERVICE_TYPES } from '@/utils/multiRequest'

// 动态调用不同服务
const oncallData = await serviceApiCall(SERVICE_TYPES.ONCALL, {
  url: '/api/v1/oncall/schedules',
  method: 'get'
})

const systemData = await serviceApiCall(SERVICE_TYPES.SYSTEM, {
  url: '/api/v1/system/users',
  method: 'get'
})
```

## 🌍 生产环境配置

### 1. 环境变量设置
```bash
# .env.production
VITE_ONCALL_API_BASE_URL=https://oncall-api.zeroduty.com
VITE_SYSTEM_API_BASE_URL=https://system-api.zeroduty.com
VITE_MESSAGE_API_BASE_URL=https://message-api.zeroduty.com
```

### 2. Nginx配置
使用提供的`nginx.conf`配置文件，包含：
- SSL/TLS配置
- 多服务代理
- 负载均衡
- 健康检查
- 安全头设置

### 3. 部署步骤
```bash
# 1. 构建前端应用
npm run build

# 2. 复制构建文件到Nginx目录
cp -r dist/* /usr/share/nginx/html/zeroduty/

# 3. 更新Nginx配置
cp nginx.conf /etc/nginx/sites-available/zeroduty
ln -s /etc/nginx/sites-available/zeroduty /etc/nginx/sites-enabled/

# 4. 重启Nginx
nginx -t && systemctl reload nginx
```

## 🔍 监控和调试

### 1. 开发环境调试
开发环境下会在控制台输出详细的请求日志：
```
🌐 [ONCALL] API请求: POST /oncall-api/api/v1/oncall/schedules
✅ [ONCALL] API响应: 200 /oncall-api/api/v1/oncall/schedules
```

### 2. 健康检查
```javascript
import { checkServiceHealth } from '@/utils/multiRequest'

// 检查服务健康状态
const isOncallHealthy = await checkServiceHealth('oncall')
const isSystemHealthy = await checkServiceHealth('system')
const isMessageHealthy = await checkServiceHealth('message')
```

### 3. 错误处理
```javascript
try {
  const data = await getOncallSchedules(params)
} catch (error) {
  if (error.message.includes('oncall服务连接失败')) {
    // 处理OnCall服务不可用的情况
    console.log('OnCall服务暂时不可用，使用缓存数据')
  }
}
```

## 🛠️ 最佳实践

### 1. 错误处理
- 为每个服务调用添加适当的错误处理
- 实现服务降级和重试机制
- 提供用户友好的错误提示

### 2. 性能优化
- 合理使用请求缓存
- 避免不必要的并发请求
- 实现请求去重

### 3. 安全考虑
- 在生产环境使用HTTPS
- 配置适当的CORS策略
- 添加请求频率限制

### 4. 监控告警
- 监控各服务的响应时间
- 设置服务可用性告警
- 记录和分析API调用日志

## 🔧 故障排除

### 常见问题

1. **代理请求失败**
   - 检查后端服务是否启动
   - 确认端口配置是否正确
   - 查看Vite代理配置

2. **生产环境API调用失败**
   - 检查环境变量配置
   - 确认Nginx代理配置
   - 查看网络连接状态

3. **CORS错误**
   - 确认后端服务CORS配置
   - 检查Nginx代理头设置
   - 验证域名配置

### 调试命令
```bash
# 检查服务端口占用
netstat -tlnp | grep :200[1-3]

# 测试API连接
curl -X GET http://localhost:2002/health
curl -X GET http://localhost:2001/health  
curl -X GET http://localhost:2003/health

# 查看Nginx日志
tail -f /var/log/nginx/zeroduty_access.log
tail -f /var/log/nginx/zeroduty_error.log
```

## 📚 相关文档

- [API接口文档](./API_DOCUMENTATION.md)
- [部署指南](./DEPLOYMENT_GUIDE.md)
- [监控配置](./MONITORING_SETUP.md)
- [安全配置](./SECURITY_GUIDE.md)
