{"name": "sub-system-demo-ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"serve": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@icon-park/vue-next": "^1.4.2", "axios": "^1.6.7", "cnchar": "^3.2.5", "element-plus": "^2.5.6", "highlight.js": "^11.11.1", "js-cookie": "^3.0.5", "marked": "^15.0.8", "monaco-editor": "^0.52.2", "pinia": "^2.1.7", "sass": "^1.69.6", "tiny-emitter": "^2.1.0", "vite-plugin-qiankun": "^1.0.15", "vue": "^3.3.11", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "unplugin-auto-import": "^0.17.3", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.4", "vite-plugin-md": "^0.20.4"}}